package scrbg.meplat.tende.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import scrbg.meplat.tende.entity.TenderPublicityComplaint;
import scrbg.meplat.tende.mapper.TenderPublicityComplaintMapper;
import scrbg.meplat.tende.service.ITenderPublicityComplaintService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;

/**
 * @描述：物资供应招标_招标结果公示_投诉受理 服务类
 * @作者: demo
 * @日期: 2023-02-15
 */
@Service
public class TenderPublicityComplaintServiceImpl extends ServiceImpl<TenderPublicityComplaintMapper, TenderPublicityComplaint> implements ITenderPublicityComplaintService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper queryWrapper) {
        IPage<TenderPublicityComplaint> page = this.page(
        new Query<TenderPublicityComplaint>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(TenderPublicityComplaint tenderPublicityComplaint) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(tenderPublicityComplaint);
    }

    @Override
    public void update(TenderPublicityComplaint tenderPublicityComplaint) {
        super.updateById(tenderPublicityComplaint);
    }


    @Override
    public TenderPublicityComplaint getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

}
