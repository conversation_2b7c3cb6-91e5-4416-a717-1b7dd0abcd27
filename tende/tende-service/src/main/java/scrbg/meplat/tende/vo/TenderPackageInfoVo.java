package scrbg.meplat.tende.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.tende.entity.TenderDtl;
import scrbg.meplat.tende.entity.TenderPackage;
import scrbg.meplat.tende.entity.TenderPackageSubcontractor;

import java.io.Serializable;
import java.util.List;
@Data
public class TenderPackageInfoVo extends TenderPackage   implements Serializable {
    @ApiModelProperty(value = "分包清单集合")
    List<TenderDtl> tenderDtls;
    @ApiModelProperty(value = "分包商集合")
    List<TenderPackageSubcontractor> tenderPackageSubcontractors;
}
