package scrbg.meplat.tende.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.stereotype.Service;
import scrbg.meplat.tende.entity.TenderResultCandidate;
import scrbg.meplat.tende.mapper.TenderResultCandidateMapper;
import scrbg.meplat.tende.service.TenderResultCandidateService;

/**
 * @描述：招标_登记_中标候选人 服务类
 * @作者: sund
 * @日期: 2023-02-03
 */
@Service
public class TenderResultCandidateServiceImpl extends ServiceImpl<TenderResultCandidateMapper, TenderResultCandidate> implements TenderResultCandidateService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<TenderResultCandidate> queryWrapper) {
        IPage<TenderResultCandidate> page = this.page(
        new Query<TenderResultCandidate>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(TenderResultCandidate tenderResultCandidate) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(tenderResultCandidate);
    }

    @Override
    public void update(TenderResultCandidate tenderResultCandidate) {
        super.updateById(tenderResultCandidate);
    }


    @Override
    public TenderResultCandidate getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

}
