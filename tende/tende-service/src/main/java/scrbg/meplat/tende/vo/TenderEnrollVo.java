package scrbg.meplat.tende.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.tende.entity.TenderEnroll;

import java.util.Date;
@Data
public class TenderEnrollVo  extends TenderEnroll {
    @ApiModelProperty(value = "发布日期")
    private Date releaseDate;
    @ApiModelProperty(value = "招标方式")
    private String tendderForm;
    @ApiModelProperty(value = "招标单位")
    private String applyOrgName;


}
