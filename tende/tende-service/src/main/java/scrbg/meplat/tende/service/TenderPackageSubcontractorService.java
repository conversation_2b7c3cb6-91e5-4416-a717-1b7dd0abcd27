package scrbg.meplat.tende.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.tende.entity.TenderPackageSubcontractor;

/**
 * @描述：招标-包件分包商 服务类
 * @作者: sund
 * @日期: 2023-02-03
 */
public interface TenderPackageSubcontractorService extends IService<TenderPackageSubcontractor> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<TenderPackageSubcontractor> queryWrapper);

        void create(TenderPackageSubcontractor tenderPackageSubcontractor);

        void update(TenderPackageSubcontractor tenderPackageSubcontractor);

        TenderPackageSubcontractor getById(String id);

        void delete(String id);

        /**
         *
         * @param tenderApplyId 招标id
         * @param packageId  分包id
         * @return   分包商
         */
    TenderPackageSubcontractor getUnitCode(String tenderApplyId, String packageId);
}
