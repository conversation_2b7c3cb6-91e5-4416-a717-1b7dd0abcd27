package scrbg.meplat.tende.service;

import java.util.Map;

public interface RabbitmqMysqlServer {
    void listenMallTenderQueue1(Map<String, Object> innerMap);

    void listenMallTenderResultQueue1(Map<String, Object> innerMap);

    void listenMallTenderPublicityQueue1(Map<String, Object> innerMap);

    void listenMallTenderChangQueue1(Map<String, Object> innerMap);

    void listenMallTenderEnroll(Map<String, Object> jsonMap);

    void listenMallTenderNotice(Map<String, Object> jsonMap);
}
