package scrbg.meplat.tende.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import scrbg.meplat.tende.entity.TenderNotice;
import scrbg.meplat.tende.mapper.TenderNoticeMapper;
import scrbg.meplat.tende.service.TenderNoticeService;

import java.util.Date;
import java.util.Map;

/**
 * @描述：招标-中标通知书 服务类
 * @作者: sund
 * @日期: 2023-02-03
 */
@Service
public class TenderNoticeServiceImpl extends ServiceImpl<TenderNoticeMapper, TenderNotice> implements TenderNoticeService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<TenderNotice> queryWrapper) {
        IPage<TenderNotice> page = this.page(
        new Query<TenderNotice>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(TenderNotice tenderNotice) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(tenderNotice);
    }

    @Override
    public void update(TenderNotice tenderNotice) {
        super.updateById(tenderNotice);
    }


    @Override
    public TenderNotice getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public PageUtils queryPageByUnitCode(JSONObject jsonObject, LambdaQueryWrapper<TenderNotice> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String unitCode = (String) innerMap.get("unitCode");
        String tenderForm = (String) innerMap.get("tenderForm");
        String packageName = (String) innerMap.get("packageName");
        String tenderOrgName = (String) innerMap.get("tenderOrgName");
        Date endDate = (Date) innerMap.get("endDate");
        Date startDate = (Date) innerMap.get("startDate");
        String projectName = (String)innerMap.get("projectName");
        String keywords = (String) innerMap.get("keywords");
        Integer mallConfig = (Integer) innerMap.get("mallConfig");
        if (unitCode!=null){
            queryWrapper.eq(TenderNotice::getProjectName,unitCode);
        }

        if (projectName!=null){
            queryWrapper.eq(TenderNotice::getProjectName,projectName);
        }
        if (tenderOrgName!=null){
            queryWrapper.eq(TenderNotice::getTenderOrgName,tenderOrgName);
        }
        if (packageName!=null){
            queryWrapper.eq(TenderNotice::getPackageName,packageName);
        }
        if (tenderForm!=null){
            queryWrapper.eq(TenderNotice::getTenderForm,tenderForm);
        }
        if (startDate!=null){
            queryWrapper.gt(TenderNotice::getReleaseDate,startDate);
        }if (endDate!=null){
            queryWrapper.lt(TenderNotice::getReleaseDate,endDate);
        }
        if (!StringUtils.isEmpty(keywords)) {
            queryWrapper.and(i->i.like(TenderNotice::getProjectName,projectName)
                    .or().like(TenderNotice::getTenderOrgName,keywords)
                    .or().like(TenderNotice::getTenderApplyNo,keywords));
        }
        queryWrapper.eq(TenderNotice::getIsBid,1);
        IPage<TenderNotice> page = this.page(
                new Query<TenderNotice>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);

    }
}
