package scrbg.meplat.tende.entity;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.tende.entity.parent.BaseEntity;

/**
 * @描述：招标_登记_中标候选人
 * @作者: demo
 * @日期: 2023-02-10
 */
@ApiModel(value="招标_登记_中标候选人")
@Data
@TableName("tender_result_candidate")
public class TenderResultCandidate  extends BaseEntity implements Serializable  {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "记录ID")

        private String recordId;

    @ApiModelProperty(value = "包件id")

        private String packageId;

    @ApiModelProperty(value = "包件编号")

        private String packageNo;

    @ApiModelProperty(value = "包件名称")

        private String packageName;

    @ApiModelProperty(value = "是否流标")

        private Integer isFail;

    @ApiModelProperty(value = "第一备选单位")

        private String firstCandidate;

    @ApiModelProperty(value = "第一备选单位id")

        private String firstCandidateId;

    @ApiModelProperty(value = "第一备选单位投标不含税金额")

        private BigDecimal firstAmount;

    @ApiModelProperty(value = "第一备选单位综合得分")

        private BigDecimal firstGrade;

    @ApiModelProperty(value = "第二备选单位_id")

        private String twoCandidateId;

    @ApiModelProperty(value = "第二备选单位")

        private String twoCandidate;

    @ApiModelProperty(value = "第二备选单位投标不含税金额")

        private BigDecimal twoAmount;

    @ApiModelProperty(value = "第二备选单位综合得分")

        private BigDecimal twoGrade;

    @ApiModelProperty(value = "中标单位")

        private String threeCandidate;

    @ApiModelProperty(value = "中标单位id")

        private String threeCandidateId;

    @ApiModelProperty(value = "中标单位投标不含税金额")

        private BigDecimal threeAmount;

    @ApiModelProperty(value = "中标单位综合得分")

        private BigDecimal threeGrade;

    @ApiModelProperty(value = "单据ID")

        private String billId;

    @ApiModelProperty(value = "创建时间")

        private Date gmtCreate;

    @ApiModelProperty(value = "上次修改时间")

        private Date gmtModified;

    @ApiModelProperty(value = "下级引用机构id")

        private String subordinateReferenceOrgId;

    @ApiModelProperty(value = "下级引用机构名称")

        private String subordinateReferenceOrgName;


}
