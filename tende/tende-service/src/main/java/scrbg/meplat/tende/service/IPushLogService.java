package scrbg.meplat.tende.service;

import scrbg.meplat.tende.entity.PushLog;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.tende.entity.PushLog;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

/**
 * @描述： 服务类
 * @作者: demo
 * @日期: 2023-03-03
 */
public interface IPushLogService extends IService<PushLog> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper queryWrapper);

        void create(PushLog pushLog);

        void update(PushLog pushLog);

        PushLog getById(String id);

        void delete(String id);
}
