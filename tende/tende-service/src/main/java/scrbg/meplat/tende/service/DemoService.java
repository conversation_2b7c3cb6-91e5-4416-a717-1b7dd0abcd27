package scrbg.meplat.tende.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import scrbg.meplat.tende.config.ConfigFeign;

@Service
public class DemoService {

    /**
     * Demo实例
     *
     * @return
     */
    public Demo test() {
        Demo e = new Demo();
        e.setId(IdWorker.getIdStr());
        e.setName("我是Demo实例");
        return e;
    }


    @Autowired
    private ConfigFeign configFeign;


}
