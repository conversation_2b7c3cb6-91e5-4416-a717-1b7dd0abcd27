package scrbg.meplat.tende.entity;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * @描述：物资供应招标_公示_候选人
 * @作者: demo
 * @日期: 2023-02-15
 */
@ApiModel(value="物资供应招标_公示_候选人")
@Data
@TableName("tender_publicity_candidate")
public class TenderPublicityCandidate implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "记录ID")

        private String recordId;

    @ApiModelProperty(value = "单据ID")

        private String billId;

    @ApiModelProperty(value = "包件ID")

        private String packageId;

    @ApiModelProperty(value = "包件编号")

        private String packageNo;

    @ApiModelProperty(value = "包件名称")

        private String packageName;

    @ApiModelProperty(value = "第一候选人")

        private String firstCandidate;

    @ApiModelProperty(value = "第一候选人id")

        private String firstCandidateId;

    @ApiModelProperty(value = "第二候选人")

        private String twoCandidate;

    @ApiModelProperty(value = "第二候选人id")

        private String twoCandidateId;

    @ApiModelProperty(value = "第三候选人")

        private String threeCandidate;

    @ApiModelProperty(value = "第三候选人id")

        private String threeCandidateId;

    @ApiModelProperty(value = "创建时间")

        private Date gmtCreate;

    @ApiModelProperty(value = "上次修改时间")

        private Date gmtModified;


}
