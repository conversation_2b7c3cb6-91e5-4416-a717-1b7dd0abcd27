package scrbg.meplat.tende.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.models.auth.In;
import org.springframework.util.StringUtils;
import scrbg.meplat.tende.entity.Tender;
import scrbg.meplat.tende.entity.TenderMessage;
import scrbg.meplat.tende.mapper.TenderMessageMapper;
import scrbg.meplat.tende.service.ITenderMessageService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import scrbg.meplat.tende.util.ThreadLocalUtil;
import scrbg.meplat.tende.util.UserLogin;

import javax.xml.crypto.Data;
import java.util.List;
import java.util.Map;

/**
 * @描述：招标消息表 服务类
 * @作者: demo
 * @日期: 2023-02-21
 */
@Service
public class TenderMessageServiceImpl extends ServiceImpl<TenderMessageMapper, TenderMessage> implements ITenderMessageService {
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<TenderMessage> queryWrapper) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String unitCode = user.getSocialCreditCode();
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String title = (String) innerMap.get("title");
        String keywords = (String) innerMap.get("keywords");
        Integer isRead = (Integer) innerMap.get("isRead");
        Integer mallType = (Integer) innerMap.get("mallType");
        Integer messageType = (Integer) innerMap.get("messageType");
        String startTime = (String) innerMap.get("startTime");
        String endTime = (String) innerMap.get("endTime");
        if (startTime != null) {
            queryWrapper.gt(TenderMessage::getSendTime, startTime);
        }
        if (endTime != null) {
            queryWrapper.lt(TenderMessage::getSendTime, endTime);
        }
        if (!StringUtils.isEmpty(keywords)) {
            queryWrapper.and(i -> i.like(TenderMessage::getTitle, keywords));
        }
        if (unitCode != null) {
            queryWrapper.eq(TenderMessage::getUnitCode, unitCode);
        }
        if (title != null) {
            queryWrapper.like(TenderMessage::getTitle, title);
        }
        if (isRead != null) {
            queryWrapper.eq(TenderMessage::getIsRead, isRead);
        }
        if (mallType != null) {
            queryWrapper.eq(TenderMessage::getMallType, mallType);
        }
        if (messageType != null) {
            queryWrapper.eq(TenderMessage::getMessageType, messageType);
        }
        queryWrapper.eq(TenderMessage::getIsDelete, 0);
        IPage<TenderMessage> page = this.page(
                new Query<TenderMessage>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(TenderMessage tenderMessage) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(tenderMessage);
    }

    @Override
    public void update(TenderMessage tenderMessage) {
        super.updateById(tenderMessage);
    }


    @Override
    public TenderMessage getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        TenderMessage byId = getById(id);
        byId.setIsDelete(1);
        update(byId);
    }

    @Override
    public void updateIsReadById(String id) {
        TenderMessage byId = getById(id);
        byId.setIsRead(1);
        update(byId);
    }

    @Override
    public List<TenderMessage> getMsgNumber(JSONObject jsonObject) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        LambdaQueryWrapper<TenderMessage> queryWrapper = new LambdaQueryWrapper<>();
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String unitCode = user.getSocialCreditCode();
        Integer mallType = (Integer) innerMap.get("mallType");
        queryWrapper.eq(TenderMessage::getUnitCode, unitCode);
        queryWrapper.eq(TenderMessage::getMallType, mallType);
        List<TenderMessage> list = list(queryWrapper);
        return list;
    }

    @Override
    public TenderMessage getData(JSONObject jsonObject) {
        LambdaQueryWrapper<TenderMessage> wrapper = new LambdaQueryWrapper<>();
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String notice = (String) innerMap.get("notice");
        Integer messageType = (Integer) innerMap.get("messageType");
        String keyword = (String) innerMap.get("keyword");
        if (messageType != null) {
            if (messageType == 0) {
                wrapper.eq(TenderMessage::getMessageType, messageType)
                        .eq(TenderMessage::getNotice,notice)
                        .eq(TenderMessage::getKeyword,keyword);
            }
        }
        TenderMessage byId = getOne(wrapper);
        return byId;
    }
}
