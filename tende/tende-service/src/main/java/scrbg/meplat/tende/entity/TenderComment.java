package scrbg.meplat.tende.entity;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.tende.entity.parent.BaseEntity;

/**
 * @描述：分包公司评论表（澄清提问）
 * @作者: demo
 * @日期: 2023-02-10
 */
@ApiModel(value="分包公司评论表（澄清提问）")
@Data
@TableName("tender_comment")
public class TenderComment  extends BaseEntity implements Serializable  {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "评论")

        private Long commentId;

    @ApiModelProperty(value = "评论编号")

        private String commentNo;

    @ApiModelProperty(value = "标书id")

        private String billId;

    @ApiModelProperty(value = "分包Id")

        private String packageId;

    @ApiModelProperty(value = "标题")

        private String title;

    @ApiModelProperty(value = "附件")

        private String accessory;


}
