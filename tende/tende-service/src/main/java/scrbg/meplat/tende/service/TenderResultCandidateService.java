package scrbg.meplat.tende.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.tende.entity.TenderResultCandidate;

/**
 * @描述：招标_登记_中标候选人 服务类
 * @作者: sund
 * @日期: 2023-02-03
 */
public interface TenderResultCandidateService extends IService<TenderResultCandidate> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<TenderResultCandidate> queryWrapper);

        void create(TenderResultCandidate tenderResultCandidate);

        void update(TenderResultCandidate tenderResultCandidate);

        TenderResultCandidate getById(String id);

        void delete(String id);
}
