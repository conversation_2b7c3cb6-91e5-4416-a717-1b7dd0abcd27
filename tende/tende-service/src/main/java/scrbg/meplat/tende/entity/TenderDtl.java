package scrbg.meplat.tende.entity;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.tende.entity.parent.BaseEntity;

/**
 * @描述：招标_清单明细
 * @作者: demo
 * @日期: 2023-02-10
 */
@ApiModel(value="招标_清单明细")
@Data
@TableName("tender_dtl")
public class TenderDtl  extends BaseEntity implements Serializable   {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "记录ID")

        private String recordId;

    @ApiModelProperty(value = "单据ID")

        private String billId;

    @ApiModelProperty(value = "包件ID")

        private String packageId;

    @ApiModelProperty(value = "物资/设备名称")

        private String name;

    @ApiModelProperty(value = "物资/设备类别")

        private String type;

    @ApiModelProperty(value = "计量单位")

        private String unit;

    @ApiModelProperty(value = "规格型号")

        private String specs;

    @ApiModelProperty(value = "数量")

        private BigDecimal num;

    @ApiModelProperty(value = "		申报限价")

        private BigDecimal limitPrice;

    @ApiModelProperty(value = "运输申报限价")

        private BigDecimal freightPrice;

    @ApiModelProperty(value = "综合单价")

        private BigDecimal unitPrice;

    @ApiModelProperty(value = "总金额")

        private BigDecimal amount;

    @ApiModelProperty(value = "创建日期")

        private Date gmtCreate;

    @ApiModelProperty(value = "上次修改时间")

        private Date gmtModified;

    @ApiModelProperty(value = "预计开始时间")

        private Date estimatedStartTime;

    @ApiModelProperty(value = "预计结束时间")

        private Date estimatedEndTime;

    @ApiModelProperty(value = "预计使用时间")

        private Integer estimatedServiceTime;

    @ApiModelProperty(value = "物资/设备类别Id")

        private String materialTypeId;

    @ApiModelProperty(value = "物资/设备名称Id")

        private String materialNameId;

    @ApiModelProperty(value = "物资/设备id")

        private String materialId;

    @ApiModelProperty(value = "基础库id")

        private String baseLibraryId;

    @ApiModelProperty(value = "数据类别,数据类别,1总计划,2零星采购计划，3月计划，4周才采购计划，5临时需用计划，6，集采汇总，7基础库")

        private Integer dataType;

    @ApiModelProperty(value = "计时单位")

        private String timingUnit;

    @ApiModelProperty(value = "运费税额")

        private BigDecimal freightTax;

    @ApiModelProperty(value = "清单类型：1物资 2设备")

        private Integer recordType;

    @ApiModelProperty(value = "报废申请编号")

        private String scrapApplicationNo;

    @ApiModelProperty(value = "处置方式")

        private String sisposalMethod;

    @ApiModelProperty(value = "处置金额")

        private BigDecimal disposalAmount;


    @ApiModelProperty(value = "材质")

        private String texture;
    @ApiModelProperty(value = "招标补遗")

    private String changeReason;



}
