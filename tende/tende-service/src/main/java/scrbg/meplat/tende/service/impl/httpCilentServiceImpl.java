package scrbg.meplat.tende.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import scrbg.meplat.tende.service.httpCilentService;
import scrbg.meplat.tende.util.HttpClientUtil;

@Service
public class httpCilentServiceImpl implements httpCilentService {
    @Value("")

    @Override
    public boolean getIsSupplier(String socialCrede, Integer mallType) {


        return false;
    }
}
