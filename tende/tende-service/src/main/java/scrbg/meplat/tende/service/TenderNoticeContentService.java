package scrbg.meplat.tende.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.tende.entity.TenderNoticeContent;

/**
 * @描述：招标_中标通知书_通知内容 服务类
 * @作者: sund
 * @日期: 2023-02-03
 */
public interface TenderNoticeContentService extends IService<TenderNoticeContent> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<TenderNoticeContent> queryWrapper);

        void create(TenderNoticeContent tenderNoticeContent);

        void update(TenderNoticeContent tenderNoticeContent);

        TenderNoticeContent getById(String id);

        void delete(String id);
}
