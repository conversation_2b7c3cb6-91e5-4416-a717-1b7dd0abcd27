package scrbg.meplat.tende.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import scrbg.meplat.tende.entity.Tender;
import scrbg.meplat.tende.entity.TenderDtl;
import scrbg.meplat.tende.service.TenderDtlService;
import scrbg.meplat.tende.service.TenderService;
import scrbg.meplat.tende.util.ThreadLocalUtil;
import scrbg.meplat.tende.util.UserLogin;
import scrbg.meplat.tende.vo.TenderPackageInfoVo;
import scrbg.meplat.tende.vo.TenderPackageVo;
import scrbg.meplat.tende.entity.TenderPackage;
import scrbg.meplat.tende.mapper.TenderPackageMapper;
import scrbg.meplat.tende.service.TenderPackageService;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @描述：招标-包件 服务类
 * @作者: sund
 * @日期: 2023-02-03
 */
@Service
public class TenderPackageServiceImpl extends ServiceImpl<TenderPackageMapper, TenderPackage> implements TenderPackageService {
    @Autowired
    private  TenderService tenderService;
    @Autowired
    private  TenderPackageService tenderPackageService;
    @Autowired
    private TenderDtlService tenderDtlService;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<TenderPackage> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String billId = (String) innerMap.get("billId");
        String keywords = (String) innerMap.get("keywords");
        if (billId!=null){
            queryWrapper.eq(TenderPackage::getBillId,billId);
        }
        if(StringUtils.isNotBlank(keywords)){
            queryWrapper.and((t) -> {
                t.like(TenderPackage::getItemNo,keywords)
                        .or()
                        .like(TenderPackage::getName,keywords);

            });
        }
        IPage<TenderPackage> page = this.page(
        new Query<TenderPackage>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(TenderPackage tenderPackage) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(tenderPackage);
    }

    @Override
    public void update(TenderPackage tenderPackage) {
        super.updateById(tenderPackage);
    }


    @Override
    public TenderPackage getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public List<TenderPackage> listByBillId(String id) {
        LambdaQueryWrapper<TenderPackage> q = new LambdaQueryWrapper<>();
        q.eq(TenderPackage::getBillId,id);
        q.select(TenderPackage::getName,TenderPackage::getDtlId,TenderPackage::getItemNo,TenderPackage::getName,TenderPackage::getPriceLimit);
        List<TenderPackage> list = tenderPackageService.list(q);
        return list;

    }

    @Override
    public List<TenderPackageVo> TeBerSuByBillId(String id) {
        QueryWrapper<TenderPackageVo> q = new QueryWrapper<>();
        q.eq("p.bill_id",id);
        List<TenderPackageVo> list =baseMapper.tenderPackageVoListbyBillId(q);
        return list;
    }

    @Override
    public PageUtils winBidPackageList(JSONObject jsonObject, QueryWrapper<TenderPackage> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String unitCode = user.getSocialCreditCode();
        String billId = (String) innerMap.get("billId");
        queryWrapper.eq("tn.unit_code",unitCode)
                .eq("tn.tender_apply_id",billId);
        IPage<Tender> pages = new Query<Tender>().getPage(jsonObject);
        List<TenderPackage> list =baseMapper.winBidPackageList(pages,queryWrapper);
        PageUtils pageUtils = new PageUtils(pages);
        pageUtils.setList(list);
        return pageUtils;

    }

    @Override
    public List<TenderPackageVo> findByBillIdAll(String billId) {
//        QueryWrapper<TenderPackageVo> q = new QueryWrapper<>();
//        q.eq("tp.bill_id",billId);
//        List<TenderPackageVo> list =baseMapper.tenderDtlListbyBillId(q);
//        return list;
        List<TenderPackageVo> list = new ArrayList();
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        List<String> orgIds = user.getOrgIds();
        LambdaQueryWrapper<Tender> tenderw = new LambdaQueryWrapper<>();
        if (orgIds!=null&&orgIds.size()>0){
            tenderw.in(Tender::getOrgId,orgIds);
        }
        tenderw.eq(Tender::getBillId, billId);
        Tender one = tenderService.getOne(tenderw);
        if (one==null){
             return list;
        }
        LambdaQueryWrapper<TenderPackage> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(TenderPackage::getBillId,billId);
        List<TenderPackage> tenderPackageList = list(wrapper);
        if (tenderPackageList!=null){
            for (TenderPackage tenderPackage : tenderPackageList) {
                TenderPackageVo tenderPackageVo = new TenderPackageVo();
                tenderPackageVo.setItemNo(tenderPackage.getItemNo());
                tenderPackageVo.setName(tenderPackage.getName());
                LambdaQueryWrapper<TenderDtl> dtlWrapper = new LambdaQueryWrapper<>();
                dtlWrapper.eq(TenderDtl::getBillId,billId);
                dtlWrapper.eq(TenderDtl::getPackageId,tenderPackage.getDtlId());
                List<TenderDtl> dtlList = tenderDtlService.list(dtlWrapper);
                tenderPackageVo.setTenderDtlList(dtlList);
                list.add(tenderPackageVo);
            }
        }
        return list;

    }
}
