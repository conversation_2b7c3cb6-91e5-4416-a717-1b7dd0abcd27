package scrbg.meplat.tende.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import scrbg.meplat.tende.entity.Tender;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import scrbg.meplat.tende.vo.TenderInfoVo;
import scrbg.meplat.tende.vo.TenderResultVo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【tender(招标)】的数据库操作Mapper
* @createDate 2023-02-08 09:31:11
* @Entity scrbg.meplat.mall.entity.Tender
*/
@Mapper
@Repository
public interface TenderMapper extends BaseMapper<Tender> {

    TenderResultVo getTenderNum(@Param("ew") LambdaQueryWrapper<Tender> queryWrapper);

    List<Tender> findByCondition(IPage<Tender> pages, @Param("ew") QueryWrapper<Tender> queryWrapper);

    List<Tender> winBidList(IPage<Tender> pages, @Param("ew") QueryWrapper<Tender> queryWrapper);

    Tender getByBillNo(@Param("billNo") String billNo);

}




