package scrbg.meplat.tende.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import scrbg.meplat.tende.entity.Tender;
import scrbg.meplat.tende.entity.TenderDtl;
import scrbg.meplat.tende.entity.TenderEnroll;
import scrbg.meplat.tende.entity.TenderResult;
import scrbg.meplat.tende.mapper.TenderMapper;
import scrbg.meplat.tende.service.TenderDtlService;
import scrbg.meplat.tende.service.TenderService;
import scrbg.meplat.tende.util.ThreadLocalUtil;
import scrbg.meplat.tende.util.UserLogin;
import scrbg.meplat.tende.vo.TenderEnrollVo;
import scrbg.meplat.tende.vo.TenderInfoVo;
import scrbg.meplat.tende.vo.TenderResultVo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @描述：租赁招标 服务类
 * @作者: sund
 * @日期: 2023-02-03
 */
@Service
public class TenderServiceImpl extends ServiceImpl<TenderMapper, Tender> implements TenderService{
@Autowired
    TenderDtlService tenderDtlService;
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<Tender> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        Integer tenderState = (Integer) innerMap.get("tenderState");
        Integer releaseDate = (Integer)innerMap.get("releaseDate");
        String tenderUser = (String)innerMap.get("tenderUser");
        String tenderForm = innerMap.get("tenderForm").toString();
        Integer tendeAmount = (Integer) innerMap.get("tendeAmount");
        Integer mallConfig = (Integer) innerMap.get("mallConfig");
        Integer tenderType = (Integer) innerMap.get("tenderType");
        String keyword = (String) innerMap.get("keyword");
        String startDate = (String) innerMap.get("startDate");
        String endDate = (String) innerMap.get("endDate");
        String tenderName = (String) innerMap.get("tenderName");
        queryWrapper.eq(Tender::getIsShow,1);
        if (tenderUser!=null){
            queryWrapper.like(Tender::getTenderUser,tenderUser);
        }
        if (tenderForm!=null){
            queryWrapper.eq(Tender::getTenderForm,tenderForm);
        }
        if (tenderName!=null){
            queryWrapper.like(Tender::getTenderName,tenderName);
        }
        if (!StringUtils.isEmpty(keyword)) {
            queryWrapper.and(i->i.like(Tender::getTenderUser,keyword)
                    .or().like(Tender::getTenderName,keyword));
        }
        if (tenderState!=null){
            if (tenderState==3){
                queryWrapper.lt(Tender::getTenderState,3);
            }
            if (tenderState==5){
                queryWrapper.between(Tender::getTenderState,3,5);
            }
            if (tenderState==6){
                queryWrapper.eq(Tender::getTenderState,6);
            }
            if (tenderState==9){
                queryWrapper.between(Tender::getTenderState,6,9);
            }

        }
        queryWrapper.eq(Tender::getReleaseState,1);
        if (tenderType!=null){
            queryWrapper.eq(Tender::getTenderType,tenderType);
        }
        if (startDate!=null){
            queryWrapper.gt(Tender::getReleaseDate,startDate);
        }if (endDate!=null){
            queryWrapper.lt(Tender::getReleaseDate,endDate);
        }
       if (mallConfig==0){
           queryWrapper.eq(Tender::getIsMaterial,1);
       }else {
           queryWrapper.eq(Tender::getIsDevice,1);
       }
       if (releaseDate!=null){
           if (releaseDate==1){
               queryWrapper.orderByDesc(Tender::getReleaseDate);
           }else {
               queryWrapper.orderByAsc(Tender::getReleaseDate);
           }
       }else {
           queryWrapper.orderByDesc(Tender::getApplyTime);
       }
        if (tendeAmount!=null){
            if (tendeAmount==1){
                queryWrapper.orderByDesc(Tender::getTenderAmount);
            }else {
                queryWrapper.orderByAsc(Tender::getTenderAmount);
            }
        }
//        IPage<Tender> page = this.page(
//        new Query<Tender>().getPage(jsonObject),
//        queryWrapper
//        );

        IPage<Tender> page = this.page(
                new Query<Tender>().getPage(jsonObject),
                queryWrapper
        );
        List<Tender> Tenders = page.getRecords();
        for (Tender tender : Tenders) {
            if (tender.getTenderClass()==2){
                LambdaQueryWrapper<TenderDtl> q = new LambdaQueryWrapper<>();
                q.eq(TenderDtl::getBillId,tender.getBillId());
                List<TenderDtl> list = tenderDtlService.list();
                if (list!=null){
                    double num= 0;
                    for (TenderDtl tenderDtl : list) {
                        double v = Double.parseDouble(tenderDtl.getNum().toString());
                       num=num+v;
                    }
                    tender.setNum(num);
                }

            }
        }

        return new PageUtils(page);
    }

    @Override
    public void create(Tender tender) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(tender);
    }

    @Override
    public void update(Tender tender) {
        super.updateById(tender);
    }


    @Override
    public Tender getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public PageUtils LoginqueryPage(JSONObject jsonObject, LambdaQueryWrapper<Tender> q) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        if(user.getOrgIds()!=null&&user.getOrgIds().size()>0){
            q.in(Tender::getOrgId,user.getOrgIds());
            q.eq(Tender::getIsShow,1);
            q.eq(Tender::getReleaseState,1);
            PageUtils pageUtils = queryPage(jsonObject, q);

            return pageUtils;
        }else {
            return new PageUtils();
        }

    }
    @Override
    public TenderResultVo getTenderNum(JSONObject jsonObject, LambdaQueryWrapper<Tender> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        Integer mallConfig = (Integer) innerMap.get("mallConfig");
        String startDate = (String) innerMap.get("startDate");
        String endDate = (String) innerMap.get("endDate");
        if (startDate!=null){
            queryWrapper.gt(Tender::getApplyTime,startDate);
        }if (endDate!=null){
            queryWrapper.lt(Tender::getApplyTime,endDate);
        }
        if (mallConfig==0){
            queryWrapper.eq(Tender::getIsMaterial,1);
        }else {
            queryWrapper.eq(Tender::getIsDevice,1);
        }
        TenderResultVo vo=baseMapper.getTenderNum(queryWrapper);
        queryWrapper.gt(Tender::getTenderState,5);
        long count = count(queryWrapper);
        vo.setCompletionProject(count);
        vo.setProceedProject(vo.getBiddingProject()-count);

       return  vo;
    }

    @Override
    public PageUtils inspectPage(JSONObject jsonObject, LambdaQueryWrapper<Tender> wrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        //inspect 纪检系统查询是否接收的招标状态 (改为发布状态)
         String keywords = (String) innerMap.get("keywords");
         String tenderName = (String) innerMap.get("tenderName");
         String tenderUser = (String) innerMap.get("tenderUser");
         String applyOrgName = (String) innerMap.get("applyOrgName");
         Integer mallType = (Integer) innerMap.get("mallType");
         Integer tenderForm = (Integer) innerMap.get("tenderForm");
        String startDate = (String) innerMap.get("startTime");
        String endDate = (String) innerMap.get("endTime");
        if (startDate!=null){
            wrapper.gt(Tender::getReleaseDate,startDate);
        }if (endDate!=null){
            wrapper.lt(Tender::getReleaseDate,endDate);
        }
        if (tenderUser!=null){
            wrapper.like(Tender::getTenderUser,tenderUser);
        }

            wrapper.eq(Tender::getTenderForm,tenderForm);
            UserLogin user = ThreadLocalUtil.getCurrentUser();
            List<String> orgIds = user.getOrgIds();
            if (orgIds!=null&&orgIds.size()>0){
                wrapper.in(Tender::getOrgId,orgIds);
            }else {
                return  new PageUtils();
            }
         if (mallType==0){
             wrapper.eq(Tender::getIsMaterial,1);

         }else {
             wrapper.eq(Tender::getIsDevice,1);
         }
        if (keywords!=null){
            wrapper.and(i->i.like(Tender::getTenderUser,keywords)
                    .or().like(Tender::getTenderName,keywords));
        }
        if (tenderName!=null){
            wrapper.like(Tender::getTenderName,tenderName);

        }
        if (applyOrgName!=null){
            wrapper.like(Tender::getApplyOrgName,applyOrgName);

        }
//        wrapper.gt(Tender::getTenderState,inspect);
        wrapper.eq(Tender::getReleaseState,1);
        IPage<Tender> page = this.page(
                new Query<Tender>().getPage(jsonObject),
                wrapper
        );
        return new PageUtils(page);
    }

    @Override
    public PageUtils attendBidList(JSONObject jsonObject, QueryWrapper<Tender> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String unitCode = user.getSocialCreditCode();
        Object tenderForm = innerMap.get("tenderForm");
        Date endDate = (Date) innerMap.get("endDate");
        Date startDate = (Date) innerMap.get("startDate");
        String applyOrgName = (String) innerMap.get("applyOrgName");
        String tenderUser = (String)innerMap.get("tenderUser");
        String keyword = (String) innerMap.get("keyword");
        String tenderName = (String) innerMap.get("tenderName");
        Integer mallConfig = (Integer) innerMap.get("mallConfig");
        if (mallConfig==0){
            queryWrapper.eq("t.is_material",1);
        }else {
            queryWrapper.eq("t.is_device",1);
        }
        if (applyOrgName!=null){
            queryWrapper.eq("t.apply_org_name",applyOrgName);
        }
        if (!StringUtils.isEmpty(keyword)) {
            queryWrapper.and(i->i.like("t.tender_user",keyword)
                    .or().like("t.tender_name",keyword)
                    .or().like("t.apply_org_name",keyword));
        }
        if (unitCode!=null){
            queryWrapper.eq("te.unit_code",unitCode);
        }
        if (tenderUser!=null){
            queryWrapper.eq("t.tender_user",tenderUser);
        }
        if (tenderName!=null){
            queryWrapper.eq("t.tender_name",tenderName);
        }
        if (tenderForm!=null){
            queryWrapper.eq("t.tender_form",tenderForm);
        }
        if (startDate!=null){
            queryWrapper.gt("t.release_date",startDate);
        }if (endDate!=null){
            queryWrapper.lt("t.release_date",endDate);
        }
        IPage<Tender> pages = new Query<Tender>().getPage(jsonObject);
        List<Tender> list = baseMapper.findByCondition(pages,queryWrapper);
        PageUtils pageUtils = new PageUtils(pages);
        pageUtils.setList(list);
        return pageUtils;
    }

    @Override
    public PageUtils winBidList(JSONObject jsonObject, QueryWrapper<Tender> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String unitCode = user.getSocialCreditCode();
        String tenderForm = (String) innerMap.get("tenderForm");
        Date endDate = (Date) innerMap.get("endDate");
        Date startDate = (Date) innerMap.get("startDate");
        String applyOrgName = (String) innerMap.get("applyOrgName");
        String tenderUser = (String)innerMap.get("tenderUser");
        String keyword = (String) innerMap.get("keyword");
        String tenderName = (String) innerMap.get("tenderName");
        Integer mallConfig = (Integer) innerMap.get("mallConfig");
        if (mallConfig==0){
            queryWrapper.eq("t.is_material",1);
        }else {
            queryWrapper.eq("t.is_device",1);
        }
        if (applyOrgName!=null){
            queryWrapper.eq("t.apply_org_name",applyOrgName);
        }
        if (!StringUtils.isEmpty(keyword)) {
            queryWrapper.and(i->i.like("t.tender_user",keyword)
                    .or().like("t.tender_name",keyword)
                    .or().like("t.apply_org_name",keyword));
        }
            queryWrapper.eq("tps.credit_code",unitCode);
        if (tenderUser!=null){
            queryWrapper.eq("t.tender_user",tenderUser);
        }
        if (tenderName!=null){
            queryWrapper.eq("t.tender_name",tenderName);
        }
        if (tenderForm!=null){
            queryWrapper.eq("t.tender_form",tenderForm);
        }
        if (startDate!=null){
            queryWrapper.gt("t.release_date",startDate);
        }if (endDate!=null){
            queryWrapper.lt("t.release_date",endDate);
        }
        IPage<Tender> pages = new Query<Tender>().getPage(jsonObject);
        List<Tender> list = baseMapper.winBidList(pages,queryWrapper);
        PageUtils pageUtils = new PageUtils(pages);
        pageUtils.setList(list);
        return pageUtils;
    }

    @Override
    public Tender getByBillNo(String BillNo) {
        Tender  tender= baseMapper.getByBillNo(BillNo);
        return tender;
    }


}
