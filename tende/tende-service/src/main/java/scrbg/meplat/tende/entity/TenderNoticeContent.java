package scrbg.meplat.tende.entity;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.tende.entity.parent.BaseEntity;

/**
 * @描述：招标_中标通知书_通知内容
 * @作者: demo
 * @日期: 2023-02-10
 */
@ApiModel(value="招标_中标通知书_通知内容")
@Data
@TableName("tender_notice_content")
public class TenderNoticeContent  extends BaseEntity implements Serializable  {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "记录ID")

        private String recordId;

    @ApiModelProperty(value = "单据ID")

        private String billId;

    @ApiModelProperty(value = "内容")

        private String content;

    @ApiModelProperty(value = "创建时间")

        private Date gmtCreate;

    @ApiModelProperty(value = "上次修改时间")

        private Date gmtModified;


}
