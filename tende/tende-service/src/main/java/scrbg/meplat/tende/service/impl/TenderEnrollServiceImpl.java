package scrbg.meplat.tende.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import scrbg.meplat.tende.entity.Tender;
import scrbg.meplat.tende.entity.TenderEnroll;
import scrbg.meplat.tende.entity.TenderPackage;
import scrbg.meplat.tende.mapper.TenderEnrollMapper;
import scrbg.meplat.tende.service.ITenderEnrollService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import scrbg.meplat.tende.service.TenderPackageService;
import scrbg.meplat.tende.service.TenderService;
import scrbg.meplat.tende.util.R;
import scrbg.meplat.tende.util.ThreadLocalUtil;
import scrbg.meplat.tende.util.UserLogin;
import scrbg.meplat.tende.vo.TenderEnrollVo;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @描述：物资供应招标_招标报名审核 服务类
 * @作者: demo
 * @日期: 2023-02-20
 */
@Service
public class TenderEnrollServiceImpl extends ServiceImpl<TenderEnrollMapper, TenderEnroll> implements ITenderEnrollService{
    @Autowired
    TenderPackageService tenderPackageService;
    @Autowired
    TenderService tenderService;
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        Integer tenderState = (Integer) innerMap.get("tenderState");
        Integer releaseDate = (Integer)innerMap.get("releaseDate");
        Integer tenderUser = (Integer)innerMap.get("tenderUser");
        Integer tenderForm = (Integer)innerMap.get("tenderForm");
        Integer tendeAmount = (Integer) innerMap.get("tendeAmount");
        Integer mallConfig = (Integer) innerMap.get("mallConfig");
        Integer tenderType = (Integer) innerMap.get("tenderType");
        String keywords = (String) innerMap.get("keywords");
        String startDate = (String) innerMap.get("startDate");
        String endDate = (String) innerMap.get("endDate");
        String tenderName = (String) innerMap.get("tenderName");

        IPage<TenderEnroll> page = this.page(
        new Query<TenderEnroll>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(TenderEnroll tenderEnroll) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(tenderEnroll);
    }

    @Override
    public void update(TenderEnroll tenderEnroll) {
        super.updateById(tenderEnroll);
    }


    @Override
    public TenderEnroll getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public PageUtils unitCodeListByEntity(JSONObject jsonObject, QueryWrapper<TenderEnrollVo> queryWrapper) {
//        Map<String, Object> innerMap = jsonObject.getInnerMap();
//        String unitCode = (String) innerMap.get("unitCode");
//        String tenderForm = (String) innerMap.get("tenderForm");
//        Date endDate = (Date) innerMap.get("endDate");
//        Date startDate = (Date) innerMap.get("startDate");
//        String applyOrgName = (String) innerMap.get("applyOrgName");
//        String applyUnit = (String) innerMap.get("applyUnit");
//        String keywords = (String) innerMap.get("keywords");
//        String tenderName = (String) innerMap.get("tenderName");
//        Integer mallConfig = (Integer) innerMap.get("mallConfig");
//        if (mallConfig==0){
//            queryWrapper.eq("t.is_material",1);
//        }else {
//            queryWrapper.eq("t.is_device",1);
//        }
//        if (applyOrgName!=null){
//            queryWrapper.eq("t.apply_org_name",applyOrgName);
//        }
//        if (!StringUtils.isEmpty(keywords)) {
//            queryWrapper.and(i->i.like("te.project_name",keywords)
//                    .or().like("te.apply_unit",keywords));
//        }
//        if (applyUnit!=null){
//            queryWrapper.eq("te.apply_unit",applyUnit);
//        }
//        if (unitCode!=null){
//            queryWrapper.eq("te.unit_code",unitCode);
//        }
//        if (tenderName!=null){
//            queryWrapper.eq("te.project_name",tenderName);
//        }
//        if (tenderForm!=null){
//            queryWrapper.eq("t.tender_form",tenderForm);
//        }
//        if (unitCode!=null){
//            queryWrapper.eq("te.unit_code",unitCode);
//        }
//        if (unitCode!=null){
//            queryWrapper.eq("te.unit_code",unitCode);
//        }
//        if (startDate!=null){
//            queryWrapper.gt("t.release_date",startDate);
//        }if (endDate!=null){
//            queryWrapper.lt("t.release_date",endDate);
//        }
//        IPage<TenderEnrollVo> pages = new Query<TenderEnrollVo>().getPage(jsonObject);
//        List<TenderEnrollVo> list = baseMapper.findByCondition(pages,queryWrapper);
        return null;
    }

    @Override
    public PageUtils Packageslist(JSONObject jsonObject, QueryWrapper queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String unitCode = (String) innerMap.get("unitCode");
        String tenderApplyId = (String) innerMap.get("tender_apply_id");
        if (unitCode!=null){
            queryWrapper.eq("te.unit_code",unitCode);
        }
        if (tenderApplyId!=null){
            queryWrapper.eq("te.tender_apply_id",tenderApplyId);
        }
        IPage<TenderPackage> pages = new Query<TenderPackage>().getPage(jsonObject);
        List<TenderPackage> list = baseMapper.Packageslist(pages,queryWrapper);
        return null;
    }

    @Override
    public R addTenderApply(String packAgeId) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        if (user==null){
          return   R.failed("用户未登录，不能报名");
        }

            TenderPackage tenderPackage = tenderPackageService.getById(packAgeId);
            Tender tender = tenderService.getById(tenderPackage.getBillId());
            if (tender==null){
                return   R.failed("报件不存在，报名失败");
            }
            Date endTime = tender.getTenderEndTime();
            if (endTime!=null){
                boolean flag = endTime.before(new Date());
                if (flag){
                    return   R.failed("报名时间已经截至，报名失败");
                }
            }else {
                return   R.failed("报名时间不存在，报名失败");
            }
            if (tenderPackage==null){
                return   R.failed("报件不存在，报名失败");

            }
            LambdaQueryWrapper<TenderEnroll> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TenderEnroll::getTenderApplyId,tender.getBillId())
                    .eq(TenderEnroll::getTenderApplyNo,tender.getBillNo())
                    .eq(TenderEnroll::getPackageIds,tenderPackage.getDtlId())
                    .eq(TenderEnroll::getUnitCode,user.getOrgId());
        TenderEnroll tenderEnroll = getOne(wrapper);
        if (tenderEnroll!=null){
            return   R.failed("用户已报名，请勿重复报名");
        }
        TenderEnroll info = new TenderEnroll();

         info.setTenderApplyId(tender.getBillId());
         info.setTenderApplyNo(tender.getBillNo());
         info.setProjectName(tender.getTenderName());
         info.setUnitCode(user.getOrgId());
//         info.setUnitName();
        info.setTenderType(tender.getTenderType());

         save(info);
        return R.success("包件报名成功");
    }
}
