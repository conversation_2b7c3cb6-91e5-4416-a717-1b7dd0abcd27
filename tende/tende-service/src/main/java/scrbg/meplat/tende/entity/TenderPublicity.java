package scrbg.meplat.tende.entity;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * @描述：招标_公示
 * @作者: demo
 * @日期: 2023-02-15
 */
@ApiModel(value="招标_公示")
@Data
@TableName("tender_publicity")
public class TenderPublicity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "单据ID")

        private String billId;

    @ApiModelProperty(value = "公示编号")

        private String billNo;

    @ApiModelProperty(value = "招标编号")

        private String tenderApplyNo;

    @ApiModelProperty(value = "招标申请ID")

        private String tenderApplyId;

    @ApiModelProperty(value = "招标项目名称")

        private String projectName;

    @ApiModelProperty(value = "采购方式")

        private String tenderForm;

    @ApiModelProperty(value = "招标类型")

        private Integer tenderClass;

    @ApiModelProperty(value = "公示开始日期")

        private Date startTime;

    @ApiModelProperty(value = "公示结束日期")

        private Date endTime;

    @ApiModelProperty(value = "状态")

        private Integer state;

    @ApiModelProperty(value = "招标机构ID")

        private String tenderOrgId;

    @ApiModelProperty(value = "招标机构名称")

        private String tenderOrgName;

    @ApiModelProperty(value = "备注")

        private String remarks;

    @ApiModelProperty(value = "机构id")

        private String orgId;

    @ApiModelProperty(value = "机构名称")

        private String orgName;

    @ApiModelProperty(value = "单据录入日期")

        private Date gmtCreate;

    @ApiModelProperty(value = "上次修改时间")

        private Date gmtModified;

    @ApiModelProperty(value = "发布状态（默认0未发表，1已发布）")

        private Integer releaseState;

    @ApiModelProperty(value = "录入人")

        private String founderName;

    @ApiModelProperty(value = "录入人ID")

        private String founderId;

    @ApiModelProperty(value = "作废时间")

        private Date nullifyCreated;

    @ApiModelProperty(value = "作废人")

        private String nullifyCreator;

    @ApiModelProperty(value = "作废人ID")

        private String nullifyCreatorId;

    @ApiModelProperty(value = "作废描述")

        private String nullifyDescription;

    @ApiModelProperty(value = "作废原因")

        private String nullifyReason;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "发布日期")

        private Date releaseDate;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "录入日期")

        private Date ntryDate;

    @ApiModelProperty(value = "默认0，1迁移数据")

        private Integer isTransfer;


}
