package scrbg.meplat.tende.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MallConfig {

    @Value("${mall.loginOutTime}")
    public Integer loginOutTime;

    @Value("${mall.prodPcwp2Url}")
    public String prodPcwp2Url;
    @Value("${host.deviceIp}")
    public String deviceIp;

    @Value("${host.materialIp}")
    public String materialIp;
}
