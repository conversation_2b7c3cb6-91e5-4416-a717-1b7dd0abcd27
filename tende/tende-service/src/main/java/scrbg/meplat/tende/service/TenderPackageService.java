package scrbg.meplat.tende.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.tende.vo.TenderPackageInfoVo;
import scrbg.meplat.tende.vo.TenderPackageVo;
import scrbg.meplat.tende.entity.TenderPackage;

import java.util.List;

/**
 * @描述：招标-包件 服务类
 * @作者: sund
 * @日期: 2023-02-03
 */
public interface TenderPackageService extends IService<TenderPackage> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<TenderPackage> queryWrapper);

        void create(TenderPackage tenderPackage);

        void update(TenderPackage tenderPackage);

        TenderPackage getById(String id);

        void delete(String id);

        List<TenderPackage> listByBillId(String id);

    List<TenderPackageVo> TeBerSuByBillId(String id);

    PageUtils winBidPackageList(JSONObject jsonObject, QueryWrapper<TenderPackage> tenderPackageQueryWrapper);

    List<TenderPackageVo> findByBillIdAll(String billId);
}
