package scrbg.meplat.tende.entity;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.tende.entity.parent.BaseEntity;

/**
 * @描述：招标_报名_企业主要业绩
 * @作者: demo
 * @日期: 2023-02-10
 */
@ApiModel(value="招标_报名_企业主要业绩")
@Data
@TableName("tender_enroll_achievement_history")
public class TenderEnrollAchievementHistory  extends BaseEntity implements Serializable  {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "记录ID")

        private String recordId;

    @ApiModelProperty(value = "单据ID")

        private String billId;

    @ApiModelProperty(value = "项目名称")

        private String projectName;

    @ApiModelProperty(value = "合同名称")

        private String contractName;

    @ApiModelProperty(value = "工作内容")

        private String workContent;

    @ApiModelProperty(value = "开工时间")

        private Date startTime;

    @ApiModelProperty(value = "竣工时间")

        private Date endTime;

    @ApiModelProperty(value = "合同金额")

        private BigDecimal contractAmount;

    @ApiModelProperty(value = "负责人")

        private String personInCharge;

    @ApiModelProperty(value = "证明人")

        private String witness;

    @ApiModelProperty(value = "证明人电话")

        private String witnessPhone;

    @ApiModelProperty(value = "证明附件")

        private String filePath;

    @ApiModelProperty(value = "创建时间")

        private Date gmtCreate;

    @ApiModelProperty(value = "上次修改时间")

        private Date gmtModified;

    @ApiModelProperty(value = "证明附件名称")

        private String fileName;


}
