<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <artifactId>tende</artifactId>
        <groupId>scrbg.meplat.tende</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>tende-interface</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.scrbg.pcwp2</groupId>
            <artifactId>common</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>7</source>
                    <target>7</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>maven_SCRBG</id>
            <url>http://192.168.100.28:8081/repository/maven_SCRBG/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>maven_SCRBG</id>
            <url>http://192.168.100.28:8081/repository/maven_SCRBG/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <!--私服仓库-->
    <distributionManagement>
        <repository>
            <id>maven_SCRBG</id>
            <url>http://192.168.100.28:8081/repository/maven_SCRBG_Releases/</url>
        </repository>
        <snapshotRepository>
            <id>maven_SCRBG</id>
            <url>http://192.168.100.28:8081/repository/maven_SCRBG_SnapShots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>