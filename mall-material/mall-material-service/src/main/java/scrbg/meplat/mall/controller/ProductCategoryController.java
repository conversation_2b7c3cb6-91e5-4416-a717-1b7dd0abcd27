package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.dto.category.UpdateCategorySateByIdDTO;
import scrbg.meplat.mall.entity.InterfaceLogs;
import scrbg.meplat.mall.entity.ProductCategory;
import scrbg.meplat.mall.entity.excelTemplate.Material;
import scrbg.meplat.mall.entity.excelTemplate.MaterialDtlInfos;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.InterfaceLogsService;
import scrbg.meplat.mall.service.ProductCategoryService;
import scrbg.meplat.mall.util.LogUtil;
import scrbg.meplat.mall.util.excel.EasyExcelUtils;
import scrbg.meplat.mall.vo.product.ImportExcelResultVO;
import scrbg.meplat.mall.vo.product.ImportMaterialExcelResultVO;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * @描述：商品分类控制类
 * @作者: y
 * @日期: 2022-11-02
 */
@RestController
@RequestMapping("/")
@ApiSort(value = 500)
@Api(tags = "商品分类")
public class ProductCategoryController {
    @Autowired
    public ProductCategoryService productCategoryService;

    @Autowired
    private InterfaceLogsService interfaceLogsService;

    @PostMapping("/platform/productCategory/create")
    @ApiOperation(value = "新增")
    @CacheEvict(value = {"category"}, key = "#g.productType + '_' + #g.mallType + '_getTree'")
    public R save(@Valid @RequestBody ProductCategory g) {
        String idStr = IdWorker.getIdStr();
        String fag="";
        try {
            productCategoryService.create(g, idStr,fag);
        } catch (Exception e) {
            LogUtil.writeErrorLog(idStr, "create", g, null, null, e.getMessage(), ProductCategoryController.class);
            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(idStr);
            iLog.setClassPackage(ProductCategoryController.class.getName());
            iLog.setMethodName("create");
            iLog.setLocalArguments(JSON.toJSONString(g));
            iLog.setFarArguments(fag);
            iLog.setIsSuccess(0);
            iLog.setLogType(1);
            iLog.setErrorInfo(e.getMessage());
            interfaceLogsService.create(iLog);
            throw new BusinessException(500, e.getMessage());
        }
        return R.success();
    }


    @PostMapping("/platform/productCategory/platforUploadMaterialExcelFile")
    @ApiOperation(value = "物资基础库批量导入")
    @NotResubmit
    public R platforUploadMaterialExcelFile(@RequestPart("file") MultipartFile file) {
        List<ImportMaterialExcelResultVO> vos = new ArrayList<>();
        //日志id
        String idStr = IdWorker.getIdStr();
        //本地参数
        List<MaterialDtlInfos> objects = new ArrayList<>();
        //请求参数
        StringBuilder farArguments = new StringBuilder();

        try {
            //文件转化为集合
            objects = EasyExcelUtils.readExcelOneSheet(file.getInputStream(), MaterialDtlInfos.class);
            if (objects.size() > 4000) {
                throw new BusinessException(500, "数量超过4000禁止导入！");
            }
            vos = productCategoryService.platforUploadMaterialExcelFile(objects, idStr, farArguments);
            return R.success(vos);
        } catch (Exception e) {
            LogUtil.writeErrorLog(idStr, "platforUploadMaterialExcelFile", objects, null, null, e.getMessage(), ProductCategoryController.class);
            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(idStr);
            iLog.setClassPackage(ProductCategoryController.class.getName());
            iLog.setMethodName("platforUploadMaterialExcelFile");
            iLog.setLocalArguments(JSON.toJSONString(objects));
            iLog.setFarArguments(String.valueOf(farArguments));
            iLog.setIsSuccess(0);
            iLog.setLogType(1);
            iLog.setErrorInfo(e.getMessage());
            interfaceLogsService.create(iLog);
            throw new BusinessException(500, e.getMessage());
        }

    }

    @PostMapping("/platform/productCategory/update")
    @ApiOperation(value = "修改")
    @CacheEvict(value = {"category"}, key = "#g.productType + '_' + #g.mallType + '_getTree'")
    public R update(@RequestBody ProductCategory g) {
        String idStr = IdWorker.getIdStr();
        try {
            productCategoryService.updateIfById(g, idStr);
        } catch (Exception e) {
            LogUtil.writeErrorLog(idStr, "updateIfById", g, null, null, e.getMessage(), ProductCategoryController.class);
            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(idStr);
            iLog.setClassPackage(ProductCategoryController.class.getName());
            iLog.setMethodName("updateIfById");
            iLog.setLocalArguments(JSON.toJSONString(g));
            iLog.setIsSuccess(0);
            iLog.setLogType(1);
            iLog.setErrorInfo(e.getMessage());
            interfaceLogsService.create(iLog);
            throw new BusinessException(500, e.getMessage());
        }
        return R.success();
    }

    @PostMapping("/platform/productCategory/update/sate")
    @ApiOperation(value = "批量启用停用状态")
    public R updateSate(@Valid @RequestBody UpdateCategorySateByIdDTO dto) {
        String idStr = IdWorker.getIdStr();
        try {
            productCategoryService.updateCategorySateById(dto,idStr);
        }catch (Exception e){
            LogUtil.writeErrorLog(idStr, "updateSate", dto, null, null, e.getMessage(), ProductCategoryController.class);
            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(idStr);
            iLog.setClassPackage(ProductCategoryController.class.getName());
            iLog.setMethodName("updateSate");
            iLog.setLocalArguments(JSON.toJSONString(dto));
            iLog.setIsSuccess(0);
            iLog.setLogType(1);
            iLog.setErrorInfo(e.getMessage());
            interfaceLogsService.create(iLog);
            throw new BusinessException(500, e.getMessage());
        }
        return R.success();
    }

    @PostMapping("/platform/productCategory/deleteBatch")
    @ApiOperation(value = "根据分类ids批量逻辑删除（平台）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "分类ids", required = true,
                    dataType = "list", paramType = "query")
    })
    public R deleteBatch(@RequestBody List<String> ids) {
        productCategoryService.removeLogicBatch(ids);
        return R.success();
    }

    @GetMapping("/platform/productCategory/listByClassName")
    @ApiOperation(value = "根据分类名称模糊查询（通用）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "className", value = "分类名称",
                    dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "productType", value = "分类类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备", required = true,
                    dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "state", value = "启用状态（1启用0停用，不传查询所有）",
                    dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "isHaveProduct", value = "是否有商品(1有0没有,不传查询所有）",
                    dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "isLc", value = "（0 全部，1 大宗临购  2 低值易耗品）",
                    dataType = "Integer", paramType = "query"),
    })
    public R<List<ProductCategory>> listByClassName(String className, Integer productType, Integer state, Integer isHaveProduct, Integer isLc) {
        List<ProductCategory> productCategory = productCategoryService.listByClassName(className, productType, state, isHaveProduct, isLc);
        return R.success(productCategory);
    }

    @GetMapping("/platform/productCategory/listByClassNameTwo")
    public R<List<ProductCategory>> listByClassNameTwo(String className, Integer productType, Integer state, Integer isHaveProduct, Integer isLc,String versionId) {
        List<ProductCategory> productCategory = productCategoryService.listByClassNameTwo(className, productType, state, isHaveProduct, isLc,versionId);
        return R.success(productCategory);
    }


    @PostMapping("/platform/productCategory/outputExcel")
    @ApiOperation(value = "根据分类导出（通用）")
    public R outputExcel(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        productCategoryService.outputExcel(jsonObject, response);
        return R.success();
    }
    /**
     * 获取分类树形
     *
     * @return
     */
//    @GetMapping("/productCategory/listTree")
//    @ApiOperation(value = "获取分类树（通用）")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "productType", value = "分类类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备",
//                    dataType = "Integer", paramType = "query"),
//            @ApiImplicitParam(name = "state", value = "启用状态（1启用0停用，不传查询所有）",
//                    dataType = "Integer", paramType = "query")
//    })
//    public R<List<ProductCategory>> listTree(Integer productType, Integer state) {
//        List<ProductCategory> entities = productCategoryService.listWithTree(productType, null, state, null);
//        return R.success(entities);
//    }


//    @GetMapping("/productCategory/findById")
//    @ApiOperation(value = "根据主键查询（通用）")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "分类id", required = true,
//                    dataType = "String", paramType = "query")
//    })
//    public R<ProductCategory> findById(String id) {
//        ProductCategory productCategory = productCategoryService.getById(id);
//        return R.success(productCategory);
//    }


//    @PostMapping("/platform/productCategory/updateBatch")
//    @ApiOperation(value = "批量修改（平台）")
//    public R updateBatch(@RequestBody List<ProductCategory> productCategories) {
//        productCategoryService.updateBatchById(productCategories);
//        return R.success();
//    }


//    @GetMapping("/delete")
//    @ApiOperation(value = "根据主键删除")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "ID", required = true,
//                    dataType = "String", paramType = "query")
//    })
//    public R delete(String id) {
//        productCategoryService.delete(id);
//
//        return R.success();
//    }
//    /**
//     * 分类信息
//     * @param classId
//     * @return
//     */
//    @GetMapping("/productCategory/info/{classId}")
//    public R info(@PathVariable("classId") String classId){
//        ProductCategory productCategory = productCategoryService.getById(classId);
//        return R.success(productCategory);
//    }

}

