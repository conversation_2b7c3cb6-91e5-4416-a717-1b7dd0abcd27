package scrbg.meplat.mall.util.poi.exp.context;

import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.util.Map;

/**
 * 上下文
 * 
 * <AUTHOR>
 * @date 2017年7月11日 下午7:54:10
 */
public class PoiExporterContext {
	private SpelExpressionParser spelExpParser;
	public static final StandardEvaluationContext EVAL_CONTEXT = new StandardEvaluationContext();
	private Map<String, Object> rootObjectMap;

	public PoiExporterContext() {
		super();
	}

	public PoiExporterContext(SpelExpressionParser spelExpressionParser, Map<String, Object> rootObjectMap) {
		this.spelExpParser = spelExpressionParser;
		this.rootObjectMap = rootObjectMap;
	}

	public SpelExpressionParser getSpelExpParser() {
		return spelExpParser;
	}

	public Map<String, Object> getRootObjectMap() {
		return rootObjectMap;
	}
	
}
