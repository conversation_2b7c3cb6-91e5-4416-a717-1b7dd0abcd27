package scrbg.meplat.mall.vo.user.userCenter;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-12-21 14:36
 */
@Data
public class CreateShopEchoInfoVO {


    @ApiModelProperty(value = "身份证人像面照")
    private String identityCardFace;

    @ApiModelProperty(value = "身份证人像面照id")
    private String identityCardFaceId;

    @ApiModelProperty(value = "身份证国徽面照")
    private String identityCardBadge;

    @ApiModelProperty(value = "身份证国徽面照id")
    private String identityCardBadgeId;

    @ApiModelProperty(value = "姓名")
    private String realName;

    @ApiModelProperty(value = "身份证号")
    private String identityCard;


    @ApiModelProperty(value = "店铺类型 0：个体户  1：企业  2：个人")
    private Integer shopType;




}
