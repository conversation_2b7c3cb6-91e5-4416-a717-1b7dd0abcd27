package scrbg.meplat.mall.service;

import scrbg.meplat.mall.dto.AuditDTO;
import scrbg.meplat.mall.entity.PlatformDealFeeRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.PlatformDealFeeRecord;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.util.List;

/**
 * @描述：平台交易费缴费记录 服务类
 * @作者: ye
 * @日期: 2024-01-24
 */
public interface PlatformDealFeeRecordService extends IService<PlatformDealFeeRecord> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<PlatformDealFeeRecord> queryWrapper);

    void create(PlatformDealFeeRecord platformDealFeeRecord);

    void update(PlatformDealFeeRecord platformDealFeeRecord);

    PlatformDealFeeRecord getById(String id);

    void delete(String id);

    void deleteBatch(List<String> ids);

    /**
     * 供应商新增交易费
     *
     * @param platformDealFeeRecord
     */
    String supplierCreateDealFee(PlatformDealFeeRecord platformDealFeeRecord);

    /**
     * 平台查询交易费记录
     * @param jsonObject
     * @param q
     * @return
     */
    PageUtils platformListByEntity(JSONObject jsonObject, LambdaQueryWrapper<PlatformDealFeeRecord> q);

    /**
     * 供应商查询交易费记录
     * @param jsonObject
     * @param q
     * @return
     */
    PageUtils supplierListByEntity(JSONObject jsonObject, LambdaQueryWrapper<PlatformDealFeeRecord> q);

    /**
     * 供应商修改交易费缴费
     * @param platformDealFeeRecord
     */
    void supplierUpdateDealFee(PlatformDealFeeRecord platformDealFeeRecord);

    /**
     * 根据编号获取数据
     * @param sn
     * @return
     */
    PlatformDealFeeRecord findBySn(String sn);

    /**
     * 根据id删除交易服务缴费记录
     * @param id
     */
    void deleteDealFeeRecord(String id);

    /**
     * 审核缴费
     * @param dto
     */
    void audit(AuditDTO dto);
}
