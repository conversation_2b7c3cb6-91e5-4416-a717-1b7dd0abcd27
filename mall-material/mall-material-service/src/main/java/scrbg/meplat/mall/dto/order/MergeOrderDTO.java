package scrbg.meplat.mall.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-11-20 11:01
 */
@Data
public class MergeOrderDTO {

    @ApiModelProperty(value = "订单id（需要合并的订单）",required = true)
    @NotEmpty(message = "订单id不能为空！")
    private List<String> orderId;

    @ApiModelProperty(value = "订单号（要合并的到的订单号）",required = true)
    @NotBlank(message = "订单号不能为空！")
    private String orderSn;
}
