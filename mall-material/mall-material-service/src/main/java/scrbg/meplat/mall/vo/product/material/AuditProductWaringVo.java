package scrbg.meplat.mall.vo.product.material;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 审核商品预警
 */
@Data
public class AuditProductWaringVo {
    @ApiModelProperty(value = "平均价格")

    private BigDecimal referencePrice;
    @ApiModelProperty(value = "区域")
    private String zone;
    @ApiModelProperty(value = "销售价格")
    private BigDecimal zonePrice;
    @ApiModelProperty(value = "预警规则名称")

    private String ruleName;
    @ApiModelProperty(value = "上限")

    private BigDecimal upPercentage;


    @ApiModelProperty(value = "下限")

    private BigDecimal lowerPercentage;
    @ApiModelProperty(value = "比价类型 （1平均分 ，2 自营店商品）")

    private Integer comparisonType;
    @ApiModelProperty(value = "销售区域比较 （0否 ，1 比较）")

    private Integer isCompareZone;
    @ApiModelProperty(value = "商品id")
    private String productId;

    @ApiModelProperty(value = "商品编码")
    private String serialNum;

}
