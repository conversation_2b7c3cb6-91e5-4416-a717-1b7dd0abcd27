package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import org.springframework.web.multipart.MultipartFile;
import scrbg.meplat.mall.dto.mail.ShopInfoUpdateDTO;
import scrbg.meplat.mall.dto.outer.GetOuterUseShopIdDTO;
import scrbg.meplat.mall.entity.EnterpriseInfo;
import scrbg.meplat.mall.entity.Role;
import scrbg.meplat.mall.entity.Shop;
import scrbg.meplat.mall.vo.platform.PlatformShopCountVo;
import scrbg.meplat.mall.vo.platform.ShopArrearageStateVo;
import scrbg.meplat.mall.vo.platform.ShopCountVo;
import scrbg.meplat.mall.vo.product.ImportExcelResultVO;
import scrbg.meplat.mall.vo.product.shop.website.WebsiteShopVo;
import scrbg.meplat.mall.vo.product.website.ImportShopExcelResultVO;
import scrbg.meplat.mall.vo.shopManage.ShopInfoUpdateVO;
import scrbg.meplat.mall.vo.user.userCenter.OpenShopInfoVO;
import scrbg.meplat.mall.vo.w.IndexShopListVO;

import java.util.List;
import java.util.Map;

/**
 * @描述：店铺 服务类
 * @作者: y
 * @日期: 2022-11-02
 */
public interface ShopService extends IService<Shop> {

    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<Shop> queryWrapper);

    PageUtils queryNotPage(JSONObject jsonObject, LambdaQueryWrapper<Shop> queryWrapper);

    R create(Shop shop, List<Role> roles);

    void update(Shop shop);

    Shop getById(String id);

    void delete(String id);

    Shop selectByUserId(String userId);

    void updateByPublish(List<String> ids, String s);


    WebsiteShopVo getWebsiteShop(String shopId, Integer mallType);



    String getShopPhoneByShopId(String shopId);


    PageUtils findPublicShop(JSONObject jsonObject);


    /**
     * 根据店铺id查询店铺名称
     * @param shopId
     * @return
     */
    String getShopNameById(String shopId);


    /**
     * 批量更新店铺信息
     * @param shops
     */
    void updateBatchAuditStatus(List<Shop> shops);

    /**
     * 根据店铺id查询店铺信息
     * @param shopId
     * @return
     */
    OpenShopInfoVO getRestartOpenShopInfo(String shopId);

    /**
     * 查询店铺信息
     * @return
     */
    ShopInfoUpdateVO getShopInfo();

    /**
     * 修改店铺信息
     * @param dto
     */
    void updateShopInfo(ShopInfoUpdateDTO dto);

    /**
     * 首页查询店铺
     * @param size
     * @return
     */
    List<IndexShopListVO> getIndexShopList(Integer size);

    /**
     * 获取店铺id
     *
     * @param dto
     * @return
     */
    Map<String, String> getOuterUseShopId(GetOuterUseShopIdDTO dto);


    /**
     * 检查店铺id是否存在和状态启用
     * @param shopId
     * @return
     */
    boolean isCheckShopIdState(String shopId);

    /**
     * 根据店铺id查询企业名称
     * @param shopId
     * @return
     */
    String getEnterpriseInfoNameByShopId(String shopId);

    PlatformShopCountVo getPlatformShopCount(JSONObject jsonObject, QueryWrapper<ShopCountVo> shopCountVoQueryWrapper);

    /**
     * 根据信用代码查询店铺id
     * @param socialCreditCode
     * @return
     */
    String getShopIdBySocialCreditCode(String socialCreditCode);

    /**
     * 根据企业Id查询店铺信息
     * @param supplierId
     * @return
     */
    Shop getDataByEnterPriseId(String supplierId);

    EnterpriseInfo getEnterpriseInfoByShopId(String shopId);


    void updateBatchArrearage(ShopArrearageStateVo vo);

    void auditArrearageByShopId (String shopId);

    /**
     店铺欠费续费Excel导入
     * @param file
     * @return
     */

    List<ImportShopExcelResultVO> shopArrearageExcelFile(MultipartFile file);

    /**
     * 店铺启用停用Excel导入
     * @param file
     * @return
     */
    List<ImportShopExcelResultVO> stopShopStateExcelFile(MultipartFile file);
}
