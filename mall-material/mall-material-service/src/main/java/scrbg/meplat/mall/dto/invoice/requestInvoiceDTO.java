package scrbg.meplat.mall.dto.invoice;

import lombok.Data;

import java.util.Date;

@Data
public class requestInvoiceDTO {
    private String invoiceId;


    private String userId;


    private String shopId;


    private Integer invoiceState;


    private Integer invoiceType;


    private Integer invoiceContent;


    private Integer riseType;

    private String userAddress;

    private String userName;

    private String userPhone;


    private String email;


    private String company;

    private String dutyParagraph;

    private String registerAddress;

    private String registerPhone;

    private String bank;

    private String bankAccount;

    private Date gmtApply;

    private Date gmtAdopt;

    private String invoiceNo;

}
