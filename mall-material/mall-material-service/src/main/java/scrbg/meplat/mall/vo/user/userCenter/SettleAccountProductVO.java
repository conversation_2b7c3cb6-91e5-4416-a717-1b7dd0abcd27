package scrbg.meplat.mall.vo.user.userCenter;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023-01-12 11:07
 */
@Data
public class SettleAccountProductVO {

    @ApiModelProperty(value = "店铺id")
    private String shopId;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "关联名称（用于关联外部的名称唯一不修改）")
    private String relevanceName;

    @ApiModelProperty(value = "商品id")
    private String productId;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "分类id")
    private String classId;

    @ApiModelProperty(value = "品牌id")

    private String brandId;

    @ApiModelProperty(value = "品牌名称")

    private String brandName;

//    @ApiModelProperty(value = "分类名称")
//    private String className;

    @ApiModelProperty(value = "商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备")
    private Integer productType;

    @ApiModelProperty(value = "商品关键字（,分隔）")
    private String productKeyword;

    @ApiModelProperty(value = "商品运费类型（0商家包邮）")
    private Integer productTransportType;

    @ApiModelProperty(value = "商品小图")
    private String productMinImg;


    @ApiModelProperty(value = "skuId")
    private String skuId;
    @ApiModelProperty(value = "规格名称")
    private String skuName;

    @ApiModelProperty(value = "原价")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "销售价格")
    private BigDecimal sellPrice;

    @ApiModelProperty(value = "库存")
    private BigDecimal stock;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "销量")
    private BigDecimal soldNum;





    @ApiModelProperty(value = "小计")
    private BigDecimal numTotalPrice;

    @ApiModelProperty(value = "总金额")
    private BigDecimal totalPrice;


    @ApiModelProperty(value = "租赁单位")
    private String leaseUnit;
}
