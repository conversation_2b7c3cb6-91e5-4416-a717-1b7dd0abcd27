package scrbg.meplat.mall.vo.user.userCenter;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.OrderReturn;
import scrbg.meplat.mall.entity.OrderReturnItem;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class OrderReturnVo  extends OrderReturn {
    @ApiModelProperty(value = "退货id")
    private String orderReturnId;
    @ApiModelProperty(value = "下单时间")
    private Date gmtCreate ;
    @ApiModelProperty(value = "订单id")
    private String orderId;
    @ApiModelProperty(value = "订单编号")
    private String orderSn;
    @ApiModelProperty(value = "状态")
    private Integer state;




    List<OrderReturnItem> items;






}
