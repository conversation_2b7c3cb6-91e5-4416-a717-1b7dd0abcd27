package scrbg.meplat.mall.vo.user.userCenter;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.vo.product.website.material.WMaterialBaseVo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023-02-10 11:12
 */
@Data
public class OrderItemProductVO  extends WMaterialBaseVo {


    @ApiModelProperty(value = "订单项id")

    private String orderItemId;

    @ApiModelProperty(value = "商品id")

    private String productId;

    @ApiModelProperty(value = "商品名称")

    private String productName;

    @ApiModelProperty(value = "商品编号")

    private String productSn;

    @ApiModelProperty(value = "商品图片")

    private String productImg;

    @ApiModelProperty(value = "skuid")

    private String skuId;

    @ApiModelProperty(value = "商品价格")

    private BigDecimal productPrice;

    @ApiModelProperty(value = "购买数量")

    private BigDecimal buyCounts;

    @ApiModelProperty(value = "商品金额")

    private BigDecimal totalAmount;




    @ApiModelProperty(value = "规格名称")

    private String skuName;

    @ApiModelProperty(value = "品牌名")

    private String brandName;

    @ApiModelProperty(value = "sku单位")

    private String unit;

    @ApiModelProperty(value = "商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备")
    private Integer productType;


    @ApiModelProperty(value = "关联计划id（逗号分隔）")
    private String billId;

    @ApiModelProperty(value = "关联计划明细id（逗号分割）")
    private String dtlId;

    @ApiModelProperty(value = "关联名称（用于关联外部的名称唯一不修改）")
    private String relevanceName;


    @ApiModelProperty(value = "服务区域")
    private String coverage;

    @ApiModelProperty(value = "客户电话")
    private String clientPhone;

    @ApiModelProperty(value = "服务范围")
    private String serveScope;

    @ApiModelProperty(value = "信息来源")
    private String infoSource;

    @ApiModelProperty(value = "租赁时长")
    private BigDecimal leaseNum;
    @ApiModelProperty(value = "租赁单位")
    private String leaseUnit;


    @ApiModelProperty(value = "材质")

    private String texture;


}
