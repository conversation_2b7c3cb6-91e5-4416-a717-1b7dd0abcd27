package scrbg.meplat.mall.vo.user.userCenter;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-01-03 8:59
 */
@Data
public class CreateInsideShopEchoInfoVO {


    @ApiModelProperty(value = "企业id")
    private String enterpriseId;

    @ApiModelProperty(value = "企业编号")

    private String enterpriseNumber;


    @ApiModelProperty(value = "企业名称（公司名称、供应商公司名称 ）")

    private String enterpriseName;


    @ApiModelProperty(value = "统一社会信用代码")

    private String socialCreditCode;
}
