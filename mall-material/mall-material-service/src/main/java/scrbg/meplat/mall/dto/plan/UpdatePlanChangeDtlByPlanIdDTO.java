package scrbg.meplat.mall.dto.plan;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-06-30 14:01
 */
@Data
public class UpdatePlanChangeDtlByPlanIdDTO {

    @ApiModelProperty(value = "是否提交")
    private Integer isSubmit;

    @ApiModelProperty(value = "变更计划Id",required = true)
    @NotEmpty(message = "变更计划id不能为空！")
    private String planChangeId;

    @ApiModelProperty(value = "计划日期",required = true)
    @JsonFormat(pattern = "yyyy-MM")
    @NotNull(message = "计划日期不能为空！")
    private Date planDate;

    @ApiModelProperty(value = "计划明细")
    private List<UpdatePlanChangeDtlByPlanIdDtlDTO> dtls;

    @ApiModelProperty(value = "备注")
    private String remarks;

}
