package scrbg.meplat.mall.vo.user.userCenter;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.File;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-12-21 14:36
 */
@Data
public class EnterpriseAuthUpdateVO {


    @ApiModelProperty(value = "企业id")
    private String enterpriseId;

    @ApiModelProperty(value = "企业编号")

    private String enterpriseNumber;


    @ApiModelProperty(value = "企业名称（公司名称、供应商公司名称 ）")

    private String enterpriseName;


    @ApiModelProperty(value = "统一社会信用代码")

    private String socialCreditCode;


    @ApiModelProperty(value = "法定代表人")

    private String legalRepresentative;


    @ApiModelProperty(value = "企业邮箱")

    private String email;

    @ApiModelProperty(value = "注册日期")

    private Date creationTime;
    @ApiModelProperty(value = "营业执照有效期")

    private Date licenseTerm;
    @ApiModelProperty(value = "注册资本(万元)")

    private BigDecimal registeredCapital;

    @ApiModelProperty(value = "注册详细地址")

    private String detailedAddress;

    @ApiModelProperty(value = "经营范围")

    private String mainBusiness;

    @ApiModelProperty(value = "注册省份")

    private String provinces;


    @ApiModelProperty(value = "注册市级")

    private String city;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;


    @ApiModelProperty(value = "注册县、区")

    private String county;


    @ApiModelProperty(value = "管理员姓名")

    private String adminName;


    @ApiModelProperty(value = "管理员电话")

    private String adminPhone;

    @ApiModelProperty(value = "管理员身份证号")

    private String adminNumber;


    @ApiModelProperty(value = "营业执照（地址）")

    private String businessLicense;

    @ApiModelProperty(value = "营业执照（记录id）")

    private String businessLicenseId;

    @ApiModelProperty(value = "经营场所")

    private String placeOfBusiness;

    @ApiModelProperty(value = "经营者")

    private String operator;

    @ApiModelProperty(value = "身份证人像面(记录id)")

    private String cardPortraitFaceId;


    @ApiModelProperty(value = "身份证人像面(存地址)")

    private String cardPortraitFace;


    @ApiModelProperty(value = "身份证国徽面（记录id）")

    private String cardPortraitNationalEmblemId;


    @ApiModelProperty(value = "身份证国徽面（存地址）")


    private String cardPortraitNationalEmblem;

    @ApiModelProperty(value = "导入类型：1excel")
    private Integer importType;
    @ApiModelProperty(value = "附件列表")
    private List<File> files;


}
