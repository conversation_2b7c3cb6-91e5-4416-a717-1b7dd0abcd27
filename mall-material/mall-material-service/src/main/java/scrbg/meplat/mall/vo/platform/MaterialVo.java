package scrbg.meplat.mall.vo.platform;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MaterialVo {
    @ApiModelProperty(value = "物资id")

    private String billId;

    @ApiModelProperty(value = "物资类别id")

    private String classId;

    @ApiModelProperty(value = "物资类别名称")

    private String className;

    @ApiModelProperty(value = "类别编号")

    private String billNo;

    @ApiModelProperty(value = "物料名称")

    private String materialName;

    @ApiModelProperty(value = "规格")

    private String spec;

    @ApiModelProperty(value = "是否启用(0：停用;1：启用)")

    private Integer isEnable;

    @ApiModelProperty(value = "计量单位")

    private String unit;







}
