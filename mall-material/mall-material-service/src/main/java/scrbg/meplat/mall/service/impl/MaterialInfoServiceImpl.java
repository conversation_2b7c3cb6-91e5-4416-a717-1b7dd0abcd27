package scrbg.meplat.mall.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import scrbg.meplat.mall.entity.MaterialInfo;
import scrbg.meplat.mall.mapper.MaterialInfoMapper;
import scrbg.meplat.mall.pcwp.third.model.MaterialNew;
import scrbg.meplat.mall.service.MaterialInfoService;
import scrbg.meplat.mall.utils.ThreadLocalUtil;
import scrbg.meplat.mall.vo.user.UserLogin;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @描述：物资信息表 服务实现类
 * @作者: AI Assistant
 * @日期: 2025-07-25
 */
@Slf4j
@Service
public class MaterialInfoServiceImpl extends ServiceImpl<MaterialInfoMapper, MaterialInfo> implements MaterialInfoService {

    @Autowired
    private MaterialInfoMapper materialInfoMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInsert(List<MaterialInfo> materialInfoList) {
        if (CollectionUtils.isEmpty(materialInfoList)) {
            log.warn("批量插入物资信息列表为空");
            return 0;
        }

        log.info("开始批量插入物资信息，数量：{}", materialInfoList.size());
        
        // 分批处理，每批1000条记录
        int batchSize = 1000;
        int totalInserted = 0;
        
        for (int i = 0; i < materialInfoList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, materialInfoList.size());
            List<MaterialInfo> batchList = materialInfoList.subList(i, endIndex);
            
            int inserted = materialInfoMapper.batchInsert(batchList);
            totalInserted += inserted;
            
            log.info("批量插入第{}批，插入数量：{}", (i / batchSize + 1), inserted);
        }
        
        log.info("批量插入物资信息完成，总插入数量：{}", totalInserted);
        return totalInserted;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInsertFromMaterialNew(List<MaterialNew> materialNewList) {
        if (CollectionUtils.isEmpty(materialNewList)) {
            log.warn("MaterialNew列表为空，无法进行批量插入");
            return 0;
        }

        log.info("开始从MaterialNew列表转换并批量插入，数量：{}", materialNewList.size());

        // 过滤掉已存在的记录
        List<MaterialNew> filteredList = materialNewList.stream()
                .filter(materialNew -> !isExist(materialNew))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filteredList)) {
            log.info("所有记录都已存在，无需插入");
            return 0;
        }

        log.info("过滤后需要插入的记录数量：{}", filteredList.size());

        // 转换为MaterialInfo对象
        List<MaterialInfo> materialInfoList = filteredList.stream()
                .map(this::convertFromMaterialNew)
                .collect(Collectors.toList());

        return batchInsert(materialInfoList);
    }

    @Override
    public MaterialInfo convertFromMaterialNew(MaterialNew materialNew) {
        if (materialNew == null) {
            return null;
        }

        MaterialInfo materialInfo = new MaterialInfo();
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        LocalDateTime now = LocalDateTime.now();

        // 基本字段映射
        materialInfo.setSort(materialNew.getSort());
        materialInfo.setBillId(materialNew.getBillId());
        materialInfo.setBillNo(materialNew.getBillNo());
        materialInfo.setClassId(materialNew.getClassId());
        materialInfo.setClassIdPath(materialNew.getClassIdPath());
        materialInfo.setClassName(materialNew.getClassName());
        materialInfo.setClassNamePath(materialNew.getClassNamePath());
        materialInfo.setIsEnable(materialNew.getIsEnable());
        materialInfo.setMaterialName(materialNew.getMaterialName());
        materialInfo.setOrgId(materialNew.getOrgId());
        materialInfo.setOrgName(materialNew.getOrgName());
        materialInfo.setSpec(materialNew.getSpec());
        materialInfo.setTopClassId(materialNew.getTopClassId());
        materialInfo.setTopClassName(materialNew.getTopClassName());
        materialInfo.setUnit(materialNew.getUnit());
        materialInfo.setVersionId(materialNew.getVersionId());

        // 设置默认值和系统字段
        materialInfo.setMallType(0); // 默认物资商城
        materialInfo.setGmtCreate(now);
        materialInfo.setGmtModified(now);
        materialInfo.setIsDelete(0); // 未删除

        // 设置创建人信息
        if (currentUser != null) {
            materialInfo.setFounderId(currentUser.getUserId());
            materialInfo.setFounderName(currentUser.getUserName());
            materialInfo.setModifyId(currentUser.getUserId());
            materialInfo.setModifyName(currentUser.getUserName());
        }

        return materialInfo;
    }

    @Override
    public MaterialInfo getByBillIdAndBillNo(String billId, String billNo) {
        if (StringUtils.isEmpty(billId) || StringUtils.isEmpty(billNo)) {
            return null;
        }
        return materialInfoMapper.selectByBillIdAndBillNo(billId, billNo);
    }

    @Override
    public List<MaterialInfo> getByMaterialNameAndClassId(String materialName, String classId) {
        if (StringUtils.isEmpty(materialName) || StringUtils.isEmpty(classId)) {
            return new ArrayList<>();
        }
        return materialInfoMapper.selectByMaterialNameAndClassId(materialName, classId);
    }

    @Override
    public boolean isExist(MaterialNew materialNew) {
        if (materialNew == null) {
            return false;
        }

        // 根据单据ID和单据编号判断是否存在
        if (!StringUtils.isEmpty(materialNew.getBillId()) && !StringUtils.isEmpty(materialNew.getBillNo())) {
            MaterialInfo existing = getByBillIdAndBillNo(materialNew.getBillId(), materialNew.getBillNo());
            if (existing != null) {
                log.debug("物资信息已存在，billId: {}, billNo: {}", materialNew.getBillId(), materialNew.getBillNo());
                return true;
            }
        }

        // 根据物资名称和类别ID判断是否存在
        if (!StringUtils.isEmpty(materialNew.getMaterialName()) && !StringUtils.isEmpty(materialNew.getClassId())) {
            List<MaterialInfo> existingList = getByMaterialNameAndClassId(materialNew.getMaterialName(), materialNew.getClassId());
            if (!CollectionUtils.isEmpty(existingList)) {
                log.debug("物资信息已存在，materialName: {}, classId: {}", materialNew.getMaterialName(), materialNew.getClassId());
                return true;
            }
        }

        return false;
    }
}
