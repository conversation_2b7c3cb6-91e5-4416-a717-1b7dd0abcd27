package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.EnterpriseBankAccounts;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.EnterpriseBankAccounts;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
/**
 * @描述：企业-对公账户表 服务类
 * @作者: ye
 * @日期: 2025-03-06
 */
public interface EnterpriseBankAccountsService extends IService<EnterpriseBankAccounts> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<EnterpriseBankAccounts> queryWrapper);

        void create(EnterpriseBankAccounts enterpriseBankAccounts);
        void update(EnterpriseBankAccounts enterpriseBankAccounts);
        EnterpriseBankAccounts getById(String id);

        void delete(String id);

        void deleteBatch( List<String> ids);

    void create1(EnterpriseBankAccounts enterpriseBankAccounts);
}
