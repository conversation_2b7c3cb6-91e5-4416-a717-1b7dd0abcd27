package scrbg.meplat.mall.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2022-11-09 11:14
 */
@Data
public class BuySkuInfoDTO {

    @ApiModelProperty(value = "商品id")
    private String productId;

    @ApiModelProperty(value = "skuId")
    private String skuId;

    @ApiModelProperty(value = "购买数量")
    private BigDecimal buyNum;

    @ApiModelProperty(value = "租赁时长")

    private BigDecimal leaseNum;

}
