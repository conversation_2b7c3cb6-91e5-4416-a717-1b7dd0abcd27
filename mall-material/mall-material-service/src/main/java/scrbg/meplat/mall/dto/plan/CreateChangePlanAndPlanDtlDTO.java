package scrbg.meplat.mall.dto.plan;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.MaterialMonthSupplyPlanChange;
import scrbg.meplat.mall.entity.MaterialMonthSupplyPlanDtlChange;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-07-04 15:04
 */
@Data
public class CreateChangePlanAndPlanDtlDTO extends MaterialMonthSupplyPlanChange {


        @ApiModelProperty(value = "是否提交")
        private Integer isSubmit;

        @ApiModelProperty(value = "变更计划明细")
        private List<MaterialMonthSupplyPlanDtlChange> dtls;
}
