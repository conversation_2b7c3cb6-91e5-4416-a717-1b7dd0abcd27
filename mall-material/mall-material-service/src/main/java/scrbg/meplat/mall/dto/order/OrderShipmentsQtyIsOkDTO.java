package scrbg.meplat.mall.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023-11-02 13:36
 */
@Data
public class OrderShipmentsQtyIsOkDTO {

    @ApiModelProperty(value = "订单项id")
    private String orderItemId;

    @ApiModelProperty(value = "发货单数量")
    private BigDecimal qty;


    @ApiModelProperty(value = "发货单项Id（修改才传输）")
    private String dtlId;

    @ApiModelProperty(value = "是否是修改")
    private boolean isUpdate;
}
