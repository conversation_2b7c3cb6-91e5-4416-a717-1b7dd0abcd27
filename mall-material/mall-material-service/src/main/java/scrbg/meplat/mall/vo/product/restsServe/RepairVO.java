package scrbg.meplat.mall.vo.product.restsServe;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.File;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-11-29 12:55
 */
@Data
public class RepairVO {

    /**
     * product
     */
    @ApiModelProperty(value = "商品id")
    private String productId;

    @ApiModelProperty(value = "商品编码")
    private String serialNum;

    @ApiModelProperty(value = "商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备 6维修服务 7金融服务 8保险服务")
    private Integer productType;

    @ApiModelProperty(value = "商品名称")
    private String productName;
    @ApiModelProperty(value = "商品简介")
    private String productIntro;

    @ApiModelProperty(value = "分类id")
    private String classId;

    @ApiModelProperty(value = "分类路径")
    private List<String> classPath;

    @ApiModelProperty(value = "商品的最低价")
    private BigDecimal productMinPrice;

    @ApiModelProperty(value = "省")
    private String province;

    @ApiModelProperty(value = "市")
    private String city;

    @ApiModelProperty(value = "县、区")
    private String county;

    @ApiModelProperty(value = "详细地址")
    private String detailedAddress;

    @ApiModelProperty(value = "商品描述")
    private String productDescribe;

    @ApiModelProperty(value = "商品状态（0待上架 1已上架 2已下架）")

    private Integer state;
    /**
     * file
     */
    @ApiModelProperty(value = "商品主图")
    private List<File> adminFile;

    /**
     * 其他
     */

    @ApiModelProperty(value = "服务区域")
    private String coverage;

    @ApiModelProperty(value = "客户电话")
    private String clientPhone;

    @ApiModelProperty(value = "服务范围")
    private String serveScope;

    @ApiModelProperty(value = "信息来源")
    private String infoSource;



}
