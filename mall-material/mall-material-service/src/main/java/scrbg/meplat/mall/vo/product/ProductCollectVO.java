package scrbg.meplat.mall.vo.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProductCollectVO {
    @ApiModelProperty(value = "收藏id")
    private String collectId;
    //商品id
    @ApiModelProperty(value = "商品id")
    private String productId;
    //图片地址
    @ApiModelProperty(value = "图片地址")
    private String pictureUrl;
    //型号
    @ApiModelProperty(value = "型号")
    private String title;
    //价格
    @ApiModelProperty(value = "价格")
    private BigDecimal price;
    //库存
    @ApiModelProperty(value = "库存")
    private BigDecimal remain;
}
