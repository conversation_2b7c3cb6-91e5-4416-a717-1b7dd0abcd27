package scrbg.meplat.mall.controller.website.userCenter;

import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.ProductComment;
import scrbg.meplat.mall.service.ProductCommentService;

/**
 * @描述：商品评价控制类
 * @作者: y
 * @日期: 2022-11-02
 */
@RestController
@RequestMapping("/userCenter/productComment")
@Api(tags = "商品评价(个人中心)")
public class UserCenterProductCommentController {

    @Autowired
    public ProductCommentService productCommentService;

    @PostMapping("/create")
    @ApiOperation(value = "新增评价")
    public R save(@RequestBody ProductComment productComment) {
        productCommentService.create(productComment);
        return R.success();
    }
    @PostMapping("/update")
    @ApiOperation(value = "修改评价")
    public R updateComment(@RequestBody ProductComment productComment) {
        productCommentService.updateComment(productComment);
        return R.success();
    }

    @GetMapping("/getCommentByOrderItemId")
    @ApiOperation(value = "获取评价根据订单项id")
    public R getCommentByOrderItemId(String orderItemId) {
        ProductComment productComment =  productCommentService.getCommentByOrderItemId(orderItemId);
        return R.success(productComment);
    }
    @GetMapping("/deleteComment")
    @ApiOperation(value = "删除评价")
    public R deleteComment(String orderItemId) {
        productCommentService.deleteComment(orderItemId);
        return R.success();
    }
}

