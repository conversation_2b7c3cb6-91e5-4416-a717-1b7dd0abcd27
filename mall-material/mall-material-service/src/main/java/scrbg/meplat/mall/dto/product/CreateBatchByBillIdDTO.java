package scrbg.meplat.mall.dto.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-11-16 16:48
 */
@Data
public class CreateBatchByBillIdDTO {

    @ApiModelProperty(value = "商品名称")
    private String materialName;

//    @ApiModelProperty(value = "类别路径")
//    private String classNamePath;

    @ApiModelProperty(value = "类别id")
    private String classId;

    @ApiModelProperty(value = "类别名称")
    private String className;

    @ApiModelProperty(value = "规格")
    private String spec;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "关联外部id")
    private String billId;

    @ApiModelProperty(value = "关联外部编号")
    private String billNo;

}
