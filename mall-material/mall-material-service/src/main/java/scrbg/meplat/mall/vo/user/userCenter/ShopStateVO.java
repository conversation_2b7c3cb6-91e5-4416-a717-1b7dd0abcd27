package scrbg.meplat.mall.vo.user.userCenter;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022-12-30 17:17
 */
@Data
public class ShopStateVO {

    @ApiModelProperty(value = "是否自营：1：是  0：否（默认：0）")

    private Integer isBusiness;
    @ApiModelProperty(value = "店铺id")
    private String shopId;

    @ApiModelProperty(value = "店铺类别（1普通店铺2多供方店铺）")
    private Integer shopClass;

    @ApiModelProperty(value = "店铺审核状态： 1：审核通过  2：未审核  3：审核未通过 ")

    private Integer auditStatus;

    @ApiModelProperty(value = "其他服务权限")
    private Map<String, Integer> isOtherAuth;
    @ApiModelProperty(value = "店铺状态 1:启用 0停用")

    private Integer state;
}


