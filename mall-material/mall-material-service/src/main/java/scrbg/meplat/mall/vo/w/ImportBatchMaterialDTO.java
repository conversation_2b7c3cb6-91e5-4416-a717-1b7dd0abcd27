package scrbg.meplat.mall.vo.w;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.dto.product.material.CuterCreateMaterialDTO;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-05-05 16:40
 */
@Data
public class ImportBatchMaterialDTO {


    @ApiModelProperty(value = "商品列表",required = true)
    private List<CuterCreateMaterialDTO> productList;
}
