package scrbg.meplat.mall.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023-10-13 9:44
 */
@Data
public class GetSynthesizeTemporaryPlanDetailItemVO {

    @ApiModelProperty(value = "大宗临购单明细id")
    private String synthesizeTemporaryDtlId;

    @ApiModelProperty(value = "大宗临购单id")

    private String synthesizeTemporaryId;


    @ApiModelProperty(value = "商品id")

    private String productId;


    @ApiModelProperty(value = "商品编号")

    private String productSn;

    @ApiModelProperty(value = "商品名称")

    private String productName;



    @ApiModelProperty(value = "基础库物资id")

    private String materialId;
    @ApiModelProperty(value = "基础库物资编号")

    private String materialSn;
    @ApiModelProperty(value = "基础库物资名称")

    private String materialName;


    @ApiModelProperty(value = "商品供应商机构id")

    private String supplierOrgId;

    @ApiModelProperty(value = "商品供应商机构名称")

    private String supplierName;


    @ApiModelProperty(value = "分类id")

    private String classId;


    @ApiModelProperty(value = "分类名称")

    private String className;


    @ApiModelProperty(value = "分类id路径（xxx/xxx/xx）")

    private String classIdPath;


    @ApiModelProperty(value = "分类name路径（xxx/xxx/xx）")

    private String classNamePath;


    @ApiModelProperty(value = "规格型号")

    private String spec;


    @ApiModelProperty(value = "材质")

    private String texture;


    @ApiModelProperty(value = "品牌id")

    private String brandId;


    @ApiModelProperty(value = "品牌名称")

    private String brandName;


    @ApiModelProperty(value = "计量单位")

    private String unit;


    @ApiModelProperty(value = "数量")

    private BigDecimal qty;


    @ApiModelProperty(value = "重量（废弃）")

    private BigDecimal weightNum;

    @ApiModelProperty(value = "成本价")

    private BigDecimal costPrice;

    @ApiModelProperty(value = "参考单价（商品的参考价（不会变化））")

    private BigDecimal referencePrice;


    @ApiModelProperty(value = "综合单价（会变化，网价+固定费 或者 出厂价+运杂费），新增时是商品参考价（销售价）")

    private BigDecimal synthesizePrice;


    @ApiModelProperty(value = "网价（浮动价格使用）")

    private BigDecimal netPrice;

    @ApiModelProperty(value = "固定费用（浮动价格使用）")

    private BigDecimal fixationPrice;

    @ApiModelProperty(value = "出厂价（固定价格使用）")

    private BigDecimal outFactoryPrice;

    @ApiModelProperty(value = "运杂费（固定价格使用）")

    private BigDecimal transportPrice;



    @ApiModelProperty(value = "参考金额（不会变化）")

    private BigDecimal referenceAmount;


    @ApiModelProperty(value = "综合金额")

    private BigDecimal synthesizeAmount;


    /**
     * pcwp计划明细
     */


    @ApiModelProperty(value = "大宗临购计划明细id")

    private String dtlId;

    @ApiModelProperty(value = "大宗临购计划id")

    private String billId;


    @ApiModelProperty(value = "大宗临购金额（不含税）")

    private BigDecimal amount;

    @ApiModelProperty(value = "商城大宗临购明细id")

    private String sourceDtlId;

    @ApiModelProperty("消耗金额")
    public BigDecimal consumeAmount;

    @ApiModelProperty("未消耗金额")
    public BigDecimal notConsumeAmount;


    @ApiModelProperty("已下单数量")
    public BigDecimal receivedQuantity;


    @ApiModelProperty("已收货数量")
    public BigDecimal confirmCounts;


    @ApiModelProperty(value = "是否有二级单位")

    private Integer isTwoUnit;

    @ApiModelProperty(value = "二级单位")

    private String twoUnit;

    @ApiModelProperty(value = "二级单位购买数量")

    private BigDecimal twoUnitNum;

    @ApiModelProperty(value = "临购副单位对应主单位数量系数")

    private BigDecimal secondUnitNum;









    // 订单使用

    @ApiModelProperty("提交订单选择的数量")
    public BigDecimal selectQty;

}
