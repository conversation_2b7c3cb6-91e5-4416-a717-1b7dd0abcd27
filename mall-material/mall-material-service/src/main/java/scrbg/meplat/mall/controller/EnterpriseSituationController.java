package scrbg.meplat.mall.controller;
import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.mall.config.logRecording.LogRecord;
import scrbg.meplat.mall.config.logRecording.enums.BusinessType;
import scrbg.meplat.mall.config.logRecording.enums.OperatorType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.service.EnterpriseSituationService;
import scrbg.meplat.mall.entity.EnterpriseSituation;

import java.util.List;

/**
 * @描述：企业-情况表控制类
 * @作者: ye
 * @日期: 2025-03-06
 */
@RestController
@RequestMapping("/enterpriseSituation")
@Api(tags = "企业-情况表")
public class EnterpriseSituationController{

@Autowired
public EnterpriseSituationService enterpriseSituationService;

@PostMapping("/listByEntity")
@ApiOperation(value = "根据实体属性分页查询")
@DynamicParameters(name = "根据实体属性分页查询", properties = {
        @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
        @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
})
public PageR<EnterpriseSituation> listByEntity(@RequestBody JSONObject jsonObject){
        PageUtils page= enterpriseSituationService.queryPage(jsonObject,new LambdaQueryWrapper<EnterpriseSituation>());
        return PageR.success(page);
        }

@GetMapping("/findById")
@ApiOperation(value = "根据主键查询")
@ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "ID", required = true,
                dataType = "String", paramType = "query")
})
public R<EnterpriseSituation> findById(String id){
    EnterpriseSituation enterpriseSituation = enterpriseSituationService.getById(id);
        return R.success(enterpriseSituation);
        }

@PostMapping("/create")
@ApiOperation(value = "新增")
@LogRecord(title = "xx管理",businessType = BusinessType.INSERT,operatorType = OperatorType.MANAGE)

public R save(@RequestBody EnterpriseSituation enterpriseSituation){
    enterpriseSituationService.create(enterpriseSituation);
        return R.success();
        }

@PostMapping("/update")
@ApiOperation(value = "修改")
@LogRecord(title = "xx管理",businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE)

public R update(@RequestBody EnterpriseSituation enterpriseSituation){
    enterpriseSituationService.update(enterpriseSituation);
        return R.success();
        }

@GetMapping("/delete")
@ApiOperation(value = "根据主键删除")
@ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "ID", required = true,
                dataType = "String", paramType = "query")
})
@LogRecord(title = "xx管理",businessType = BusinessType.DELETE,operatorType = OperatorType.MANAGE)

public R delete(String id){
    enterpriseSituationService.delete(id);
        return R.success();
        }


@PostMapping("/deleteBatch")
@ApiOperation(value = "根据主键批量删除")
@LogRecord(title = "xx管理",businessType = BusinessType.DELETE,operatorType = OperatorType.MANAGE)

public R deleteBatch(@RequestBody List<String> ids){
    enterpriseSituationService.removeByIds(ids);
        return R.success();
        }
        }

