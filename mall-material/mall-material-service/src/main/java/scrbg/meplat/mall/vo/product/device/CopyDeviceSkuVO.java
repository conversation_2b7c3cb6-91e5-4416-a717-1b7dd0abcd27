package scrbg.meplat.mall.vo.product.device;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2022-11-30 10:25
 */
@Data
public class CopyDeviceSkuVO {
    @ApiModelProperty(value = "skuid")
    private String skuId;

    @ApiModelProperty(value = "sku名称")
    private String skuName;

    @ApiModelProperty(value = "成本价")
    private BigDecimal costPrice;

    @ApiModelProperty(value = "原价")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "销售价格")
    private BigDecimal sellPrice;

    @ApiModelProperty(value = "库存")
    private BigDecimal stock;

    @ApiModelProperty(value = "规格单位")
    private String unit;
    @ApiModelProperty(value = "临购副单位")

    private String secondUnit;
    @ApiModelProperty(value = "临购副单位系数")

    private BigDecimal secondUnitNum;
    @ApiModelProperty(value = "租赁时长")
    private BigDecimal leaseNum;

    @ApiModelProperty(value = "租赁单位（天月年）")
    private String leaseUnit;

    @ApiModelProperty(value = "结算价")
    private BigDecimal settlePrice;

    @ApiModelProperty(value = "区域类型（1全区域,2区域）")
    private Integer isZone;

}
