package scrbg.meplat.mall.vo.user.userCenter;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-01-12 11:07
 */
@Data
public class SettleAccountProductCartVO {


    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "商品信息")
    private List<SettleAccountProductCartChildVO> cartChildVOS;

    @ApiModelProperty(value = "总金额（放在数组第一个）")
    private BigDecimal totalPrice;










}
