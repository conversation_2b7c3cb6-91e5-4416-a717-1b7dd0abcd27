package scrbg.meplat.mall.entity;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.BaseEntity;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：
 * @作者: ye
 * @日期: 2025-03-05
 */
@ApiModel(value="")
@Data
@TableName("user_re_enterprise")
public class UserReEnterprise extends BaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "用户企业关联id")
    private String UserReEnterpriseId;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "企业id")

    private String enterpriseId;


    @ApiModelProperty(value = "机构简码")

    private String shortCode;


    @ApiModelProperty(value = "企业名称")

    private String enterpriseName;

    @ApiModelProperty(value = "机构类型(1:集团|2:分子公司|4:经理部|5:项目部|6:股份|7:事业部)")
    private Integer orgType;

    @ApiModelProperty(value = "远程用户id")
    private String farUserId;


    @ApiModelProperty(value = "角色id")

    private String roleId;


    @ApiModelProperty(value = "默认机构  1是，2 否")
    private Integer institutionStatus;


    @ApiModelProperty(value = "远程机构id")

    private String interiorId;











    @ApiModelProperty(value = "修改人名称")

    private String modifyName;


    @ApiModelProperty(value = "修改人id")

    private String modifyId;





}