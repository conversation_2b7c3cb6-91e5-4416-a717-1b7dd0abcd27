package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.entity.MaterialInfo;

import java.util.List;

/**
 * @描述：物资信息表 Mapper 接口
 * @作者: AI Assistant
 * @日期: 2025-07-25
 */
@Mapper
@Repository
public interface MaterialInfoMapper extends BaseMapper<MaterialInfo> {

    /**
     * 批量插入物资信息
     * @param materialInfoList 物资信息列表
     * @return 插入成功的记录数
     */
    int batchInsert(@Param("list") List<MaterialInfo> materialInfoList);

    /**
     * 根据单据ID和单据编号查询物资信息
     * @param billId 单据ID
     * @param billNo 单据编号
     * @return 物资信息
     */
    MaterialInfo selectByBillIdAndBillNo(@Param("billId") String billId, @Param("billNo") String billNo);

    /**
     * 根据物资名称和类别ID查询物资信息
     * @param materialName 物资名称
     * @param classId 类别ID
     * @return 物资信息列表
     */
    List<MaterialInfo> selectByMaterialNameAndClassId(@Param("materialName") String materialName, @Param("classId") String classId);
}
