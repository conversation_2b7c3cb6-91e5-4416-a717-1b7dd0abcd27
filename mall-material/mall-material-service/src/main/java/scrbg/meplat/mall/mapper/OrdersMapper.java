package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.entity.Orders;
import scrbg.meplat.mall.vo.order.GetShopManageOrderOutZIPDataVO;
import scrbg.meplat.mall.vo.order.GetShopManageOrderOutZIPVO;
import scrbg.meplat.mall.vo.platform.PlatformOrdersCountVO;
import scrbg.meplat.mall.vo.platform.PlatformShopCountVo;
import scrbg.meplat.mall.vo.platform.ShopCountVo;
import scrbg.meplat.mall.vo.user.userCenter.UserOrderPageListVO;

import java.util.List;
import java.util.Map;

/**
 * @描述：商品订单 Mapper 接口
 * @作者: y
 * @日期: 2022-11-02
 */
@Mapper
@Repository
public interface OrdersMapper extends BaseMapper<Orders> {

    /**
     * 查询当前用户的订单
     *
     * @param pages
     * @param dto
     * @return
     */
    List<UserOrderPageListVO> listUserOrderPageList(Page<UserOrderPageListVO> pages, @Param("dto") Map<String, Object> dto);

    /**
     * 查询当前用户的订单
     * @param dto
     * @return
     */
    int listUserOrderPageListCount(@Param("dto") Map<String, Object> dto);

    Orders findOrderByorderSn(@Param("orderSn")String orderSn);

    /**
     * 统计订单每周的数量
     * @param dto
     * @return
     */
    List<PlatformOrdersCountVO> getPlatformWeekOrdersCount(@Param("dto")  Map<String, Object> dto);





    PlatformShopCountVo getPlatformOrderTotalCount(@Param("ew") QueryWrapper<PlatformShopCountVo> q);

    List<ShopCountVo> getPlatformShopOrderCount(IPage<ShopCountVo> page, @Param("ew") QueryWrapper<ShopCountVo> wrapper);

    /**
     * 获取供应商平台订单数据
     * @param eq
     */
    List<GetShopManageOrderOutZIPDataVO> getShopManageOrderOutZIPData(@Param("ew") QueryWrapper<Orders> q);

    List<GetShopManageOrderOutZIPVO> selectOutOrderList(@Param("ew") QueryWrapper<Orders> q);

    List<ShopCountVo> getPlatformShopOrderCountExcel( @Param("ew") QueryWrapper<ShopCountVo> wrapper);
}
