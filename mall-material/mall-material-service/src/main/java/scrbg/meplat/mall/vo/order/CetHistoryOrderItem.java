package scrbg.meplat.mall.vo.order;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.Version;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class CetHistoryOrderItem  {
    @ApiModelProperty(value = "订单id")

    private String orderId;

    @ApiModelProperty(value = "订单号")

    private String orderSn;

    @ApiModelProperty(value = "商品id")

    private String productId;

    @ApiModelProperty(value = "商品编号")

    private String productSn;

    @ApiModelProperty(value = "商品名称")

    private String productName;

    @ApiModelProperty(value = "商品图片")

    private String productImg;

    @ApiModelProperty(value = "skuid")

    private String skuId;

    @ApiModelProperty(value = "sku名称")

    private String skuName;
    @ApiModelProperty(value = "供应商价格")

    private String supplyPrice;

    @ApiModelProperty(value = "商品成本价")

    private BigDecimal costPrice;

    @ApiModelProperty(value = "总成本价")

    private BigDecimal costAmount;

    @ApiModelProperty(value = "原价")
    private BigDecimal originalPrice;


    @ApiModelProperty(value = "综合价格")

    private BigDecimal productPrice;



    @ApiModelProperty(value = "购买数量")

    private BigDecimal buyCounts;

    @ApiModelProperty(value = "确认收货数量")

    private BigDecimal confirmCounts;

    @ApiModelProperty(value = "商品总金额")

    private BigDecimal totalAmount;

    @ApiModelProperty(value = "评论状态： 0 未评价  1 已评价")

    private Integer isComment;

    @ApiModelProperty(value = "购买时间")

    private Date buyTime;

    @ApiModelProperty(value = "订单明细状态（0默认不使用1已选择供方2待分配3待分配竞价4已生成竞价）")

    private Integer state;

    @ApiModelProperty(value = "商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备 10零星采购，11办公用品, 12大宗月供订单13大宗临购")

    private Integer productType;





    @ApiModelProperty(value = "sku单位")

    private String unit;



    @ApiModelProperty(value = "关联计划id（逗号分隔）废弃")
    private String billId;

    @ApiModelProperty(value = "关联计划明细id（逗号分割）废弃")
    private String dtlId;

    @ApiModelProperty(value = "关联名称（用于关联外部的名称唯一不修改）废弃")
    private String relevanceName;




    @ApiModelProperty(value = "利润")

    private BigDecimal profitPrice;

    @ApiModelProperty(value = "发货数量")

    private BigDecimal shipCounts;

    @ApiModelProperty(value = "供应商id（本地机构id）主要用户拆单分组生成订单用（都是取商品实际的供应商id）")

    private String supplierId;

    @ApiModelProperty(value = "供应商名称）")
    @TableField(exist = false)
    private String supplierName;

    @ApiModelProperty(value = "店铺名称）")
    @TableField(exist = false)
    private String shopName;


    @ApiModelProperty(value = "父订单明细id（是拆单订单这里才有值）")

    private String parentOrderItemId;

    @ApiModelProperty(value = "退货数量")

    private BigDecimal returnCounts;

    @ApiModelProperty(value = "分类路径名称（xxx/xxx/xxx）")
    private String classPathName;

    @ApiModelProperty(value = "分类路径id（xxx/xxx/xxx）")
    private String classPathId;

    @ApiModelProperty(value = "分类id")

    private String classId;

    @ApiModelProperty(value = "品牌id")

    private String brandId;

    @ApiModelProperty(value = "品牌名称")

    private String brandName;

    @ApiModelProperty(value = "材质")

    private String texture;
    @ApiModelProperty(value = "完成时间")

    private Date successDate;
    @ApiModelProperty(value = "清单类型（1浮动价格2固定价格）")

    private Integer billType;
    @ApiModelProperty(value = "网价（浮动价格使用）")

    private BigDecimal netPrice;

    @ApiModelProperty(value = "固定费用（浮动价格使用）")

    private BigDecimal fixationPrice;

    @ApiModelProperty(value = "出厂价（固定价格使用）")

    private BigDecimal outFactoryPrice;

    @ApiModelProperty(value = "运杂费（固定价格使用）")

    private BigDecimal transportPrice;
    @ApiModelProperty(value = "收货地址快照")

    private String receiverAddress;







}
