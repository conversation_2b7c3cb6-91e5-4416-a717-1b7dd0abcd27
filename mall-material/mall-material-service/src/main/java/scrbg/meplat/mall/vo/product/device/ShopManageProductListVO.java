package scrbg.meplat.mall.vo.product.device;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2022-11-15 10:10
 */
@Data
public class ShopManageProductListVO extends MustBaseEntity {

    @ApiModelProperty(value = "商品id")

    private String productId;

    @ApiModelProperty(value = "商品名称")

    private String productName;

    @ApiModelProperty(value = "店铺id")

    private String shopId;

    @ApiModelProperty(value = "分类id")

    private String classId;

    @ApiModelProperty(value = "商品描述")

    private String productDescribe;

    @ApiModelProperty(value = "商品状态（0待上架 1已上架 2已下架）")

    private Integer state;

    @ApiModelProperty(value = "商品关键字（,分隔）")

    private String productKeyword;

    @ApiModelProperty(value = "商品运费类型（0商家包邮）")

    private Integer productTransportType;

    @ApiModelProperty(value = "商品的最低价")

    private BigDecimal productMinPrice;

    @ApiModelProperty(value = "商品库id")

    private String productInventoryId;

    @ApiModelProperty(value = "关联外部id")

    private String relevanceId;

    @ApiModelProperty(value = "品牌id")

    private String brandId;

    @ApiModelProperty(value = "商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备")
    private Integer productType;

    // 新加的

    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "上架时间")
    private Date putawayDate;


}
