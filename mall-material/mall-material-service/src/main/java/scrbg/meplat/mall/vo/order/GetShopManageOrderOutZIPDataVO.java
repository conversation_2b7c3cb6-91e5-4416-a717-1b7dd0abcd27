package scrbg.meplat.mall.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-08-18 10:40
 */
@Data
public class GetShopManageOrderOutZIPDataVO {


    @ApiModelProperty(value = "供应商名称（本地机构名称）")
    private String supplierName;

    @ApiModelProperty(value = "本地机构名称，采购员")
    private String enterpriseName;

    @ApiModelProperty(value = "订单明细")
    private List<GetShopManageOrderOutZIPDataItemVO> dataList;


    @ApiModelProperty(value = "订单编号（单个导出使用）")
    private String orderSn;

    @ApiModelProperty(value = "订单创建时间（单个导出使用）")
    private String createDateStr;

    @ApiModelProperty(value = "不含税总金额（小计）str（单个导出使用）")
    private String subtotalMoRateAmountStr;

    @ApiModelProperty(value = "商品总金额（小计）str（单个导出使用）")
    private String subtotalTotalAmountStr;


}
