package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.config.logRecording.LogRecord;
import scrbg.meplat.mall.config.logRecording.enums.BusinessType;
import scrbg.meplat.mall.config.logRecording.enums.OperatorType;
import scrbg.meplat.mall.dto.free.MyQueryPayFreeListByEntity;
import scrbg.meplat.mall.dto.free.PlatformYearAndDealFreePageListVO;
import scrbg.meplat.mall.dto.free.TotalCountFreeVO;
import scrbg.meplat.mall.entity.PlatformDealFee;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.PlatformYearFeeMapper;
import scrbg.meplat.mall.service.PlatformYearFeeService;
import scrbg.meplat.mall.entity.PlatformYearFee;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.countExcel.ExcelForWebUtil;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * @描述：平台年费表控制类
 * @作者: ye
 * @日期: 2024-01-24
 */
@RestController
@RequestMapping("/platformYearFee")
@Api(tags = "平台年费表")
public class PlatformYearFeeController {

    @Autowired
    public PlatformYearFeeService platformYearFeeService;


    @Autowired
    PlatformYearFeeMapper platformYearFeeMapper;


//    @PostMapping("/platform/listPlatformYearAndDealFreePageList")
//    @ApiOperation(value = "平台费用明细")
//    public PageR<PlatformYearAndDealFreePageListVO> listPlatformYearAndDealFreePageList(@RequestBody JSONObject jsonObject) {
//        Page<PlatformYearAndDealFreePageListVO> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
//        IPage<PlatformYearAndDealFreePageListVO> resIPage =  platformYearFeeMapper.listPlatformYearAndDealFreePageList(pages,jsonObject);
//        return PageR.success(new PageUtils<>(resIPage));
//    }


    @PostMapping("/listByEntity")
    @ApiOperation(value = "平台查看年费")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<PlatformYearFee> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = platformYearFeeService.queryPage(jsonObject, new LambdaQueryWrapper<PlatformYearFee>());
        return PageR.success(page);
    }


    @PostMapping("/supplier/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询(供应商)")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<PlatformYearFee> supplierListByEntity(@RequestBody JSONObject jsonObject) {
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        if(StringUtils.isBlank(enterpriseId)) throw new BusinessException(500,"登陆过期");
        LambdaQueryWrapper<PlatformYearFee> q = new LambdaQueryWrapper<>();
        q.eq(PlatformYearFee::getEnterpriseId, enterpriseId);
        PageUtils page = platformYearFeeService.queryPage(jsonObject, q);
        return PageR.success(page);
    }


    @PostMapping("/platform/totalCountFree")
    @ApiOperation(value = "平台统计报表")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<TotalCountFreeVO> totalCountFree(@RequestBody JSONObject jsonObject) {
        PageUtils page = platformYearFeeService.totalCountFree(jsonObject);
        return PageR.success(page);
    }

    @Autowired
    MallConfig mallConfig;
    @PostMapping("/platform/excelTotalCountFree")
    @ApiOperation(value = "导出平台统计报表")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public void excelTotalCountFree(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        ArrayList enterpriseNames = (ArrayList) jsonObject.get("enterpriseNames");
        if(CollectionUtils.isEmpty(enterpriseNames)) {
            jsonObject.put("page",1);
            jsonObject.put("limit",4000);
        }
        PageUtils page = platformYearFeeService.totalCountFree(jsonObject);
        HashMap<String, Object> dataMap = new HashMap<>();
        dataMap.put("dataList",page.getList());
        String src = mallConfig.templateFormUrl;
        try {
            ExcelForWebUtil.exportExcel(response, dataMap, "平台供应商费用报表模板.xlsx", src, "平台供应商费用报表.xlsx");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }



    @PostMapping("/supplier/exportExcelYearFree")
    @ApiOperation(value = "供应商导出excel年费明细")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public void supplierExportExcelYearFree(@RequestBody JSONObject jsonObject,HttpServletResponse response) {
        ArrayList ids = (ArrayList) jsonObject.get("ids");
        if(CollectionUtils.isEmpty(ids)) {
            jsonObject.put("page",1);
            jsonObject.put("limit",4000);
        }
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        if(StringUtils.isBlank(enterpriseId)) throw new BusinessException(500,"登陆过期");
        LambdaQueryWrapper<PlatformYearFee> q = new LambdaQueryWrapper<>();
        q.eq(PlatformYearFee::getEnterpriseId, enterpriseId);
        PageUtils page = platformYearFeeService.queryPage(jsonObject, q);
        HashMap<String, Object> dataMap = new HashMap<>();
        dataMap.put("dataList",page.getList());
        String src = mallConfig.templateFormUrl;
        try {
            ExcelForWebUtil.exportExcel(response, dataMap, "供应商管理年度费用明细模板.xlsx", src, "供应商管理年度费用明细.xlsx");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @PostMapping("/platform/exportExcelYearFree")
    @ApiOperation(value = "平台导出excel年费明细)")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public void platformExportExcelYearFree(@RequestBody JSONObject jsonObject,HttpServletResponse response) {
        ArrayList ids = (ArrayList) jsonObject.get("ids");
        if(CollectionUtils.isEmpty(ids)) {
            jsonObject.put("page",1);
            jsonObject.put("limit",4000);
        }
        LambdaQueryWrapper<PlatformYearFee> q = new LambdaQueryWrapper<>();
        PageUtils page = platformYearFeeService.queryPage(jsonObject, q);
        HashMap<String, Object> dataMap = new HashMap<>();
        dataMap.put("dataList",page.getList());
        String src = mallConfig.templateFormUrl;
        try {
            ExcelForWebUtil.exportExcel(response, dataMap, "供应商管理年度费用明细模板.xlsx", src, "供应商管理年度费用明细.xlsx");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }






    @GetMapping("/findBySn")
    @ApiOperation(value = "根据编号获取数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sn", value = "SN", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<PlatformYearFee> findBySn(String sn) {
        PlatformYearFee platformYearFee = platformYearFeeService.findBySn(sn);
        return R.success(platformYearFee);
    }


    @GetMapping("/platform/findBySn")
    @ApiOperation(value = "平台根据编号获取数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sn", value = "SN", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<PlatformYearFee> platformFindBySn(String sn) {
        PlatformYearFee platformYearFee = platformYearFeeService.platformFindBySn(sn);
        return R.success(platformYearFee);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<PlatformYearFee> findById(String sn) {
        PlatformYearFee platformYearFee = platformYearFeeService.getById(sn);
        return R.success(platformYearFee);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody PlatformYearFee platformYearFee) {
        platformYearFeeService.create(platformYearFee);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody PlatformYearFee platformYearFee) {
        platformYearFeeService.update(platformYearFee);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        platformYearFeeService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        platformYearFeeService.removeByIds(ids);
        return R.success();
    }



}

