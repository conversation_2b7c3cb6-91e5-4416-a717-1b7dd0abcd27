package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import scrbg.meplat.mall.config.logRecording.LogRecord;
import scrbg.meplat.mall.config.logRecording.enums.BusinessType;
import scrbg.meplat.mall.config.logRecording.enums.OperatorType;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.dto.mail.ShopInfoUpdateDTO;
import scrbg.meplat.mall.entity.Role;
import scrbg.meplat.mall.entity.Shop;
import scrbg.meplat.mall.entity.excelTemplate.*;
import scrbg.meplat.mall.service.EnterpriseInfoService;
import scrbg.meplat.mall.service.ShopService;
import scrbg.meplat.mall.util.excel.EasyExcelUtils;
import scrbg.meplat.mall.vo.platform.ShopArrearageStateVo;
import scrbg.meplat.mall.vo.platform.enterprise.EnterpriseArrearageVo;
import scrbg.meplat.mall.vo.product.ImportExcelResultVO;
import scrbg.meplat.mall.vo.product.website.ImportShopExcelResultVO;
import scrbg.meplat.mall.vo.shopManage.ShopInfoUpdateVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @描述：店铺控制类
 * @作者: y
 * @日期: 2022-11-02
 */
@Log4j2
@RestController
@RequestMapping("/platform/shop")
@ApiSort(value = 500)
@Api(tags = "店铺（后台）")
public class ShopController {

    @Autowired
    public ShopService shopService;

    /**
     * 通过主键查询店铺
     *
     * @param id
     * @return 店铺信息
     */
    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<Shop> findById(String id) {
        Shop shop = shopService.getById(id);
        return R.success(shop);
    }


    @GetMapping("/getShopInfo")
    @ApiOperation(value = "查询店铺信息")
    public R<ShopInfoUpdateVO> getShopInfo() {
        ShopInfoUpdateVO vo = shopService.getShopInfo();
        return R.success(vo);
    }

    @PostMapping("/updateShopInfo")
    @ApiOperation(value = "修改店铺信息")
    @LogRecord(title = "店铺管理",businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE,isSaveRequestData = true)

    public R updateShopInfo(@RequestBody ShopInfoUpdateDTO dto) {
        shopService.updateShopInfo(dto);
        return R.success();
    }



    /**
     * 添加店铺并赋予角色
     *
     * @param shop
     * @return R
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @NotResubmit
    public R save(@RequestBody Shop shop,
                  @RequestParam(value = "roles", required = false) List<Role> roles) {
        R r = shopService.create(shop, roles);
        return r;
    }



    @GetMapping("/excel/template")
    @ApiOperation(value = "下载模板")
    @NotResubmit
    public void downloadTemplate(@RequestParam Integer shopState, HttpServletResponse response) {
        try {
            if (shopState == 1) {
                EasyExcelUtils.writeWeb("关闭店铺模板", StopShopExcel.class, null, "关闭店铺模板", response);
            }

            if (shopState == 2) {
                EasyExcelUtils.writeWeb("添加欠费店铺模板", ArrearageShopExcel.class, null, "添加欠费店铺模板", response);
            }
        } catch (Exception e) {
            log.error("异常信息：" + e.getMessage());
        }
    }

    @PostMapping("/shopArrearageExcelFile")
    @ApiOperation(value = "店铺欠费续费Excel导入")
    @NotResubmit
    public R shopArrearageExcelFile(@RequestPart("file") MultipartFile file) {
        List<ImportShopExcelResultVO> vos = shopService.shopArrearageExcelFile(file);
        return R.success(vos);
    }


    @PostMapping("/stopShopStateExcelFile")
    @ApiOperation(value = "店铺启用停用Excel导入")
    @NotResubmit
    public R stopShopStateExcelFile(@RequestPart("file") MultipartFile file) {
        List<ImportShopExcelResultVO> vos = shopService.stopShopStateExcelFile(file);
        return R.success(vos);
    }
    @PostMapping("/update")
    @ApiOperation(value = "修改")
    @LogRecord(title = "店铺管理",businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE,isSaveRequestData = true)

    public R update(@RequestBody Shop shop) {
        shopService.update(shop);
        return R.success();
    }

    @PostMapping("/updateBatchAuditStatus")
    @ApiOperation(value = "批量更新店铺信息")
    // 审核店铺
    @LogRecord(title = "店铺管理",businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE,isSaveRequestData = true)

    public R updateBatchAuditStatus(@RequestBody List<Shop> shops) {
        shopService.updateBatchAuditStatus(shops);
        return R.success();
    }

    @PostMapping("/updateBatchById")
    @LogRecord(title = "店铺管理",businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE,isSaveRequestData = true)

    @ApiOperation(value = "批量更新店铺信息")
    public R update(@RequestBody List<Shop> shops) {
        shopService.updateBatchById(shops);
        return R.success();
    }

    /**
     * 通过主键删除店铺信息（逻辑删除）
     *
     * @param id
     * @return
     */
    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    @LogRecord(title = "店铺管理",businessType = BusinessType.DELETE,operatorType = OperatorType.MANAGE,isSaveRequestData = true)

    public R delete(String id) {
        shopService.delete(id);
        return R.success();
    }

    /**
     * 根据主键批量删除（逻辑删除）
     *
     * @param ids
     * @return
     */
    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    @LogRecord(title = "店铺管理",businessType = BusinessType.DELETE,operatorType = OperatorType.MANAGE,isSaveRequestData = true)

    public R deleteBatch(@RequestBody List<String> ids) {
        shopService.removeByIds(ids);
        return R.success();
    }

    /**
     * 批量发布
     *
     * @param ids
     * @return
     */
    @PostMapping("/updateByPublish")
    @ApiOperation(value = "批量启用")
    @LogRecord(title = "店铺管理",businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE,isSaveRequestData = true)

    public R updatePublish(@RequestBody List<String> ids) {
        shopService.updateByPublish(ids, "1");
        return R.success();
    }

    /**
     * 批量取消发布
     *
     * @param ids
     * @return
     */
    @PostMapping("/updateNotPublish")
    @ApiOperation(value = "批量停用")
    @LogRecord(title = "店铺管理",businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE,isSaveRequestData = true)

    public R updateNotPublish(@RequestBody List<String> ids) {
        shopService.updateByPublish(ids, "0");
        return R.success();
    }

    @Autowired
    public EnterpriseInfoService enterpriseInfoService;
    /**
     * 分页查询(通过审核)
     *
     * @param jsonObject
     * @return
     */
    @PostMapping("/findByConditionByPage")
    @ApiOperation(value = "根据实体属性分页查询(通过审核)")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "shopName", value = "店铺名称", dataTypeClass = String.class),
            @DynamicParameter(name = "shopType", value = "店铺类型", dataTypeClass = Integer.class),
            @DynamicParameter(name = "state", value = "店铺状态", dataTypeClass = Integer.class),
            @DynamicParameter(name = "auditStatus", value = "审核状态", dataTypeClass = Integer.class),
            @DynamicParameter(name = "isBusiness", value = "是否自营", dataTypeClass = Integer.class),
            @DynamicParameter(name = "isSupplier", value = "是否供应商", dataTypeClass = Integer.class),
            @DynamicParameter(name = "isInternalShop", value = "是否内部店铺", dataTypeClass = Integer.class),
            @DynamicParameter(name = "enterpriseId", value = "企业id", dataTypeClass = String.class),
            @DynamicParameter(name = "orderBy", value = "排序方式", dataTypeClass = Integer.class),
    })
    public PageR<Shop> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = shopService.queryPage(jsonObject, new LambdaQueryWrapper<>());
        return PageR.success(page);
    }

    /**
     * 分页查询(未通过审核和未审核)
     *
     * @param jsonObject
     * @return
     */
    @PostMapping("/findByConditionByPageNot")
    @ApiOperation(value = "根据实体属性分页查询(未通过审核和未审核)")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "shopName", value = "店铺名称", dataTypeClass = String.class),
            @DynamicParameter(name = "detailedAddress", value = "店铺地址", dataTypeClass = String.class),
            @DynamicParameter(name = "shopType", value = "店铺类型", dataTypeClass = Integer.class),
            @DynamicParameter(name = "auditStatus", value = "审核状态", dataTypeClass = Integer.class),
            @DynamicParameter(name = "state", value = "店铺状态", dataTypeClass = Integer.class),
            @DynamicParameter(name = "isBusiness", value = "是否自营", dataTypeClass = Integer.class),
            @DynamicParameter(name = "isInternalShop", value = "是否内部店铺", dataTypeClass = Integer.class),
            @DynamicParameter(name = "isInternalSettlement", value = "是否支持内部结算", dataTypeClass = Integer.class),
            @DynamicParameter(name = "orderBy", value = "排序方式", dataTypeClass = Integer.class),
    })
    public PageR<Shop> listByEntityNot(@RequestBody JSONObject jsonObject) {
        PageUtils page = shopService.queryNotPage(jsonObject, new LambdaQueryWrapper<>());
        return PageR.success(page);
    }
    @PostMapping("/updateBatchArrearage")
    @ApiOperation(value = "批量修改欠费数据和可欠费时间")
    public R updateBatchArrearage(@RequestBody ShopArrearageStateVo vo) {
        shopService.updateBatchArrearage(vo);
        return R.success();
    }







}

