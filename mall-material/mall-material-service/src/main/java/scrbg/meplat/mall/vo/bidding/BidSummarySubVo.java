package scrbg.meplat.mall.vo.bidding;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import scrbg.meplat.mall.exception.BusinessException;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @program: maill_api
 * @description: 竞价汇总子对象
 * @author: 代文翰
 * @create: 2023-09-20 16:12
 **/
@Data
public class BidSummarySubVo {
    // yyyy-MM-dd的格式代表了日期部分，你可以根据需要进行修改
    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    @ApiModelProperty(value = "供应商名称")

    private String supplierName;
    @ApiModelProperty(value = "商品名称")

    private String productName;
    @ApiModelProperty(value = "规格型号")

    private String spec;


    @ApiModelProperty(value = "计量单位")

    private String unit;

    @ApiModelProperty(value = "商品材质")

    private String productTexture;
    @ApiModelProperty(value = "数量")

    private BigDecimal num;
    @ApiModelProperty(value = "送货时间")

    private String deliveryDate;

    @ApiModelProperty(value = "不含税到场单价")

    private BigDecimal bidPrice;
    @ApiModelProperty(value = "含税到场单价")

    private BigDecimal bidRatePrice;


    @ApiModelProperty(value = "含税总金额")

    private BigDecimal bidRateAmount;


    @ApiModelProperty(value = "不含税总金额")

    private BigDecimal bidAmount;
    @ApiModelProperty(value = "备注")
    private String remarks;


    public String getDeliveryDate() {
        if (StringUtils.isBlank(this.deliveryDate)) {
            return "";
        }
        // 转换成Date对象
        Date date = null;
        try {
            date = sdf.parse(this.deliveryDate);
        } catch (Exception e) {
           throw new BusinessException("日期格式转换异常");
        }
        // 以"yyyy-MM-dd"格式返回日期字符串
        return sdf.format(date);
    }
}
