package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import org.springframework.web.multipart.MultipartFile;
import scrbg.meplat.mall.config.auth.IsRole;
import scrbg.meplat.mall.config.auth.RoleEnum;
import scrbg.meplat.mall.config.logRecording.LogRecord;
import scrbg.meplat.mall.config.logRecording.enums.BusinessType;
import scrbg.meplat.mall.config.logRecording.enums.OperatorType;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.dto.AuditDTO;
import scrbg.meplat.mall.dto.free.FeeInputTemplateDTO;
import scrbg.meplat.mall.dto.free.MyQueryPayFreeListByEntity;
import scrbg.meplat.mall.dto.payment.SdImportDto;
import scrbg.meplat.mall.entity.PlatformYearFee;
import scrbg.meplat.mall.entity.Shop;
import scrbg.meplat.mall.entity.SystemParam;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.PlatformYearFeeRecordService;
import scrbg.meplat.mall.entity.PlatformYearFeeRecord;
import scrbg.meplat.mall.service.PlatformYearFeeService;
import scrbg.meplat.mall.service.ShopService;
import scrbg.meplat.mall.service.SystemParamService;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.excel.EasyExcelUtils;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @描述：年费缴费记录控制类
 * @作者: ye
 * @日期: 2024-01-24
 */
@RestController
@RequestMapping("/platformYearFeeRecord")
@Api(tags = "年费缴费记录")
public class PlatformYearFeeRecordController {

    @Autowired
    public PlatformYearFeeRecordService platformYearFeeRecordService;

    @PostMapping("/platformQueryListByEntity")
    @ApiOperation(value = "平台查询缴费记录")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "shopName", value = "店铺名称", dataTypeClass = String.class),
            @DynamicParameter(name = "paymentRecordUn", value = "编号", dataTypeClass = String.class),
            @DynamicParameter(name = "enterpriseName", value = "企业名称", dataTypeClass = String.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
            @DynamicParameter(name = "startGmtCreate", value = "开始创建时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endGmtCreate", value = "结束创建时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startGmtModified", value = "开始修改时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endGmtModified", value = "结束修改时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startAuditTime", value = "开始审核时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endAuditTime", value = "结束审核时间", dataTypeClass = String.class),
            @DynamicParameter(name = "orderBy", value = "排序", dataTypeClass = Integer.class),
            @DynamicParameter(name = "states", value = "状态", dataTypeClass = List.class),
            @DynamicParameter(name = "recordType", value = "缴费记类型（1店铺年费2招标年费）", dataTypeClass = List.class),
    })
    public PageR<PlatformYearFeeRecord> platformQueryListByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = platformYearFeeRecordService.platformQueryListByEntity(jsonObject, new LambdaQueryWrapper<PlatformYearFeeRecord>());
        return PageR.success(page);
    }


    @PostMapping("/myQueryListByEntity")
    @ApiOperation(value = "查詢我的繳費记录")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
            @DynamicParameter(name = "startGmtCreate", value = "开始创建时间", dataTypeClass = String.class),
            @DynamicParameter(name = "paymentRecordUn", value = "编号", dataTypeClass = String.class),
            @DynamicParameter(name = "endGmtCreate", value = "结束创建时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startGmtModified", value = "开始修改时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endGmtModified", value = "结束修改时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startAuditTime", value = "开始审核时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endAuditTime", value = "结束审核时间", dataTypeClass = String.class),
            @DynamicParameter(name = "orderBy", value = "排序", dataTypeClass = Integer.class),
            @DynamicParameter(name = "states", value = "状态", dataTypeClass = List.class),
            @DynamicParameter(name = "recordType", value = "缴费记类型（1店铺年费2招标年费）", dataTypeClass = List.class),
    })
    public PageR<PlatformYearFeeRecord> myQueryListByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = platformYearFeeRecordService.myQueryListByEntity(jsonObject, new LambdaQueryWrapper<PlatformYearFeeRecord>());
        return PageR.success(page);
    }




    @PostMapping("/myQueryPayFreeListByEntity")
    @ApiOperation(value = "供应商查询缴费管理列表")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
            @DynamicParameter(name = "startGmtCreate", value = "开始创建时间", dataTypeClass = String.class),
            @DynamicParameter(name = "paymentRecordUn", value = "编号", dataTypeClass = String.class),
            @DynamicParameter(name = "endGmtCreate", value = "结束创建时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startGmtModified", value = "开始修改时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endGmtModified", value = "结束修改时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startAuditTime", value = "开始审核时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endAuditTime", value = "结束审核时间", dataTypeClass = String.class),
            @DynamicParameter(name = "orderBy", value = "排序", dataTypeClass = Integer.class),
            @DynamicParameter(name = "states", value = "状态", dataTypeClass = List.class),
            @DynamicParameter(name = "recordType", value = "缴费记类型（1店铺年费2招标年费3店铺交易服务费4合同履约交易服务费）", dataTypeClass = List.class),
    })
    public PageR<MyQueryPayFreeListByEntity> myQueryPayFreeListByEntity(@RequestBody JSONObject jsonObject) {
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        if (StringUtils.isBlank(enterpriseId)) {
            throw new BusinessException("登陆过期！");
        }
        jsonObject.put("enterpriseId",enterpriseId);
        PageUtils page = platformYearFeeRecordService.myQueryPayFreeListByEntity(jsonObject, new LambdaQueryWrapper<PlatformYearFeeRecord>());
        return PageR.success(page);
    }







    @PostMapping("/platformQueryYearAndDealFreeListByEntity")
    @ApiOperation(value = "平台查询缴费记录（包括交易）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "shopName", value = "店铺名称", dataTypeClass = String.class),
            @DynamicParameter(name = "paymentRecordUn", value = "编号", dataTypeClass = String.class),
            @DynamicParameter(name = "enterpriseName", value = "企业名称", dataTypeClass = String.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
            @DynamicParameter(name = "startGmtCreate", value = "开始创建时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endGmtCreate", value = "结束创建时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startGmtModified", value = "开始修改时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endGmtModified", value = "结束修改时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startAuditTime", value = "开始审核时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endAuditTime", value = "结束审核时间", dataTypeClass = String.class),
            @DynamicParameter(name = "orderBy", value = "排序", dataTypeClass = Integer.class),
            @DynamicParameter(name = "states", value = "状态", dataTypeClass = List.class),
            @DynamicParameter(name = "recordType", value = "缴费记类型（1店铺年费2招标年费3店铺交易服务费4合同履约交易服务费）", dataTypeClass = List.class),
    })
    public PageR<MyQueryPayFreeListByEntity> platformQueryYearAndDealFreeListByEntity(@RequestBody JSONObject jsonObject) {
        jsonObject.put("neState",0);
        PageUtils page = platformYearFeeRecordService.platformQueryYearAndDealFreeListByEntity(jsonObject, new LambdaQueryWrapper<PlatformYearFeeRecord>());
        return PageR.success(page);
    }

    @Autowired
    SystemParamService systemParamService;

    @Autowired
    PlatformYearFeeService platformYearFeeService;

    @GetMapping("/getPlatformFreeAccountAndAddress")
    @ApiOperation(value = "获取平台收费账号及开户行")
    public R<Map> getPlatformFreeAccountAndAddress() {
        HashMap<String, Object> map = new HashMap<>();
        SystemParam s1 = systemParamService
                .lambdaQuery()
                .eq(SystemParam::getCode, PublicEnum.PLATFORMFREEYHADDRESS.getRemark()).one();
        map.put("platformFreeyhAddress",s1.getKeyValue());
        SystemParam s2 = systemParamService
                .lambdaQuery()
                .eq(SystemParam::getCode, PublicEnum.PLATFORMFREEYHACCOUNT.getRemark()).one();
        map.put("platformFreeyhAccount",s2.getKeyValue());
        SystemParam s3 = systemParamService
                .lambdaQuery()
                .eq(SystemParam::getCode, PublicEnum.PLATFORMFREEYHORGNAME.getRemark()).one();
        map.put("platformFreeyhOrgName",s3.getKeyValue());

        map.put("enterpriseName",ThreadLocalUtil.getCurrentUser().getEnterpriseName());

        // 查询店铺和招标到期时间
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        PlatformYearFee one = platformYearFeeService.lambdaQuery().eq(PlatformYearFee::getEnterpriseId, enterpriseId)
                .eq(PlatformYearFee::getServeType, 1).one();
        if(one != null) {
            map.put("shopYearServerEndTime",one.getServeEndTime());
        }
        PlatformYearFee one2 = platformYearFeeService.lambdaQuery().eq(PlatformYearFee::getEnterpriseId, enterpriseId)
                .eq(PlatformYearFee::getServeType, 2).one();
        if(one2 != null) {
            map.put("bidYearServerEndTime",one2.getServeEndTime());
        }
        return R.success(map);
    }

    @Autowired
    ShopService shopService;

    @GetMapping("/getIsYearServe")
    @ApiOperation(value = "检查是否开通年费相关的服务")
    public R<Map> getIsYearServe() {
        HashMap<String, Object> map = new HashMap<>();
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        Integer count = shopService.lambdaQuery()
                .eq(Shop::getEnterpriseId, enterpriseId)
                .eq(Shop::getAuditStatus, 1)
                .count();
        if(count > 0) {
            map.put("isShopFeeServeBtn",1);
        }

        PlatformYearFee one2 = platformYearFeeService.lambdaQuery().eq(PlatformYearFee::getEnterpriseId, enterpriseId)
                .eq(PlatformYearFee::getServeType, 2).one();
        if(one2 != null && one2.getServeEndTime() != null) {
            map.put("isBidFeeServeBtn",1);
        }
        return R.success(map);
    }



    @GetMapping("/excel/feeInputTemplate")
    @ApiOperation(value = "缴费导入模板")
    public void feeInputTemplate(HttpServletResponse response) {
        try {
            EasyExcelUtils.writeWeb("缴费导入", FeeInputTemplateDTO.class, null, "缴费导入模板", response);
        } catch (Exception e) {
            throw new BusinessException("模板下载失败" + e.getMessage());
        }
    }

    @PostMapping("/excel/feeInputData")
    @ApiOperation(value = "导入缴费信息")
    public void feeInputData(@RequestPart("file") MultipartFile file, HttpServletResponse response) {
        platformYearFeeRecordService.feeInputData(file,response);
    }




    @GetMapping("/findBySn")
    @ApiOperation(value = "根据编号查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sn", value = "sn", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<PlatformYearFeeRecord> findBySn(String sn) {
        PlatformYearFeeRecord platformYearFeeRecord = platformYearFeeRecordService.findBySn(sn);
        return R.success(platformYearFeeRecord);
    }

    @PostMapping("/supplier/createFee")
    @ApiOperation(value = "供应商新增缴费记录")
    @NotResubmit
    public R supplierCreateFee(@RequestBody PlatformYearFeeRecord platformYearFeeRecord) {
        String un = platformYearFeeRecordService.supplierCreateFee(platformYearFeeRecord);
        return R.success(un);
    }

    @PostMapping("/supplier/createFeeAndDealFree")
    @ApiOperation(value = "供应商新增缴费（包括交易和年费）")
    @NotResubmit
    public R createFeeAndDealFree(@RequestBody PlatformYearFeeRecord platformYearFeeRecord) {
        String un = platformYearFeeRecordService.createFeeAndDealFree(platformYearFeeRecord);
        return R.success(un);
    }

    @PostMapping("/supplier/updateFee")
    @ApiOperation(value = "修改缴费")
    public R supplierUpdateFee(@RequestBody PlatformYearFeeRecord platformYearFeeRecord) {
        platformYearFeeRecordService.supplierUpdateFee(platformYearFeeRecord);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        platformYearFeeRecordService.delete(id);
        return R.success();
    }


    @PostMapping("/platform/auditFee")
    @ApiOperation(value = "审核缴费服务年费缴费")
    @NotResubmit
//    @IsRole(roleName = RoleEnum.ROLE_8)
    public R auditFee(@RequestBody AuditDTO dto) {
        platformYearFeeRecordService.auditFee(dto);
        return R.success();
    }

    @GetMapping("/reCreateContract")
    @ApiOperation("重新生成合同文件")
    @LogRecord(title = "缴费管理",businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE)

    public R reCreateContract(String shopId,Boolean contractFileCheck){
        String contractNo = platformYearFeeRecordService.reCreateContract(shopId,contractFileCheck);
        return R.success(Collections.singletonMap("contractNo", contractNo));
    }









}

