package scrbg.meplat.mall.vo.ship;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.OrderReturnItem;

@Data
public class OrderReturnItemPCWPVo extends OrderReturnItem {
    @ApiModelProperty(value = "发货供应商企业id")

    private String shipEnterpriseId;
    @ApiModelProperty(value = "发货供应商企业名称")

    private String shipEnterpriseName;

    @ApiModelProperty(value = "商铺名称")
    private String shopName;

    @ApiModelProperty(value = "商铺Id")
    private String shopId;

    @ApiModelProperty(value = "商铺Id")
    private String dtlId;

}
