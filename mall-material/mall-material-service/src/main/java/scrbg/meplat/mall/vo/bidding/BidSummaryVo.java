package scrbg.meplat.mall.vo.bidding;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.List;

/**
 * @program: maill_api
 * @description: 竞价汇总导出对象
 * @author: 代文翰
 * @create: 2023-09-20 16:09
 **/
@Data
public class BidSummaryVo {
    @ApiModelProperty(value = "供应商名称")

    private String supplierName;
    @ApiModelProperty(value = "报价明细")
    private List<BidSummarySubVo> products;
}
