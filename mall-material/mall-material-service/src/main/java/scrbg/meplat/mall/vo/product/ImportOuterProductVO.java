package scrbg.meplat.mall.vo.product;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-03-23 14:27
 */
@Data
@ApiModel(value = "导入失败列表")
public class ImportOuterProductVO {

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "商品编码（慧采商城编码）")
    private String productCode;
    @ApiModelProperty(value = "商品编码（外部商品编码）")
    private String outerProductCode;

    @ApiModelProperty(value = "消息内容")
    private String message;

    @ApiModelProperty(value = "返回码，200:成功：500:未知异常 50090：分类不存在，50091：商品保存失败，50092：" +
            "主图保存失败，50093：商品图片保存失败，50094：" +
            "小图保存失败, 50095：物资基础库该分类下不存在该物资, 50096: 物资基础库接口调用未知异常, " +
            "50097: 未配置供应商id, 50098: 对应分类品牌不存在，50099：只能导入低值易耗品分类商品，" +
            " 50100: 商品名称重复！，50101: 商城分类和pcwp分类不统一！ 50102：商品描述内容过大")
    private Integer code;
}
