package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @描述：店铺商品信息
 * @作者: y
 * @日期: 2022-11-25
 */
@ApiModel(value = "店铺商品信息")
@Data
@TableName("product")
public class Product extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "商品id")
    private String productId;

    @ApiModelProperty(value = "商品编码")
    private String serialNum;

    @ApiModelProperty(value = "商品名称")

    private String productName;

    @ApiModelProperty(value = "商品简介")
    private String productIntro;

    @ApiModelProperty(value = "店铺id")

    private String shopId;

    @ApiModelProperty(value = "店铺名称")
    @TableField(exist = false)
    private String shopName;


    @ApiModelProperty(value = "分类id")

    private String classId;


    @ApiModelProperty(value = "商品描述")

    private String productDescribe;

    // 需要注意现在页面上基本查询商城都是传的product：0，如果新增了大宗临购需要检查首页是否展示大宗临购商品（待确认）
    @ApiModelProperty(value = "商品类型：0物资 （所有商品都是物资，只有下单才会根据分类自动生成不同的订单）最新改动：商品类型：0 低值易耗品 1大宗临购")

    private Integer productType;
    @ApiModelProperty(value = "商品材质")

    private String productTexture;


    @ApiModelProperty(value = "商品状态（0待上架 1已上架 2已下架 3 待审核 4审核失败）")

    private Integer state;
    @ApiModelProperty(value = "商品所属店铺类型（1自营店铺2内部店铺3外部店铺）")

    private Integer shopType;


    @ApiModelProperty(value = "商品关键字（,分隔）")

    private String productKeyword;


    @ApiModelProperty(value = "商品运费类型（0商家包邮）")

    private Integer productTransportType;


    @ApiModelProperty(value = "商品的最低价")

    private BigDecimal productMinPrice;


    @ApiModelProperty(value = "商品库id")

    private String productInventoryId;


    @ApiModelProperty(value = "关联外部id")

    private String relevanceId;



    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;


    @ApiModelProperty(value = "品牌id")

    private String brandId;


    @ApiModelProperty(value = "品牌名称冗余")

    private String brandName;
    @ApiModelProperty(value = "上架时间")
    private Date putawayDate;


    @ApiModelProperty(value = "销量")

    private BigDecimal soldNum;


    @ApiModelProperty(value = "商品访问量")

    private Integer productVisitNum;


    @ApiModelProperty(value = "省")

    private String province;


    @ApiModelProperty(value = "市")

    private String city;


    @ApiModelProperty(value = "县、区")

    private String county;


    @ApiModelProperty(value = "详细地址")

    private String detailedAddress;


    @ApiModelProperty(value = "经度")

    private BigDecimal longitude;


    @ApiModelProperty(value = "纬度")

    private BigDecimal latitude;


    @ApiModelProperty(value = "店铺排序")

    private Integer shopSort;


    @ApiModelProperty(value = "综合排序")

    private Integer synthesisSort;


    @ApiModelProperty(value = "商品小图")

    private String productMinImg;

    @ApiModelProperty(value = "分类id路径/分割")

    private String classPath;

    @ApiModelProperty(value = "关联名称（用于关联外部的名称唯一不修改）")

    private String relevanceName;

    @ApiModelProperty(value = "关联编号")

    private String relevanceNo;

    @ApiModelProperty(value = "是否完成商品编辑（0否1是）")

    private Integer isCompletion;

    @ApiModelProperty(value = "出厂日期")
    private Date leaveFactory;

    @ApiModelProperty(value = "质量、成色")
    private Integer quality;
    @ApiModelProperty(value = "是否周材")
    private Integer isWeekMaterials;
    @ApiModelProperty(value = "图标题（保存在数组第一个）")
    @TableField(
            exist = false
    )
    List<String> labelTitle;

    @ApiModelProperty(value = "图数量（保存在数组第一个）")
    @TableField(
            exist = false
    )
    List<Integer> count;

    @ApiModelProperty(value = "审核失败原因")
    private String failReason;


    @ApiModelProperty(value = "是否外部导入商品")
    private Integer isOpenImport;

    @ApiModelProperty(value = "供应商冗余")
    private String supperBy;

    @ApiModelProperty(value = "是否维修")
    private String isMaintain;
    @ApiModelProperty(value = "规格冗余")
    private String skuName;

    @ApiModelProperty(value = "提交状态（0默认1待提交2待确认3已确认4已拒绝）供方使用，")
    private Integer supplierSubmitState;



    @ApiModelProperty(value = "提交失败原因")
    private String supplierSubmitError;

    @ApiModelProperty(value = "供方名称冗余")
    private String supplierName;

    @ApiModelProperty(value = "是否不显示（0显示1不显示）默认0")
    private Integer showState;


    @ApiModelProperty(value = "库存")
    @TableField(exist = false)
    private BigDecimal stock;

    @ApiModelProperty(value = "成本价")
    @TableField(exist = false)
    private BigDecimal costPrice;


    @ApiModelProperty(value = "原价")
    @TableField(exist = false)
    private BigDecimal originalPrice;


    @ApiModelProperty(value = "销售价格")
    @TableField(exist = false)
    private BigDecimal sellPrice;
    @ApiModelProperty(value = "平均价格（预警商品专用）")
    @TableField(exist = false)
    private BigDecimal averageSellPrice;

    @ApiModelProperty(value = "差价")
    @TableField(exist = false)
    private BigDecimal profitPrice;

    @ApiModelProperty(value = "单位")
    @TableField(exist = false)
    private String unit;

    @ApiModelProperty(value = "上架时间（导出）")
    @TableField(exist = false)
    private String putawayDateStr;
    @ApiModelProperty(value = "创建时间（导出）")
    @TableField(exist = false)
    private String gmtCreateStr;

    @ApiModelProperty(value = "区域类型（1全区域,2区域）")
    @TableField(exist = false)
    private Integer isZone;


    @ApiModelProperty(value = "分类id路径/分割")
    @TableField(exist = false)
    private String classPathName;


    @ApiModelProperty(value = "商品编码（外部商品编码）")
    private String outerProductCode;


    /**
     * 自营店商品id,
     */
    @ApiModelProperty(value = "自营店商品id")
    private String outKeyId;

    @ApiModelProperty(value = "物资版本id")
    private String classVersionId;
    @ApiModelProperty(value = "物资版本名称")
    private String classVersionName;
    @ApiModelProperty(value = "物资版本")
    private String classVersion;

    /**
     * 1：已处理；-1：未处理
     */
    private Integer isHandle;

    private Date handleTime;

    private String fromProductId;


}
