package scrbg.meplat.mall.vo.product.website;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.vo.product.website.material.WMaterialBaseVo;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2022-11-04 16:56
 */
@Data
public class WCartItemVO   {
    @ApiModelProperty(value = "购物车id")
    private String cartId;

    @ApiModelProperty(value = "商品id")
    private String productId;

    @ApiModelProperty(value = "skuId")
    private String skuId;

    @ApiModelProperty(value = "添加购物车时商品价格")

    private BigDecimal productPrice;




    @ApiModelProperty(value = "购买数量")
    private BigDecimal cartNum;

    @ApiModelProperty(value = "添加购物车时间")
    private Date cartTime;

    @ApiModelProperty(value = "选择的规格")
    private String skuProps;

    @ApiModelProperty(value = "小计")
    private BigDecimal numTotalPrice;

    @ApiModelProperty(value = "商品小图")
    private String productMinImg;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;


    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "分类id")
    private String classId;

    @ApiModelProperty(value = "品牌id")

    private String brandId;

    @ApiModelProperty(value = "品牌名称")

    private String brandName;

    @ApiModelProperty(value = "分类名称")
    private String className;

    @ApiModelProperty(value = "商品类型")
    private Integer productType;

    @ApiModelProperty(value = "商品状态（0待上架 1已上架 2已下架）")
    private Integer state;

    @ApiModelProperty(value = "商品的最低价")
    private BigDecimal productMinPrice;

    @ApiModelProperty(value = "商品关键字（,分隔）")
    private String productKeyword;

    @ApiModelProperty(value = "商品运费类型（0商家包邮）")
    private Integer productTransportType;



    @ApiModelProperty(value = "租赁时长")

    private BigDecimal leaseNum;

    @ApiModelProperty(value = "租赁单位（天月年）")

    private String leaseUnit;
    @ApiModelProperty(value = "规格名称")
    private String skuName;

    @ApiModelProperty(value = "原价")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "销售价格")
    private BigDecimal sellPrice;

    @ApiModelProperty(value = "库存")
    private BigDecimal stock;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "销量")
    private BigDecimal soldNum;

    @ApiModelProperty(value = "是否选中")
    private Boolean checked;

    @ApiModelProperty(value = "是否收藏（1是，0否")
    private boolean collect;
    @ApiModelProperty(value = "临购副单位")

    private String secondUnit;
    @ApiModelProperty(value = "临购副单位对应主单位数量系数")

    private BigDecimal secondUnitNum;
    @ApiModelProperty(value = "区域地址")

    private String zoneAddr;





}
