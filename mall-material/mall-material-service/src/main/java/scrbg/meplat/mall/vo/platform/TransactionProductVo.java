package scrbg.meplat.mall.vo.platform;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @program: maill_api
 * @description: 商品交易量类
 * @author: 曾湘
 * @create: 2023-09-21 11:00
 **/
@Data
public class TransactionProductVo {
    @ApiModelProperty(value = "订单号")

    private String orderSn;
    @ApiModelProperty(value = "商品id")

    private String productId;
    @ApiModelProperty(value = "商品id")

    private String orderItemId;

    @ApiModelProperty(value = "商品编号")

    private String productSn;
    @ApiModelProperty(value = "商品名称")

    private String productName;
    @ApiModelProperty(value = "购买数量")

    private BigDecimal buyCounts;
    @ApiModelProperty(value = "商品总金额")

    private BigDecimal totalAmount;
    @ApiModelProperty(value = "完成时间")

    private Date successDate;
    @ApiModelProperty(value = "完成时间")

    private String successDateStr;

}
