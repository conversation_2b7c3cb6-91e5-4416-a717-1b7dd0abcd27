package scrbg.meplat.mall.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import scrbg.meplat.mall.pcwp.third.model.MaterialNew;
import scrbg.meplat.mall.service.MaterialInfoService;
import scrbg.meplat.mall.pcwp.PcwpPageRes;

import java.util.List;

/**
 * @描述：物资信息批量处理工具类
 * @作者: AI Assistant
 * @日期: 2025-07-25
 */
@Slf4j
@Component
public class MaterialInfoBatchUtil {

    @Autowired
    private MaterialInfoService materialInfoService;

    /**
     * 处理分页查询结果并批量插入物资信息
     * @param pcwpPageRes 分页查询结果
     * @return 插入成功的记录数
     */
    public int processPcwpPageRes(PcwpPageRes<MaterialNew> pcwpPageRes) {
        if (pcwpPageRes == null) {
            log.warn("分页查询结果为空");
            return 0;
        }

        List<MaterialNew> materialNewList = pcwpPageRes.getList();
        if (CollectionUtils.isEmpty(materialNewList)) {
            log.warn("分页查询结果中的数据列表为空");
            return 0;
        }

        log.info("开始处理分页查询结果，当前页：{}, 每页大小：{}, 总记录数：{}, 当前页记录数：{}",
                pcwpPageRes.getCurrPage(),
                pcwpPageRes.getPageSize(),
                pcwpPageRes.getTotalCount(),
                materialNewList.size());

        try {
            int insertedCount = materialInfoService.batchInsertFromMaterialNew(materialNewList);
            log.info("批量插入完成，插入记录数：{}", insertedCount);
            return insertedCount;
        } catch (Exception e) {
            log.error("批量插入物资信息失败", e);
            throw new RuntimeException("批量插入物资信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理多页查询结果并批量插入物资信息
     * @param pcwpPageResList 多页查询结果列表
     * @return 总插入成功的记录数
     */
    public int processMultiplePcwpPageRes(List<PcwpPageRes<MaterialNew>> pcwpPageResList) {
        if (CollectionUtils.isEmpty(pcwpPageResList)) {
            log.warn("多页查询结果列表为空");
            return 0;
        }

        int totalInserted = 0;
        for (int i = 0; i < pcwpPageResList.size(); i++) {
            PcwpPageRes<MaterialNew> pageRes = pcwpPageResList.get(i);
            log.info("处理第{}页查询结果", i + 1);
            
            int inserted = processPcwpPageRes(pageRes);
            totalInserted += inserted;
        }

        log.info("多页批量插入完成，总插入记录数：{}", totalInserted);
        return totalInserted;
    }

    /**
     * 验证MaterialNew数据的完整性
     * @param materialNew MaterialNew对象
     * @return 验证结果
     */
    public boolean validateMaterialNew(MaterialNew materialNew) {
        if (materialNew == null) {
            log.warn("MaterialNew对象为空");
            return false;
        }

        // 检查必填字段
        if (materialNew.getMaterialName() == null || materialNew.getMaterialName().trim().isEmpty()) {
            log.warn("物资名称为空，billId: {}, billNo: {}", materialNew.getBillId(), materialNew.getBillNo());
            return false;
        }

        if (materialNew.getClassId() == null || materialNew.getClassId().trim().isEmpty()) {
            log.warn("类别ID为空，materialName: {}", materialNew.getMaterialName());
            return false;
        }

        return true;
    }

    /**
     * 过滤有效的MaterialNew数据
     * @param materialNewList MaterialNew列表
     * @return 过滤后的有效数据列表
     */
    public List<MaterialNew> filterValidMaterialNew(List<MaterialNew> materialNewList) {
        if (CollectionUtils.isEmpty(materialNewList)) {
            return materialNewList;
        }

        return materialNewList.stream()
                .filter(this::validateMaterialNew)
                .collect(java.util.stream.Collectors.toList());
    }
}
