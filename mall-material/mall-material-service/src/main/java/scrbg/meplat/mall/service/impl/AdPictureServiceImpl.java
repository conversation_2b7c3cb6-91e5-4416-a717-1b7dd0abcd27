package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import com.scrbg.common.utils.R;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.mapper.AdPictureMapper;
import scrbg.meplat.mall.mapper.ContentMapper;
import scrbg.meplat.mall.mapper.LinksMapper;
import scrbg.meplat.mall.mapper.SystemParamMapper;
import scrbg.meplat.mall.service.AdPictureService;
import scrbg.meplat.mall.vo.webContent.HomePageBidVo;
import scrbg.meplat.mall.vo.webContent.HomePageVo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @描述： 服务类
 * @作者: y
 * @日期: 2022-11-08
 */
@Service
public class AdPictureServiceImpl extends ServiceImpl<AdPictureMapper, AdPicture> implements AdPictureService {
    @Autowired
    MallConfig mallConfig;
    @Autowired
    LinksMapper linksMapper;
    @Autowired
    ContentMapper contentMapper;
    @Autowired
    SystemParamMapper systemParamMapper;


    /**
     * 分页查询（全部数据）
     *
     * @param jsonObject
     * @param queryWrapper
     * @return
     */
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<AdPicture> queryWrapper) {
        IPage<AdPicture> page = this.page(
                new Query<AdPicture>().getPage(jsonObject),
                queryWrapper.orderByDesc(AdPicture::getGmtCreate)
        );
        return new PageUtils(page);
    }

    /**
     * 添加数据
     *
     * @param adPicture
     */
    @Override
    public void create(AdPicture adPicture) {
        //新增数据默认为不发布状态
        adPicture.setState(2);
        super.save(adPicture);
    }

    /**
     * 根据id更新数据
     *
     * @param adPicture
     */
    @Override
    public void update(AdPicture adPicture) {
        //修改数据默认为不发布状态
        adPicture.setState(2);
        super.updateById(adPicture);
    }


    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    @Override
    public AdPicture getById(String id) {
        return super.getById(id);
    }

    /**
     * 根据id删除
     *
     * @param id
     */
    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    /**
     * 根据图片类型查询
     *
     * @param type
     * @return
     */
    @Override
    public List<AdPicture> getByType(Integer type) {
        QueryWrapper<AdPicture> wrapper = new QueryWrapper();
        wrapper.eq("picture_type", type);
        return list(wrapper);
    }

    /**
     * 根据条件查询
     *
     * @param adPicture
     * @return
     */
    @Override
    public List<AdPicture> findByCondition(AdPicture adPicture) {
        QueryWrapper<AdPicture> wrapper = new QueryWrapper();
        //添加根据图片类型查询
        if (adPicture.getPictureType() != 0) {
            wrapper.eq("picture_type", adPicture.getPictureType());
        }
        //添加根据图片状态查询
        if (adPicture.getState() != 0) {
            wrapper.eq("state", adPicture.getState());
        }
        //添加根据使用状态查询
        if (adPicture.getUseType() != 0) {
            wrapper.eq("use_type", adPicture.getUseType());
        }
        //排序方式(排序值升序)
        if (adPicture.getOrderBy() == PublicEnum.ORDER_BY_SORT.getCode()) {
            wrapper.orderByDesc("sort");
        }
        //排序方式(创建时间降序)
        if (adPicture.getOrderBy() == PublicEnum.ORDER_BY_GMT_CREATE.getCode()) {
            wrapper.orderByDesc("gmt_create");
        }
        //排序方式(修改时间降序)
        if (adPicture.getOrderBy() == PublicEnum.ORDER_BY_GMT_MODIFIED.getCode()) {
            wrapper.orderByDesc("gmt_modified");
        }
        return list(wrapper);
    }

    /**
     * 根据条件分页查询
     *
     * @param jsonObject
     * @param queryWrapper
     * @return
     */
    @Override
    public PageUtils queryAdPicturePage(JSONObject jsonObject, LambdaQueryWrapper<AdPicture> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        Integer pictureType = (Integer) innerMap.get("pictureType");
        Integer useType = (Integer) innerMap.get("useType");
        Integer state = (Integer) innerMap.get("state");
        Integer mallType = Integer.valueOf(mallConfig.mallType);
        Integer orderBy = (Integer) innerMap.get("orderBy");
        String keywords = (String) innerMap.get("keywords");


        if (pictureType != null) {
            queryWrapper.eq(AdPicture::getPictureType, pictureType);
        }
        if (mallType != null) {
            queryWrapper.eq(AdPicture::getMallType, mallType);
        }
        if (useType != null) {
            queryWrapper.eq(AdPicture::getUseType, useType);
        }
        if (state != null) {
            queryWrapper.eq(AdPicture::getState, state);
        }
        if (StringUtils.isNotEmpty(keywords)) {
            queryWrapper.like(AdPicture::getRemarks, keywords.trim());
        }

        //排序方式(排序值升序)
        if (orderBy == null) {

        } else if (orderBy == PublicEnum.ORDER_BY_SORT.getCode()) {
            queryWrapper.orderByDesc(AdPicture::getSort)
                    .orderByDesc(AdPicture::getGmtCreate);
            //排序方式(修改时间降序)

        } else if (orderBy == PublicEnum.ORDER_BY_GMT_CREATE.getCode()) {
            queryWrapper.orderByDesc(AdPicture::getGmtModified);

            //排序方式(创建时间降序)
        } else if (orderBy == PublicEnum.ORDER_BY_GMT_MODIFIED.getCode()) {
            queryWrapper.orderByDesc(AdPicture::getGmtCreate);
        }

        IPage<AdPicture> page = this.page(
                new Query<AdPicture>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);

    }

    /**
     * 根据id批量发布
     *
     * @param
     */

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByPublish(List<String> ids, String type) {
        List<AdPicture> resutls = listByIds(ids);
        for (AdPicture adPicture : resutls) {
            if ("1".equals(type)) {
                adPicture.setState(1);
            } else {
                adPicture.setState(2);
            }
        }
        super.saveOrUpdateBatch(resutls);
    }

    @Override
    public R queryHomeInfo(JSONObject jsonObject, Integer mallType) {
        HomePageVo vo = new HomePageVo();
//        setTestData(  vo);
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        Integer maxAdImg = (Integer) innerMap.get("maxAdImg") - 1;
        Integer maxNews = (Integer) innerMap.get("maxNews") - 1;
        Integer maxLinks = (Integer) innerMap.get("maxLinks") - 1;

        //友情链接
        LambdaQueryWrapper<Links> wrapperLinks = new LambdaQueryWrapper();
        wrapperLinks.select(Links::getName, Links::getUrl);
        wrapperLinks.eq(Links::getState, 1);
        wrapperLinks.orderByDesc(Links::getSort).orderByDesc(Links::getGmtCreate);
        wrapperLinks.last("limit 0," + maxLinks);
        List linksResults = linksMapper.selectList(wrapperLinks);
        vo.setLinks(linksResults);
        //新闻
        LambdaQueryWrapper<Content> wrapperContents = new LambdaQueryWrapper();
        wrapperContents.select(Content::getTitle, Content::getContentId, Content::getGmtRelease,Content::getBannerImg);
        wrapperContents.eq(Content::getState, 1);
        wrapperContents.eq(Content::getHome, 1);
        wrapperContents.eq(Content::getProgramaKey, "aboutUs");
        wrapperContents.lt(Content::getGmtRelease, new Date());
        //排序  置顶/排序/发布时间
        wrapperContents.orderByDesc(Content::getTop, Content::getSort ,Content::getGmtRelease);
        wrapperContents.last("limit 0," + maxNews);
        List contentsResults = contentMapper.selectList(wrapperContents);
        vo.setNewses(contentsResults);
        //广告图
        LambdaQueryWrapper<AdPicture> wrapperAd = new LambdaQueryWrapper();
        wrapperAd.select(AdPicture::getPictureUrl, AdPicture::getPictureType);
        wrapperAd.eq(AdPicture::getState, 1);
        wrapperAd.orderByDesc(AdPicture::getSort).orderByDesc(AdPicture::getGmtCreate);
        wrapperAd.eq(AdPicture::getUseType, 1);
        wrapperAd.last("limit 0," + maxAdImg);
        List adPictures = this.baseMapper.selectList(wrapperAd);
        vo.setAdPictures(adPictures);
        //设备商城
        if (mallType == 1) {
            //系统参数
            LambdaQueryWrapper<SystemParam> wrapperSystemParam = new LambdaQueryWrapper();
            wrapperSystemParam.select(SystemParam::getKeyValue, SystemParam::getCode);
            wrapperSystemParam.eq(SystemParam::getMallType, mallType);
            //publishRequireTotal     已发布需求数
            //transactionProjectTotal      交易项目数
            //transactionAmount     交易总金额
            //merchantsSettledTotal      入驻商家数
            String[] codes = {"publishRequireTotal", "transactionProjectTotal", "transactionAmount", "merchantsSettledTotal"};
            wrapperSystemParam.in(SystemParam::getCode, codes);

            List wrapperKeyDatas = systemParamMapper.selectList(wrapperSystemParam);
            vo.setKeyDatas(wrapperKeyDatas);
        }
        return R.success(vo);
    }

    /**
     * 查询首页广告图
     *
     * @param jsonObject
     * @return
     */
    @Override
    public List<AdPicture> getListAdPicture(JSONObject jsonObject) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        Integer useType = (Integer) innerMap.get("useType");
        Integer size = (Integer) innerMap.get("size");
        List<AdPicture> list = lambdaQuery().eq(AdPicture::getState, 1)
                .eq(useType != null, AdPicture::getUseType, useType)
                .last(size != null, "limit " + size)
                .eq(AdPicture::getMallType, mallConfig.mallType).list();
        return list;
    }


    private void setTestData(HomePageVo vo) {


        List<Links> links = new ArrayList<>();

        Links links1 = new Links();
        links1.setName("test111111111111111111111111");
        links1.setUrl("http://wwww.baidu.com");
        links.add(links1);
        //新闻
        List<Content> newses = new ArrayList<>();
        Content content = new Content();
        content.setTitle("ddddddddddddddddddasf 第三方打分但是dddddddddd");
        content.setGmtRelease(new Date());
        newses.add(content);
        vo.setNewses(newses);
        //广告图
        List<AdPicture> adPictures = new ArrayList<>();
        AdPicture adPicture = new AdPicture();
        adPicture.setPictureUrl("//www.baidu.com/img/PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png");

        adPictures.add(adPicture);
        vo.setAdPictures(adPictures);
        //商家
        List<Shop> shopes = new ArrayList<>();
        Shop shop = new Shop();
        shop.setShopImg("//www.baidu.com/img/PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png");
        shop.setShopName("Test");
        shop.setProvince("四川");
        shop.setCity("成都");
        shop.setShopDescrible("ass法师打发打发斯蒂芬水电费卡萨丁解放啦时代峻峰卡拉三等奖反倒是扣发奖金第十六届");
        shop.setMainBusiness("五金 设备");
        shop.setShopId("005a633cc344-1e28");
        Shop shop2 = new Shop();
        // shop2.setShopImg("//www.baidu.com/img/PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png");
        shop2.setShopName("Test");
        shop2.setProvince("四川");
        shop2.setCity("成都");
        shop2.setShopDescrible("ass法师打发打发斯蒂芬水电费卡萨丁解放啦时代峻峰卡拉三等奖反倒是扣发奖金第十六届");
        shop2.setMainBusiness("五金 设备");
        shop2.setShopId("005a633cc344-1e28");
        shopes.add(shop);
        shopes.add(shop2);

        vo.setLinks(links);
        vo.setShopes(shopes);


        List<HomePageBidVo> homePageBidVos = new ArrayList<>();
        HomePageBidVo homePageBidVo = new HomePageBidVo();
        homePageBidVo.setNumber("*********2");
        homePageBidVo.setTitle("四川公路桥梁建设集团有限公司，通辽市科左后旗，100MW光伏治沙储能项目地形测量及后续现场服…");
        homePageBidVo.setStartTime(new Date());
        homePageBidVo.setLogUrl("//www.baidu.com/img/PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png");
        homePageBidVo.setCompanyName("四川路桥");
        homePageBidVo.setAddress("成都");
        homePageBidVo.setType("设备租赁");
        homePageBidVos.add(homePageBidVo);

        HomePageBidVo homePageBidVo2 = new HomePageBidVo();
        homePageBidVo2.setNumber("*********2");
        homePageBidVo2.setTitle("四川公路桥梁建设集团有限公司，通辽市科左后旗，100MW光伏治沙储能项目地形测量及后续现场服…");
        homePageBidVo2.setStartTime(new Date());
        //   homePageBidVo2.setLogUrl("//www.baidu.com/img/PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png");
        homePageBidVo2.setCompanyName("四川路桥");
        homePageBidVo2.setAddress("成都");
        homePageBidVo2.setType("设备租赁");
        homePageBidVos.add(homePageBidVo2);
        vo.setHomePageBids(homePageBidVos);


        List<SystemParam> keyDatas = new ArrayList<>();
        SystemParam systemParam = new SystemParam();
        systemParam.setCode("222");
        systemParam.setKeyValue("*********");
        keyDatas.add(systemParam);
        vo.setKeyDatas(keyDatas);

    }

}
