package scrbg.meplat.mall.entity;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：通用附件
 * @作者: ye
 * @日期: 2024-01-24
 */
@ApiModel(value="通用附件")
@Data
@TableName("platform_fee_file")
public class PlatformFeeFile extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "附件id")
    private String fileId;

    @ApiModelProperty(value = "附件名称")

    private String name;


    @ApiModelProperty(value = "关联id")

    private String relevanceId;


    @ApiModelProperty(value = "关联类型（1年费缴费记录2交易费缴费记录）")

    private Integer relevanceType;


    @ApiModelProperty(value = "附件地址")

    private String url;


    @ApiModelProperty(value = "附件远程id")

    private String fileFarId;


    @ApiModelProperty(value = "媒体类型 1:图片2视频3附件")

    private Integer fileType;
















}