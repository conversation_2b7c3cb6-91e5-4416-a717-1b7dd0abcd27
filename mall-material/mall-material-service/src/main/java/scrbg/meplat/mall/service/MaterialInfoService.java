package scrbg.meplat.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import scrbg.meplat.mall.entity.MaterialInfo;
import scrbg.meplat.mall.pcwp.third.model.MaterialNew;

import java.util.List;

/**
 * @描述：物资信息表 服务类
 * @作者: AI Assistant
 * @日期: 2025-07-25
 */
public interface MaterialInfoService extends IService<MaterialInfo> {

    /**
     * 批量插入物资信息
     * @param materialInfoList 物资信息列表
     * @return 插入成功的记录数
     */
    int batchInsert(List<MaterialInfo> materialInfoList);

    /**
     * 批量插入物资信息（从MaterialNew转换）
     * @param materialNewList MaterialNew列表
     * @return 插入成功的记录数
     */
    int batchInsertFromMaterialNew(List<MaterialNew> materialNewList);

    /**
     * 将MaterialNew转换为MaterialInfo
     * @param materialNew MaterialNew对象
     * @return MaterialInfo对象
     */
    MaterialInfo convertFromMaterialNew(MaterialNew materialNew);

    /**
     * 根据单据ID和单据编号查询物资信息
     * @param billId 单据ID
     * @param billNo 单据编号
     * @return 物资信息
     */
    MaterialInfo getByBillIdAndBillNo(String billId, String billNo);

    /**
     * 根据物资名称和类别ID查询物资信息
     * @param materialName 物资名称
     * @param classId 类别ID
     * @return 物资信息列表
     */
    List<MaterialInfo> getByMaterialNameAndClassId(String materialName, String classId);

    /**
     * 检查物资信息是否已存在
     * @param materialNew MaterialNew对象
     * @return 是否存在
     */
    boolean isExist(MaterialNew materialNew);
}
