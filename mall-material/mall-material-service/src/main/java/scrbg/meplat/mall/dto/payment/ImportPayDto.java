package scrbg.meplat.mall.dto.payment;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @program: maill_api
 * @description: 倒入缴费记录数据对象
 * @author: 代文翰
 * @create: 2023-08-30 00:39
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@HeadRowHeight(40)
@ColumnWidth(25)
@ContentRowHeight(40)
public class ImportPayDto implements Serializable {
    private static final long serialVersionUID = -5144055068797033748L;
    @ExcelProperty(value = "乙方名称（企业）")
    private String enterpriseName;

    @ExcelProperty(value = "签约类别(中文、分隔：集采模块、租赁模块、物流模块)")
    private String serviceName;

    @ExcelProperty(value = "是否到款")
    private String payState;
    @ExcelProperty(value = "合同编号")
    private String contractN;
    @ExcelProperty(value = "金额（含税）")
    private BigDecimal payAmount;

    @ExcelProperty(value = "签约时间（例：2023/08/20）")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date startDate;
    //@ExcelProperty(value = "到期时间（例：2024/08/20）")
    //private Date endDate;
    @ExcelProperty(value = "平台审核是否通过")
    private String auditStatus;
    @ExcelProperty(value = "是否开票申请")
    private String payBillState;
}
