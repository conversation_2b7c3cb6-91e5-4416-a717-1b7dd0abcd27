package scrbg.meplat.mall.dto.thirdapi;

import java.util.List;
import java.util.Map;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MaterialDtlDTO {
    @ApiModelProperty(value = "物资id")
    private String billId;

    @ApiModelProperty(value = "物料编号")
    private String billNo;

    @ApiModelProperty(value = "物资类别id")
    private String classId;


    @ApiModelProperty(value = "物资类别id")
    private String classPath;

    @ApiModelProperty(value = "物资类别id")
    private List classPathList;

    @ApiModelProperty(value = "物资类别名称")
    private String className;

    @ApiModelProperty(value = "是否启用(0：停用;1：启用)")
    private Integer isEnable;

    @ApiModelProperty(value = "物料名称")

    private String materialName;
    @ApiModelProperty(value = "规格")

    private String spec;
    @ApiModelProperty(value = "计量单位")

    private String unit;
    @ApiModelProperty(value = "远程机构Id")
    private String orgId;

    @ApiModelProperty(value = "远程机构名称")
    private String orgName;

    @ApiModelProperty(value = "物资库版本Id")
    private String versionId;


    public static MaterialDtlDTO fromMap(Map map) {
        MaterialDtlDTO dto = new MaterialDtlDTO();
            dto.billId = (String) map.get("billId");
            dto.billNo = (String) map.get("billNo");
            dto.classId = (String) map.get("classId");
            dto.className = (String) map.get("className");
            dto.isEnable = (Integer) map.get("isEnable");
            dto.materialName = (String) map.get("materialName");
            dto.classPath = (String) map.get("classIdPath");

        if (map.containsKey("spec")) {
            dto.spec = (String) map.get("spec");
        }
        if (map.containsKey("unit")) {
            dto.unit = (String) map.get("unit");
        }
        if (map.containsKey("orgId")) {
            dto.orgId = (String) map.get("orgId");
        }
        if (map.containsKey("orgName")) {
            dto.orgName = (String) map.get("orgName");
        }
        return dto;




    }
}
