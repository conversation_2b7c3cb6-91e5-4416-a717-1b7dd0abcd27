package scrbg.meplat.mall.vo.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-12-21 14:36
 */
@Data
public class LoginDataVO {

    @ApiModelProperty(value = "组织列表")
    private List<OrganizationVO> organizationVOS;

    @ApiModelProperty(value = "当前组织名称")
    private String orgName;

    @ApiModelProperty(value = "当前组织id")
    private String orgId;

    @ApiModelProperty(value = "当前组织统一社会信用代码")
    private String socialCreditCode;

    @ApiModelProperty(value = "是否有电商权限（0否1是）")
    private Integer isShpAuthority;

    @ApiModelProperty(value = "是否内部用户（0不是1是）")
    private Integer isInterior;



    @ApiModelProperty(value = "用户类型：1普通用户 2店铺用户 4店铺+供应商 5供应商 6内部普通用户（内部） 7运营平台管理者（内部） 8内部普通店铺管理者（内部）")
    private Integer userType;

    @ApiModelProperty(value = "店铺id")
    private String shopId;

    @ApiModelProperty(value = "店铺类型 0：个体户  1：企业  2：个人")
    private Integer shopType;


    @ApiModelProperty(value = "企业类型：0：个体户  1：企业  2：个人")

    private Integer enterpriseType;


    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "用户编号")
    private String userNumber;


}
