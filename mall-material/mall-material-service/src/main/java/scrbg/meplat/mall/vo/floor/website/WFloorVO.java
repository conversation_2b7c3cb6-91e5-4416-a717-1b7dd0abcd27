package scrbg.meplat.mall.vo.floor.website;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-12-14 9:26
 */
@Data
public class WFloorVO {

    @ApiModelProperty(value = "楼层id")
    private String floorId;

    @ApiModelProperty(value = "栏目id")

    private String columnId;

    @ApiModelProperty(value = "楼层名称")

    private String floorName;

    @ApiModelProperty(value = "楼层名称后小字")

    private String floorNameText;

    @ApiModelProperty(value = "主图id")

    private String imgUrlId;

    @ApiModelProperty(value = "楼层主图链接")

    private String imgUrl;
    @ApiModelProperty(value = "商品分类ID")

    private String classId;

    @ApiModelProperty(value = "商品分类path")

    private String classPath;
    @ApiModelProperty(value = "楼层商品分类名称")

    private String className;
    @ApiModelProperty(value = "楼层背景图链接")

    private String backgroundUrl;

    @ApiModelProperty(value = "点击主图链接地址")

    private String mainImgUrl;

    @ApiModelProperty(value = "商品数据")
    private List<WFloorGoodsVO> goodsVOS;

}
