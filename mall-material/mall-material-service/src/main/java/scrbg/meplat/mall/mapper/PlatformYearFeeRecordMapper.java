package scrbg.meplat.mall.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import scrbg.meplat.mall.dto.free.MyQueryPayFreeListByEntity;
import scrbg.meplat.mall.entity.PlatformYearFeeRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Mapper;

/**
 * @描述：年费缴费记录 Mapper 接口
 * @作者: ye
 * @日期: 2024-01-24
 */
@Mapper
@Repository
public interface PlatformYearFeeRecordMapper extends BaseMapper<PlatformYearFeeRecord> {

    /**
     * 供应商查询缴费管理列表
     * @param pages
     * @param jsonObject
     * @return
     */
    IPage<MyQueryPayFreeListByEntity> myQueryPayFreeListByEntity(Page<MyQueryPayFreeListByEntity> pages,@Param("dto") JSONObject jsonObject);

}