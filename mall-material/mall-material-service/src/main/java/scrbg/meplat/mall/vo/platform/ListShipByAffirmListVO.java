package scrbg.meplat.mall.vo.platform;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2023-06-26 16:40
 */
@Data
public class ListShipByAffirmListVO {

    @ApiModelProperty(value = "店铺名称")

    private String shopName;

    @ApiModelProperty(value = "供应商名称")

    private String supplierName;
    @ApiModelProperty(value = "采购方名称")

    private String enterpriseName;

    @ApiModelProperty(value = "商品名称")
    private String productName;
    @ApiModelProperty(value = "基础库物资名称")

    private String materialName;

    @ApiModelProperty(value = "商品价格")
    private BigDecimal productPrice;

    @ApiModelProperty(value = "交易总金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "交易数量")
    private BigDecimal number;

    @ApiModelProperty(value = "退货数量")
    private BigDecimal returnCounts;



    @ApiModelProperty(value = "交易完成时间")
    private String finishDateStr;

    @ApiModelProperty(value = "订单编号")
    private String orderSn;


    @ApiModelProperty(value = "查询合计金额")
    private BigDecimal countAmount;


    @ApiModelProperty(value = "发货单明细id")
    private String dtlId;
    public BigDecimal getNumber() {
        return number.subtract(returnCounts);
    }

    public BigDecimal getAmount() {
        //大宗月供没有金额
        if (amount!=null){
            return amount.subtract(returnCounts.multiply(productPrice).setScale(2, RoundingMode.HALF_UP));
        }
        return new BigDecimal(0);
    }
}
