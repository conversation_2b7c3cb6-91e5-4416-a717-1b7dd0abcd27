package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.itextpdf.text.Image;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.service.ShopSupplierReleService;
import scrbg.meplat.mall.entity.ShopSupplierRele;

import java.util.List;

/**
 * @描述：店铺供方关联表控制类
 * @作者: ye
 * @日期: 2023-06-05
 */
@RestController
@RequestMapping("/")
@Api(tags = "店铺供方关联表")
public class ShopSupplierReleController {

    @Autowired
    public ShopSupplierReleService shopSupplierReleService;

    @PostMapping("shopSupplierRele/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
    })
    public PageR<ShopSupplierRele> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = shopSupplierReleService.queryPage(jsonObject, new LambdaQueryWrapper<ShopSupplierRele>());
        return PageR.success(page);
    }


    @PostMapping("shopSupplierRele/listByShopId")
    @ApiOperation(value = "根据店铺查询对应关联的供应商")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
    })
    public PageR<ShopSupplierRele> listByShopId(@RequestBody JSONObject jsonObject) {
        PageUtils page = shopSupplierReleService.listByShopId(jsonObject, new LambdaQueryWrapper<ShopSupplierRele>());
        return PageR.success(page);
    }
    @PostMapping("shopSupplierRele/listBySupplierId")
    @ApiOperation(value = "根据店铺查询对应关联的供应商")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
    })
    public PageR<ShopSupplierRele> listBySupplierId(@RequestBody JSONObject jsonObject) {
        PageUtils page = shopSupplierReleService.listBySupplierId(jsonObject, new LambdaQueryWrapper<ShopSupplierRele>());
        return PageR.success(page);
    }





    @GetMapping("shopSupplierRele/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<ShopSupplierRele> findById(String id) {
        ShopSupplierRele shopSupplierRele = shopSupplierReleService.getById(id);
        return R.success(shopSupplierRele);
    }

    @PostMapping("shopSupplierRele/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody ShopSupplierRele shopSupplierRele) {
        shopSupplierReleService.create(shopSupplierRele);
        return R.success();
    }

    @PostMapping("shopSupplierRele/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody ShopSupplierRele shopSupplierRele) {
        shopSupplierReleService.update(shopSupplierRele);
        return R.success();
    }

    @GetMapping("shopSupplierRele/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        shopSupplierReleService.delete(id);
        return R.success();
    }


    @PostMapping("shopSupplierRele/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        shopSupplierReleService.removeByIds(ids);
        return R.success();
    }

    @PostMapping("shopSupplierRele/updateByBatch")
    @ApiOperation(value = "批量修改排序值")
    public R updateByBatch(@RequestBody List<ShopSupplierRele> shopSupplierRele) {
        shopSupplierReleService.updateBatchById(shopSupplierRele);
        return R.success();
    }


    @PostMapping("supplier/shopSupplierRele/listShopListBySupplierId")
    @ApiOperation(value = "获取本机构下供店铺")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "shopName", value = "店铺名称", dataTypeClass = String.class),
    })
    public PageR<ShopSupplierRele> listShopListBySupplierId(@RequestBody JSONObject jsonObject) {
        PageUtils page = shopSupplierReleService.listShopListBySupplierId(jsonObject, new LambdaQueryWrapper<ShopSupplierRele>());
        return PageR.success(page);
    }


    @GetMapping("shopSupplierRele/shopManage/isTwoSupper")
    @ApiOperation(value = "获取子供应商")
    public R<ShopSupplierRele> isTwoSupper() {
        ShopSupplierRele b = shopSupplierReleService.isTwoSupper();
        return R.success(b);
    }


}

