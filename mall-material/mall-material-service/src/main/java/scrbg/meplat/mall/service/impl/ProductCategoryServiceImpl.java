package scrbg.meplat.mall.service.impl;

import static cn.hutool.core.lang.Console.log;
import static java.util.concurrent.TimeUnit.MINUTES;

import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeSet;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import com.scrbg.common.utils.R;

import io.seata.spring.annotation.GlobalTransactional;
import scrbg.meplat.mall.common.redis.RedisKey;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.dto.category.UpdateCategorySateByIdDTO;
import scrbg.meplat.mall.dto.thirdapi.BatchUpdateMaterialDtlState;
import scrbg.meplat.mall.dto.thirdapi.CategoryLibraryDTO;
import scrbg.meplat.mall.dto.thirdapi.CategoryLibrarySave;
import scrbg.meplat.mall.dto.thirdapi.CreateProductCategoryDTO;
import scrbg.meplat.mall.dto.thirdapi.MaterialDtlDTO;
import scrbg.meplat.mall.entity.InterfaceLogs;
import scrbg.meplat.mall.entity.Product;
import scrbg.meplat.mall.entity.ProductCategory;
import scrbg.meplat.mall.entity.ProductInventory;
import scrbg.meplat.mall.entity.excelTemplate.MaterialDtlInfos;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.enums.product.ProductCategoryEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.ProductCategoryMapper;
import scrbg.meplat.mall.service.BrandService;
import scrbg.meplat.mall.service.InterfaceLogsService;
import scrbg.meplat.mall.service.ProductCategoryService;
import scrbg.meplat.mall.service.ProductInventoryService;
import scrbg.meplat.mall.service.ProductService;
import scrbg.meplat.mall.util.LogUtil;
import scrbg.meplat.mall.util.MaterialLockUtils;
import scrbg.meplat.mall.util.PCWP2ApiUtil;
import scrbg.meplat.mall.util.RestTemplateUtils;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.vo.platform.MaterialVo;
import scrbg.meplat.mall.vo.product.ImportMaterialExcelResultVO;
import scrbg.meplat.mall.vo.product.material.CategoryClassIdAndClassNameVO;

/**
 * @描述：商品分类 服务类
 * @作者: y
 * @日期: 2022-11-02
 */
@Service
public class ProductCategoryServiceImpl extends ServiceImpl<ProductCategoryMapper, ProductCategory> implements ProductCategoryService {
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    ProductService productService;
    @Autowired
    private InterfaceLogsService interfaceLogsService;
    @Autowired
    ProductInventoryService productInventoryService;
    @Autowired
    BrandService brandService;

    @Autowired
    public MallConfig mallConfig;

    @Autowired
    ProductCategoryMapper productCategoryMapper;

    @Autowired
    RestTemplateUtils restTemplateUtils;

    // 创建/修改分类
    private static final String CREATE_CLASS_URL = "/thirdapi/matarialpurchase/saveCategoryLibrary";

    // 创建/修改物资信息
    private static final String CREATE_MATARIAL_URL = "/thirdapi/matarialpurchase/saveMaterialInfo";

    // 批量修改分类启用/停用状态
    private static final String UPDATE_CLASS_STATE_URL = "/thirdapi/matarialpurchase/batchUpdateCategoryLibraryState";


    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ProductCategory> queryWrapper) {
        IPage<ProductCategory> page = this.page(new Query<ProductCategory>().getPage(jsonObject), queryWrapper);
        return new PageUtils(page);
    }

    @GlobalTransactional
    @Override
    public void create(ProductCategory productCategory, String idStr,String fag) {
        String classId = productCategory.getParentId();
        List<CategoryClassIdAndClassNameVO> path = getCategoryParentPath(classId);
        if (!path.get(0).getClassName().equals("低值易耗品")) {
            throw new BusinessException("【本地异常】只能新增低值易耗品子分类！");
        }
        // 处理重复问题
        Integer count = lambdaQuery().eq(ProductCategory::getClassLevel, productCategory.getClassLevel()).eq(ProductCategory::getClassName, productCategory.getClassName()).eq(ProductCategory::getProductType, productCategory.getProductType()).eq(productCategory.getParentId() != null, ProductCategory::getParentId, productCategory.getParentId()).eq(ProductCategory::getMallType, mallConfig.mallType).count();
        if (count > 0) {
            throw new BusinessException("【本地异常】分类同级名称重复！");
        }
        // 获取classPath
        if (!StringUtils.isEmpty(productCategory.getParentId())) {
            List<CategoryClassIdAndClassNameVO> categoryParentPath = getCategoryParentPath(productCategory.getParentId());
            String classPath = "";
            if (!CollectionUtils.isEmpty(categoryParentPath)) {
                for (CategoryClassIdAndClassNameVO categoryClassIdAndClassNameVO : categoryParentPath) {
                    if (!StringUtils.isEmpty(categoryClassIdAndClassNameVO.getClassName())) {
                        classPath += categoryClassIdAndClassNameVO.getClassName() + "/";
                    }
                }
                classPath += productCategory.getClassName();
                productCategory.setClassPath(classPath);
            }
        } else {
            productCategory.setClassPath(productCategory.getClassName());
        }

        // 调用远程进行新增
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String url = mallConfig.prodPcwp2Url02 + CREATE_CLASS_URL;
        CategoryLibrarySave dto = new CategoryLibrarySave();
        dto.setKeyId(idStr);
        CategoryLibraryDTO dto2 = new CategoryLibraryDTO();
        dto2.setParentClassId(productCategory.getParentId());
        dto2.setParentClassName(getById(productCategory.getParentId()).getClassName());
        dto2.setClassName(productCategory.getClassName());
        if (productCategory.getState() == 1) {
            dto2.setIsEnable(1);
        }else {
            dto2.setIsEnable(0);
        }
        dto2.setMaterialType(0);
        dto2.setOrgId(user.getOrgId());
        dto2.setOrgName(user.getEnterpriseName());
        // TODO 这里接口传参已经与接口文档差别较大了，估计已经没用了
        // dto2.setVersionId();
        dto.setCategoryLibrary(dto2);
        // 记录日志、处理异常
        LogUtil.writeInfoLog(idStr, "create", productCategory, dto, null, ProductCategoryServiceImpl.class);
        R<Map> r = null;
        try {
            fag=JSON.toJSONString(dto);
            r = restTemplateUtils.postPCWP2(url, dto);
        } catch (Exception e) {
            // 捕获异常
            LogUtil.writeErrorLog(idStr, "create", productCategory, dto, null, e.getMessage(), ProductCategoryServiceImpl.class);
            throw new BusinessException("【远程异常】" + e.getMessage());
        }
        if (r.getCode() == null || r.getCode() != 200) {
            // 返回不是200异常
            LogUtil.writeErrorLog(idStr, "create", productCategory, dto, r, r.getMessage(), ProductCategoryServiceImpl.class);
            throw new BusinessException("【远程异常】" + r.getMessage());
        }
        // 成功保存日志
        InterfaceLogs iLog = new InterfaceLogs();
        iLog.setSecretKey(idStr);
        iLog.setClassPackage(ProductCategoryServiceImpl.class.getName());
        iLog.setMethodName("create");
        iLog.setLocalArguments(JSON.toJSONString(productCategory));
        iLog.setFarArguments(JSON.toJSONString(dto));
        iLog.setIsSuccess(1);
        iLog.setLogType(1);
        iLog.setResult(JSON.toJSONString(r));
        interfaceLogsService.create(iLog);
        Map data = r.getData();
        productCategory.setClassId((String) data.get("billId"));
        productCategory.setClassNo((String) data.get("billNo"));
        //调用父类方法即可
        boolean save = super.save(productCategory);
        if (!save) {
            throw new BusinessException("保存失败");
        }
    }

    @Override
    public void update(ProductCategory productCategory) {
        super.updateById(productCategory);
    }


    @Override
    public ProductCategory getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        int i = productCategoryMapper.removeById(id);
    }


    /**
     * 获取分类树形
     *
     * @param productType
     * @param classIds
     * @return
     */
    @Override
    public List<ProductCategory> listWithTree(Integer productType, List<String> classIds, Integer state, Integer isHaveProduct, Integer isLc, String versionId) {
        LambdaQueryChainWrapper<ProductCategory> q = lambdaQuery().eq(productType != null, ProductCategory::getProductType, productType)
            .eq(versionId != null,ProductCategory::getVersionId, versionId);
        q.eq(ProductCategory::getMallType, mallConfig.mallType);
        if (classIds != null) {
            q.in(ProductCategory::getClassId, classIds);
        }
        if (state != null) {
            q.eq(ProductCategory::getState, state);
        }
        if (isHaveProduct != null) {
            q.eq(ProductCategory::getIsHaveProduct, isHaveProduct);
        }
        // 查出所有分类
        List<ProductCategory> entities = q.list();
        // 临购排除低脂易耗
        if (isLc != null) {
            if (!CollectionUtils.isEmpty(entities)) {
                Iterator<ProductCategory> iterator = entities.iterator();
                switch (isLc) {
                    case 1:
                        while (iterator.hasNext()) {
                            ProductCategory category = iterator.next();
                            String className = category.getClassPath();
                            if (!className.contains("主要材料")) {
                                iterator.remove();
                            }
                        }
                        ;
                        break;
                    case 2:
                        while (iterator.hasNext()) {
                            ProductCategory next = iterator.next();
                            if (next.getClassPath() != null) {
                                try {
                                    if (!next.getClassPath().contains("低值易耗品")) {
                                        iterator.remove();
                                    }
                                } catch (Exception e) {
                                    throw new RuntimeException("物资分类" + iterator.next() + "没有父级分类,请联系管理员处理");
                                }
                            } else {
                                iterator.remove();
                            }

                        }
                        ;
                        break;
                    default:
                        break;
                }

            }
        }
        // 组装成父子树形结构
        return getChildrens(entities);
    }

    private List<ProductCategory> getChildrens(List<ProductCategory> entities) {
        List<ProductCategory> level1Menus = new ArrayList<>();
        if (entities.size() > 0) {
            level1Menus = entities.stream().filter(entity -> StringUtils.isEmpty(entity.getParentId()) || "0".equals(entity.getParentId())).map(entity -> {
                entity.setChildren(getChildren(entity, entities));
                return entity;
            }).sorted((entity1, entity2) -> (entity2.getSort() == null ? 0 : entity2.getSort()) - (entity1.getSort() == null ? 0 : entity1.getSort())).collect(Collectors.toList());
        }
        return level1Menus;
    }

    /**
     * 递归查找所有菜单的子菜单
     */
    private List<ProductCategory> getChildren(ProductCategory root, List<ProductCategory> all) {
        List<ProductCategory> children = all.stream().filter(entity -> root.getClassId().equals(entity.getParentId())).map(entity -> {
            entity.setChildren(getChildren(entity, all));
            return entity;
        }).sorted((entity1, entity2) -> (entity2.getSort() == null ? 0 : entity2.getSort()) - (entity1.getSort() == null ? 0 : entity1.getSort())).collect(Collectors.toList());
        return children;
    }

    /**
     * 根据ids批量逻辑删除
     *
     * @param ids
     */
    @Override
    public void removeLogicBatch(List<String> ids) {
        // 不对一级操作
//        ids.remove(0);
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException("不能操作一级分类！");
        }
        List<ProductCategory> categoryList = listByIds(ids);
        if (categoryList.size() == 0) throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "分类不存在！");
        for (ProductCategory c : categoryList) {
            Integer count1 = lambdaQuery().eq(ProductCategory::getParentId, c.getClassId()).eq(ProductCategory::getIsDelete, PublicEnum.IS_DELETE_NO.getCode()).count();
            if (count1 > 0) {
                throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "【" + c.getClassName() + "】该分类有对应子分类不能删除！");
            }
            Integer count2 = productService.lambdaQuery().eq(Product::getClassId, c.getClassId()).eq(Product::getIsDelete, PublicEnum.IS_DELETE_NO.getCode()).count();
            if (count2 > 0) {
                throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "【" + c.getClassName() + "】该分类有对应商品正在使用不能删除！");

            }
            Integer count3 = productInventoryService.lambdaQuery().eq(ProductInventory::getClassId, c.getClassId()).eq(ProductInventory::getIsDelete, PublicEnum.IS_DELETE_NO.getCode()).count();
            if (count3 > 0) {
                throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "【" + c.getClassName() + "】该分类有对应商品库商品正在使用不能删除！");
            }
        }
//        lambdaUpdate()
//                .in(ProductCategory::getClassId, ids)
//                .set(ProductCategory::getIsDelete, PublicEnum.IS_DELETE_YES.getCode())
//                .set(ProductCategory::getGmtModified, new Date())
//                .update();
        String classId = ids.get(0);
        ProductCategory byId = getById(classId);
        if (byId != null) {
            String key = RedisKey.CATEGORY + "::" + byId.getProductType() + "_" + byId.getMallType() + "_getTree";
            stringRedisTemplate.delete(key);
        }
        productCategoryMapper.removeByIds(ids);
    }

    /**
     * 根据分类名称模糊查询
     *
     * @param className
     * @param isLc
     * @return
     */
    @Override
    public List<ProductCategory> listByClassName(String className, Integer productType, Integer state, Integer isHaveProduct, Integer isLc) {
        UserLogin userLogin = ThreadLocalUtil.getCurrentUser();
        String versionId;
        if (userLogin==null) {
            // 游客 默认2.0
            versionId = "1942513893823057922";
        }else if (userLogin.getMaterialBaseId()!=null) {
            // 项目部看绑定的那个物资库
            versionId = userLogin.getMaterialBaseId();
        }else if (userLogin.getCurrentMbId()==null) {
            // 直属未选择默认展示2.0
            versionId = "1942513893823057922";
        }else {
            // 直属已选择
            versionId = userLogin.getCurrentMbId();
        }
        if (StringUtils.isEmpty(className)) {
            return listWithTree(productType, null, state, isHaveProduct, isLc, versionId);
        }
        LambdaQueryChainWrapper<ProductCategory> q = lambdaQuery().eq(productType != null, ProductCategory::getProductType, productType).eq(ProductCategory::getMallType, mallConfig.mallType);

        // 查出所有分类
        List<ProductCategory> entities = q.list();
        List<ProductCategory> list = new ArrayList<>();

        LambdaQueryChainWrapper<ProductCategory> eq = lambdaQuery().eq(productType != null, ProductCategory::getProductType, productType).eq(ProductCategory::getMallType, mallConfig.mallType);
        eq.like(ProductCategory::getClassName, className);
        if (state != null) {
            eq.eq(ProductCategory::getState, state);
        }
        if (isHaveProduct != null) {
            eq.eq(ProductCategory::getIsHaveProduct, isHaveProduct);
        }
        List<ProductCategory> categoryList = eq.list();
        if (CollectionUtils.isEmpty(categoryList) || CollectionUtils.isEmpty(entities)) return list;
        for (ProductCategory entity : categoryList) {
            getParentCategoryObject(list, entity, entities);
            list.add(entity);
        }
        // 去重
        List<ProductCategory> lists = list.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getClassId() + ";" + o.getClassId()))), ArrayList::new));
        // 组装树
        return getChildrens(lists);
    }

    @Override
    public List<ProductCategory> listByClassNameTwo(String className, Integer productType, Integer state, Integer isHaveProduct, Integer isLc,String versionId) {
        if (StringUtils.isEmpty(className)) {
            return listWithTree(productType, null, state, isHaveProduct, isLc, versionId);
        }
        LambdaQueryChainWrapper<ProductCategory> q = lambdaQuery().eq(productType != null, ProductCategory::getProductType, productType).eq(ProductCategory::getMallType, mallConfig.mallType).eq(versionId != null,ProductCategory::getVersionId,versionId);

        // 查出所有分类
        List<ProductCategory> entities = q.list();
        List<ProductCategory> list = new ArrayList<>();

        LambdaQueryChainWrapper<ProductCategory> eq = lambdaQuery().eq(productType != null, ProductCategory::getProductType, productType).eq(ProductCategory::getMallType, mallConfig.mallType).eq(versionId != null,ProductCategory::getVersionId,versionId);
        eq.like(ProductCategory::getClassName, className);
        if (state != null) {
            eq.eq(ProductCategory::getState, state);
        }
        if (isHaveProduct != null) {
            eq.eq(ProductCategory::getIsHaveProduct, isHaveProduct);
        }
        List<ProductCategory> categoryList = eq.list();
        if (CollectionUtils.isEmpty(categoryList) || CollectionUtils.isEmpty(entities)) return list;
        for (ProductCategory entity : categoryList) {
            getParentCategoryObject(list, entity, entities);
            list.add(entity);
        }
        // 去重
        List<ProductCategory> lists = list.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getClassId() + ";" + o.getClassId()))), ArrayList::new));
        // 组装树
        return getChildrens(lists);
    }

    /**
     * 根据ids和分类类型查询分类对应的父级树
     *
     * @param productType
     * @param classs
     * @return
     */
    @Override
    public List<ProductCategory> ListByCategoryParentByIds(Integer productType, List<ProductCategory> classs) {
        // 查出所有分类
        List<ProductCategory> entities = lambdaQuery().eq(ProductCategory::getProductType, productType).eq(ProductCategory::getIsDelete, PublicEnum.IS_DELETE_NO.getCode()).eq(ProductCategory::getMallType, mallConfig.mallType).list();

        // 递归拿到所有的父级
        List<ProductCategory> list = new ArrayList<>();

        if (CollectionUtils.isEmpty(entities) || CollectionUtils.isEmpty(classs)) return list;
        for (ProductCategory entity : classs) {
            list.add(entity);
            getParentCategoryObject(list, entity, entities);
        }

        // 去重
        List<ProductCategory> lists = list.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getClassId() + ";" + o.getClassId()))), ArrayList::new));
        // 组装树
        return getChildrens(lists);
    }

    /**
     * 根据id获取分类
     *
     * @param classId
     * @param isDelete
     * @return
     */
    @Override
    public ProductCategory getProductCategoryById(String classId, Integer isDelete) {
        LambdaQueryChainWrapper<ProductCategory> q = lambdaQuery();
        q.eq(ProductCategory::getClassId, classId);
        if (isDelete != null) {
            q.eq(ProductCategory::getIsDelete, isDelete);
        }
        return q.one();
    }

    /**
     * 根据id修改
     *
     * @param productCategory
     * @param idStr
     */
    @GlobalTransactional
    @Override
    public void updateIfById(ProductCategory productCategory, String idStr) {
        String classId = productCategory.getClassId();
        List<CategoryClassIdAndClassNameVO> path = getCategoryParentPath(classId);
        if (!path.get(0).getClassName().equals("低值易耗品")) {
            throw new BusinessException("【本地异常】只能修改低值易耗品子分类！");
        }
        if (path.size() == 1) {
            throw new BusinessException("不能修改一级分类！");
        }
        Integer count = lambdaQuery().eq(ProductCategory::getClassLevel, productCategory.getClassLevel()).eq(ProductCategory::getClassName, productCategory.getClassName()).eq(ProductCategory::getParentId, productCategory.getParentId()).ne(ProductCategory::getClassId, classId).eq(ProductCategory::getMallType, mallConfig.mallType).count();
        if (count > 0) {
            throw new BusinessException("【本地异常】分类同级名称重复！");
        }

        // 修改品牌的分类名称
        brandService.updateClassNameByClassId(productCategory.getClassId(), productCategory.getClassName());

        // 调用远程进
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String url = mallConfig.prodPcwp2Url02 + CREATE_CLASS_URL;
        CategoryLibrarySave dto = new CategoryLibrarySave();
        dto.setKeyId(idStr);
        CategoryLibraryDTO dto2 = new CategoryLibraryDTO();
        dto2.setBillId(productCategory.getClassId());
        dto2.setParentClassId(productCategory.getParentId());
        dto2.setParentClassName(getById(productCategory.getParentId()).getClassName());
        dto2.setClassName(productCategory.getClassName());
        dto2.setOrgId(user.getOrgId());
        dto2.setOrgName(user.getEnterpriseName());
        Integer state = productCategory.getState();
        if (state == 1) {
            dto2.setIsEnable(1);
        } else {
            dto2.setIsEnable(0);
        }
        dto2.setMaterialType(0);
        dto.setCategoryLibrary(dto2);
        // 记录日志、处理异常
        LogUtil.writeInfoLog(idStr, "updateIfById", productCategory, dto, null, ProductCategoryServiceImpl.class);
        R r = null;
        try {
            r = restTemplateUtils.postPCWP2(url, dto);
        } catch (Exception e) {
            // 捕获异常
            LogUtil.writeErrorLog(idStr, "updateIfById", productCategory, dto, null, e.getMessage(), ProductCategoryServiceImpl.class);
            throw new BusinessException(500, "【远程异常】" + e.getMessage());
        }
        if (r.getCode() == null || r.getCode() != 200) {
            // 返回不是200异常
            LogUtil.writeErrorLog(idStr, "updateIfById", productCategory, dto, r, r.getMessage(), ProductCategoryServiceImpl.class);
            throw new BusinessException(500, "【远程异常】" + r.getMessage());
        }
        // 成功保存日志
        InterfaceLogs iLog = new InterfaceLogs();
        ProductCategory pr = getById(productCategory.getClassId());
        String updateBefore = JSON.toJSONString(pr);
        iLog.setUpdateBefore(updateBefore);
        iLog.setSecretKey(idStr);
        iLog.setClassPackage(ProductCategoryServiceImpl.class.getName());
        iLog.setMethodName("updateIfById");
        iLog.setLocalArguments(JSON.toJSONString(productCategory));
        iLog.setFarArguments(JSON.toJSONString(dto));
        iLog.setIsSuccess(1);
        iLog.setLogType(1);
        iLog.setResult(JSON.toJSONString(r));
        interfaceLogsService.create(iLog);


        boolean b = updateById(productCategory);
        if (!b) {
            throw new BusinessException("修改失败！");
        }
        List<CategoryClassIdAndClassNameVO> categoryParentPath = getCategoryParentPath(productCategory.getClassId());
        String classPath = "";
        if (!CollectionUtils.isEmpty(categoryParentPath)) {
            for (CategoryClassIdAndClassNameVO categoryClassIdAndClassNameVO : categoryParentPath) {
                if (!StringUtils.isEmpty(categoryClassIdAndClassNameVO.getClassName())) {
                    classPath += categoryClassIdAndClassNameVO.getClassName() + "/";
                }
            }
        }
        lambdaUpdate().eq(ProductCategory::getClassId, productCategory.getClassId()).set(ProductCategory::getClassPath, classPath.substring(0, classPath.length() - 1)).update();
    }

    /**
     * 批量修改启用状态
     *
     * @param updateCategorySateByIdDTO
     */
    @GlobalTransactional
    @Override
    public void updateCategorySateById(UpdateCategorySateByIdDTO updateCategorySateByIdDTO, String ids) {
        List<String> classIds = updateCategorySateByIdDTO.getClassIds();
        List<CategoryClassIdAndClassNameVO> path = getCategoryParentPath(classIds.get(0));
        if (!path.get(0).getClassName().equals("低值易耗品")) {
            throw new BusinessException("【本地异常】只能修改低值易耗品子分类！");
        }
        ProductCategory productCategory = lambdaQuery().eq(ProductCategory::getClassId, classIds.get(0)).select(ProductCategory::getClassLevel).one();
        if (classIds.size() == 1) {
            if (productCategory.getClassLevel() == 1) {
                throw new BusinessException("【本地异常】不能对一级分类进行操作！");
            }
        } else {
            if (productCategory.getClassLevel() == 1) {
                classIds.remove(0);
            }
        }


        // 调用远程进
        String url = mallConfig.prodPcwp2Url02 + UPDATE_CLASS_STATE_URL;
        HashMap<Object, Object> dto = new HashMap<>();
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        dto.put("ids", updateCategorySateByIdDTO.getClassIds());
        dto.put("keyId", ids);
        dto.put("userId", currentUser.getFarUserId());
        dto.put("userName", currentUser.getUserName());
        if (updateCategorySateByIdDTO.getState() == 1) {
            dto.put("state", true);
        } else {
            dto.put("state", false);
        }
        // 记录日志、处理异常
        LogUtil.writeInfoLog(ids, "updateCategorySateById", updateCategorySateByIdDTO, dto, null, ProductCategoryServiceImpl.class);
        R r = null;
        try {
            r = restTemplateUtils.postPCWP2(url, dto);
        } catch (Exception e) {
            // 捕获异常
            LogUtil.writeErrorLog(ids, "updateCategorySateById", updateCategorySateByIdDTO, dto, null, e.getMessage(), ProductCategoryServiceImpl.class);
            throw new BusinessException("【远程异常】" + e.getMessage());
        }
        if (r.getCode() == null || r.getCode() != 200) {
            // 返回不是200异常
            LogUtil.writeErrorLog(ids, "updateCategorySateById", updateCategorySateByIdDTO, dto, r, r.getMessage(), ProductCategoryServiceImpl.class);
            throw new BusinessException("【远程异常】" + r.getMessage());
        }

        boolean update = lambdaUpdate().in(ProductCategory::getClassId, updateCategorySateByIdDTO.getClassIds()).set(ProductCategory::getGmtModified, new Date()).set(ProductCategory::getState, updateCategorySateByIdDTO.getState()).update();
        if (!update) {
            throw new BusinessException("修改失败！");
        } else {
            // 成功保存日志
            InterfaceLogs iLog = new InterfaceLogs();
            ProductCategory pr = getById(productCategory.getClassId());
            String updateBefore = JSON.toJSONString(pr);
            iLog.setUpdateBefore(updateBefore);
            iLog.setSecretKey(ids);
            iLog.setClassPackage(ProductCategoryServiceImpl.class.getName());
            iLog.setMethodName("updateCategorySateById");
            iLog.setLocalArguments(JSON.toJSONString(updateCategorySateByIdDTO));
            iLog.setFarArguments(JSON.toJSONString(dto));
            iLog.setIsSuccess(1);
            iLog.setLogType(1);
            iLog.setResult(JSON.toJSONString(r));
            interfaceLogsService.create(iLog);

            // 删除缓存
            ProductCategory byId = getById(updateCategorySateByIdDTO.getClassIds().get(0));
            if (byId != null) {
                String key = RedisKey.CATEGORY + "::" + byId.getProductType() + "_" + byId.getMallType() + "_getTree";
                stringRedisTemplate.delete(key);
            }
        }
    }

    /**
     * 根据分类id批量修改是否有商品
     *
     * @param classIds
     * @param isHaveProduct
     */
    @Override
    public void updateHaveProductStateById(List<String> classIds, Integer isHaveProduct) {
        lambdaUpdate().in(ProductCategory::getClassId, classIds).set(ProductCategory::getIsHaveProduct, isHaveProduct).set(ProductCategory::getGmtModified, new Date()).update();
    }

    /**
     * 根据id查询出所有的父级id集合，包括本身
     *
     * @param classId
     * @return
     */
    @Override
    public List<String> getCategoryParentIdList(String classId) {
        List<String> listStr = new ArrayList<>();
        ProductCategory byId = getById(classId);
        if (byId == null) return listStr;
        getParentId(byId, listStr);
        Collections.reverse(listStr);
        return listStr;
    }

    /**
     * 根据分类id查询分类路径（id和名称）
     *
     * @param classId
     * @return
     */
    @Override
    public List<CategoryClassIdAndClassNameVO> getCategoryParentPath(String classId) {
        List<CategoryClassIdAndClassNameVO> listStr = new ArrayList<>();
        ProductCategory productCategory = getById(classId);
        if (productCategory == null) return listStr;
        getParentPath(productCategory, listStr);
        Collections.reverse(listStr);
        return listStr;
    }

    public void getParentPath(ProductCategory productCategory, List<CategoryClassIdAndClassNameVO> list) {
        CategoryClassIdAndClassNameVO vo = new CategoryClassIdAndClassNameVO();
        vo.setClassId(productCategory.getClassId());
        vo.setClassName(productCategory.getClassName());
        list.add(vo);
        if (StringUtils.isEmpty(productCategory.getParentId())) {
            return;
        } else {
            ProductCategory byId = getById(productCategory.getParentId());
            if (byId == null) {
                return;
            } else {
                getParentPath(byId, list);
            }
        }
    }

    /**
     * 根据id修改该分类以及对应的父分类状态为都有商品
     *
     * @param classId
     */
    @Override
    public void updateCategoryYesProduct(String classId) {
        List<String> categoryParentIdList = getCategoryParentIdList(classId);
        // 保存成功应该修改该分类为有商品
        LambdaUpdateChainWrapper<ProductCategory> lu = lambdaUpdate();
        lu.in(ProductCategory::getClassId, categoryParentIdList);
        lu.set(ProductCategory::getIsHaveProduct, ProductCategoryEnum.IS_HAVE_PRODUCT_YES.getCode());
        lu.set(ProductCategory::getGmtModified, new Date());
        lu.update();
    }

    /**
     * 获取一级分类
     *
     * @param productType
     * @return
     */
    @Override
    public List<ProductCategory> getLevelOne(Integer productType) {
        List<ProductCategory> list = lambdaQuery().eq(productType != null, ProductCategory::getProductType, productType).eq(ProductCategory::getMallType, mallConfig.mallType).eq(ProductCategory::getClassLevel, 1).eq(ProductCategory::getState, ProductCategoryEnum.TYPE_OPEN.getCode()).list();
        return list;
    }

    /**
     * 根据分类名称和层级查询分类
     *
     * @param className
     * @param level
     * @return
     */
    @Override
    public ProductCategory getCategoryByClassNameAndLevel(String className, Integer level, Integer productType) {
        ProductCategory one = lambdaQuery().eq(ProductCategory::getClassName, className).eq(ProductCategory::getMallType, mallConfig.mallType).eq(productType != null, ProductCategory::getProductType, productType).eq(ProductCategory::getClassLevel, level).eq(ProductCategory::getState, ProductCategoryEnum.TYPE_OPEN.getCode()).one();
        return one;
    }

    public void getParentId(ProductCategory productCategory, List<String> list) {
        list.add(productCategory.getClassId());
        if (StringUtils.isEmpty(productCategory.getParentId())) {
            return;
        } else {
            ProductCategory byId = getById(productCategory.getParentId());
            if (byId == null) {
                return;
            } else {
                getParentId(byId, list);
            }
        }
    }

    /**
     * 递归查找父级
     *
     * @param category
     * @param categories
     * @return
     */
    public static void getParentCategoryObject(List<ProductCategory> list, ProductCategory category, List<ProductCategory> categories) {
        if (StringUtils.isEmpty(category.getParentId()) || "0".equals(category.getParentId())) {
            return;
        }
        ProductCategory tblCategory = categories.stream().filter(x -> Objects.equals(x.getClassId(), category.getParentId())).findFirst().get();
        list.add(tblCategory);
        getParentCategoryObject(list, tblCategory, categories);
    }


//    @Override
//    public ProductCategory getClassName(String className) {
//        LambdaQueryWrapper<ProductCategory> wrapper = new LambdaQueryWrapper<>();
//         wrapper.eq(ProductCategory::getClassName, className);
//         wrapper.eq(ProductCategory::getMallType, 0);
//
//        ProductCategory productCategory = getOne(wrapper);
//        return productCategory;
//    }
//
//
//    @Override
//    public ProductCategory getClassNameAndParentId(String className, String parentId,Integer classLevel) {
//        LambdaQueryWrapper<ProductCategory> wrapper = new LambdaQueryWrapper<>();
//        wrapper.eq(ProductCategory::getClassName, className);
//        wrapper.eq(ProductCategory::getMallType, 0);
//        wrapper.eq(ProductCategory::getProductType,0);
//        wrapper.eq(ProductCategory::getClassLevel,classLevel);
//        wrapper.eq(ProductCategory::getParentId,parentId);
//        ProductCategory  productCategory= getOne(wrapper);
//        return productCategory;
//    }


    @Override
    public ProductCategory getAllChildItem(String className) {
        LambdaQueryWrapper<ProductCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductCategory::getClassName, className);
        ProductCategory one = getOne(wrapper);

        LambdaQueryWrapper<ProductCategory> sonwrapper = new LambdaQueryWrapper<>();
        sonwrapper.eq(ProductCategory::getParentId, one.getClassId());
        List<ProductCategory> list = list(sonwrapper);
        if (list != null && list.size() > 0) {
            one.setChildren(list);
            for (ProductCategory productCategory : list) {
                LambdaQueryWrapper<ProductCategory> q = new LambdaQueryWrapper<>();
                q.eq(ProductCategory::getParentId, productCategory.getClassId());
                List<ProductCategory> threelist = list(q);
                productCategory.setChildren(threelist);
            }
        }

        return one;


    }


    @Override
    public List<ProductCategory> selectThreeCate(String name) {
        List<ProductCategory> list = baseMapper.selectThreeCateList(name);
        return list;
    }


    @Override
    public ProductCategory getDataName(String parentId, String son) {
        LambdaQueryWrapper<ProductCategory> q = new LambdaQueryWrapper<>();
        q.eq(ProductCategory::getParentId, parentId).eq(ProductCategory::getClassName, son);
        return getOne(q);

    }


    @Override
    public List<ProductCategory> getchildItemList(String parentClassId, List<String> sonlist, int classLevel) {
        //查找二级分类的准确id
        LambdaQueryWrapper<ProductCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(ProductCategory::getClassId);
        wrapper.in(ProductCategory::getClassName, sonlist);
        wrapper.eq(ProductCategory::getParentId, parentClassId);
        wrapper.eq(ProductCategory::getClassLevel, classLevel);
        List<ProductCategory> list = list(wrapper);


        //根据二级id查询所有二级的子菜单
        List<String> childClassId = new ArrayList<>();
        list.stream().forEach(i -> childClassId.add(i.getClassId()));
        LambdaQueryWrapper<ProductCategory> q = new LambdaQueryWrapper<>();
        q.select(ProductCategory::getClassId, ProductCategory::getClassName, ProductCategory::getClassLevel, ProductCategory::getClassPath, ProductCategory::getParentId);
        q.in(ProductCategory::getParentId, childClassId);
        List<ProductCategory> childList = list(q);
        return childList;
    }

    @Override
    public List<ProductCategory> getNowchildItemList(String parentClassId, List<String> sonlist, Integer classLevel) {
        LambdaQueryWrapper<ProductCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(ProductCategory::getClassId);
        wrapper.notIn(ProductCategory::getClassName, sonlist);
        wrapper.eq(ProductCategory::getParentId, parentClassId);
        wrapper.eq(ProductCategory::getClassLevel, classLevel);
        List<ProductCategory> list = list(wrapper);
        if (list != null && list.size() > 0) {
            List<ProductCategory> childList = getChilditem(list);
            if (classLevel == 2) {
                List<ProductCategory> sonleaveList = getChilditem(childList);
                return sonleaveList;
            } else if (classLevel == 3) {
                return childList;
            }
        }
        return null;


    }

    private List<ProductCategory> getChilditem(List<ProductCategory> childList) {
        List<String> sonList = childList.stream().map(productCategory -> productCategory.getClassId()).collect(Collectors.toList());
        LambdaQueryWrapper<ProductCategory> sonq = new LambdaQueryWrapper<>();
        sonq.select(ProductCategory::getClassId, ProductCategory::getClassName, ProductCategory::getClassLevel, ProductCategory::getClassPath, ProductCategory::getParentId);
        sonq.in(ProductCategory::getParentId, sonList);
        List<ProductCategory> sonleaveList = list(sonq);
        return sonleaveList;
    }

    @Override
    public void shiftProduct(String oldClassId, String nowClassId) {
        List<Product> products = productService.getDataByClassIdList(oldClassId);
        if (products != null && products.size() > 0) {
            ArrayList<Product> list = new ArrayList<>();
            for (Product product : products) {
                product.setClassId(nowClassId);
                product.setClassPath(getProductClassPath(nowClassId, new StringBuffer(nowClassId)));
                list.add(product);

            }
            productService.updateBatch(list);
        }
//        delete(oldClassId);


    }

    @Override
    public String getProductClassPath(String classId, StringBuffer prouctPath) {
        ProductCategory byId = getById(classId);
        if (byId == null || byId.getParentId() == null) {
            return prouctPath.toString();
        } else {
            prouctPath.insert(0, byId.getParentId() + "/");
            getProductClassPath(byId.getParentId(), prouctPath);
        }
        return prouctPath.toString();
    }

    /**
     * 测试分布式事务
     */
    @GlobalTransactional
    @Override
    public void testClass() {
        ProductCategory productCategory = new ProductCategory();
        productCategory.setClassName("测试分布式事务");
        boolean save = save(productCategory);
        // 调用远程进行注册
        String url = mallConfig.prodPcwp2Url02 + "/thirdapi/matarialpurchase/saveCategoryLibrary";
        CategoryLibrarySave dto = new CategoryLibrarySave();
        dto.setKeyId(IdWorker.getIdStr());
        CategoryLibraryDTO dto2 = new CategoryLibraryDTO();
        dto.setCategoryLibrary(dto2);
//        R r = restTemplateUtils.postPCWP2(url, dto);
//        System.out.println(r);


    }

    /**
     * 新增或修改类别
     *
     * @param dto
     */
    @Override
    @GlobalTransactional
    public void categoryCreateOrUpdate(CreateProductCategoryDTO dto) {
        String idStr = dto.getKeyId();
        Integer saveOrUpdate = dto.getSaveOrUpdate();
        ProductCategory productCategory = new ProductCategory();
        BeanUtils.copyProperties(dto, productCategory);
        // 新增
        if (saveOrUpdate == 1) {
            // 处理重复
            Integer count = lambdaQuery().eq(ProductCategory::getClassLevel, productCategory.getClassLevel()).eq(ProductCategory::getClassName, productCategory.getClassName()).eq(ProductCategory::getProductType, productCategory.getProductType()).eq(productCategory.getParentId() != null, ProductCategory::getParentId, productCategory.getParentId()).count();
            if (count > 0) {
                throw new BusinessException("【本地异常】分类同级名称重复！");
            }
            // 处理层级
            if (StringUtils.isEmpty(productCategory.getParentId())) {
                productCategory.setClassLevel(1);
            } else {
                List<CategoryClassIdAndClassNameVO> pathList = getCategoryParentPath(productCategory.getParentId());
               if (pathList!=null&&pathList.size()>0){
                   productCategory.setClassLevel(pathList.size() + 1);
               }else {
                   return;
               }
            }
            if (productCategory.getParentId() != null) {
                List<CategoryClassIdAndClassNameVO> categoryParentPath = getCategoryParentPath(productCategory.getParentId());
                String classPath = "";
                if (!CollectionUtils.isEmpty(categoryParentPath)) {
                    for (CategoryClassIdAndClassNameVO categoryClassIdAndClassNameVO : categoryParentPath) {
                        if (!StringUtils.isEmpty(categoryClassIdAndClassNameVO.getClassName())) {
                            classPath += categoryClassIdAndClassNameVO.getClassName() + "/";
                        }
                    }
                    classPath += productCategory.getClassName();
                    productCategory.setClassPath(classPath);
                }
            } else {
                productCategory.setClassPath(productCategory.getClassName());
            }
            try {
                save(productCategory);
            } catch (Exception e) {
                LogUtil.writeErrorLog(idStr, "categoryCreateOrUpdate", productCategory, dto, null, e.getMessage(), ProductCategoryServiceImpl.class);
                throw new BusinessException("【本地异常】分类新增失败！");
            }
            // 成功保存日志
            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(idStr);
            iLog.setClassPackage(ProductCategoryServiceImpl.class.getName());
            iLog.setMethodName("categoryCreateOrUpdate");
            iLog.setLocalArguments(JSON.toJSONString(productCategory));
            iLog.setFarArguments(JSON.toJSONString(dto));
            iLog.setIsSuccess(1);
            iLog.setLogType(3);
            interfaceLogsService.create(iLog);
            LogUtil.writeInfoLog(idStr, "categoryCreateOrUpdate", productCategory, dto, null, ProductCategoryServiceImpl.class);
        }
        // 修改
        if (saveOrUpdate == 2) {
            // 处理重复
            Integer count = lambdaQuery().eq(ProductCategory::getClassLevel, productCategory.getClassLevel()).eq(ProductCategory::getClassName, productCategory.getClassName()).eq(ProductCategory::getProductType, productCategory.getProductType()).ne(ProductCategory::getClassId, productCategory.getClassId()).eq(productCategory.getParentId() != null, ProductCategory::getParentId, productCategory.getParentId()).count();
            if (count > 0) {
                throw new BusinessException("【本地异常】分类同级名称重复！");
            }
            if (StringUtils.isEmpty(productCategory.getParentId())) {
                productCategory.setClassLevel(1);
            } else {
                List<CategoryClassIdAndClassNameVO> pathList = getCategoryParentPath(productCategory.getParentId());
                productCategory.setClassLevel(pathList.size() + 1);
            }
            // 成功保存日志
            InterfaceLogs iLog = new InterfaceLogs();
            ProductCategory pr = getById(productCategory.getClassId());
            String updateBefore = JSON.toJSONString(pr);
            iLog.setUpdateBefore(updateBefore);
            iLog.setSecretKey(idStr);
            iLog.setClassPackage(ProductCategoryServiceImpl.class.getName());
            iLog.setMethodName("categoryCreateOrUpdate");
            iLog.setLocalArguments(JSON.toJSONString(productCategory));
            iLog.setFarArguments(JSON.toJSONString(dto));
            iLog.setIsSuccess(1);
            iLog.setLogType(3);
            interfaceLogsService.create(iLog);
            LogUtil.writeInfoLog(idStr, "categoryCreateOrUpdate", productCategory, dto, null, ProductCategoryServiceImpl.class);
            try {
                update(productCategory);
            } catch (Exception e) {
                LogUtil.writeErrorLog(idStr, "categoryCreateOrUpdate", productCategory, dto, null, e.getMessage(), ProductCategoryServiceImpl.class);
                throw new BusinessException("【本地异常】分类修改失败！");
            }
            List<CategoryClassIdAndClassNameVO> categoryParentPath = getCategoryParentPath(productCategory.getClassId());
            String classPath = "";
            if (!CollectionUtils.isEmpty(categoryParentPath)) {
                for (CategoryClassIdAndClassNameVO categoryClassIdAndClassNameVO : categoryParentPath) {
                    if (!StringUtils.isEmpty(categoryClassIdAndClassNameVO.getClassName())) {
                        classPath += categoryClassIdAndClassNameVO.getClassName() + "/";
                    }
                }
            }
            lambdaUpdate().eq(ProductCategory::getClassId, productCategory.getClassId()).set(ProductCategory::getClassPath, classPath.substring(0, classPath.length() - 1)).update();
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMaterialInfo(String keyId, MaterialDtlDTO dto) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        dto.setOrgId(user.getOrgId());
        dto.setOrgName(user.getEnterpriseName());
        if (StringUtils.isEmpty(dto.getBillId())) {
            if (StringUtils.isEmpty(dto.getMaterialName())) {
                throw new BusinessException(500, "物料名称不能为空");
            }
            if (StringUtils.isEmpty(dto.getClassId())) {
                throw new BusinessException(500, "分类Id不能为空");
            } else {
                ProductCategory productCategory = productCategoryMapper.selectById(dto.getClassId());
                dto.setClassName(productCategory.getClassName());
                // TODO 只支持1.0
                // dto.setVersionId();
            }
        }

//        if (dto.getBillId()!=null){
//            List<Product> list = productService.lambdaQuery().eq(Product::getRelevanceId, dto.getBillId()).eq(Product::getState,1).list();
//            if (list.size() > 0) {
//                List<String> num = list.stream().map(Product::getSerialNum).collect(Collectors.toList());
//                String join = String.join(",", num);
//                throw new BusinessException(500, "不能修改该物资，该物资已经关联商品编号有："+join);}
//        }
        HashMap<String, Object> request = new HashMap<>();
        request.put("keyId", keyId);

        request.put("materialDtlInfo", dto);
        R result = null;
        try {
            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(keyId);
            iLog.setClassPackage(ProductCategoryServiceImpl.class.getName());
            iLog.setMethodName("saveMaterialInfo");
            iLog.setLocalArguments(JSON.toJSONString(request));
            iLog.setFarArguments(JSON.toJSONString(dto));
            iLog.setIsSuccess(1);
            iLog.setLogType(3);
            interfaceLogsService.create(iLog);
            log("修改物资参数" + JSON.toJSONString(request));
            result = restTemplateUtils.postPCWP2(mallConfig.prodPcwp2Url02 + CREATE_MATARIAL_URL, request);
            if (result.getCode() == null || result.getCode() != 200) {
                LogUtil.writeErrorLog(keyId, "saveMaterialInfo", dto, request, result, result.getMessage(), MaterialReconciliationServiceImpl.class);
                throw new BusinessException("【远程异常】" + result.getMessage());
            }
            //如果修改物资数据
            if (dto.getBillId()!=null){
                stringRedisTemplate.delete(MaterialLockUtils.CLASS_ID_AND_RELEVANCE_N0_LOCK_KEY + dto.getClassId()+":" +dto.getBillNo());
                if (dto.getIsEnable()==1){
                    //如果启用物资，删除物资基础库缓存
                    stringRedisTemplate.delete(MaterialLockUtils.MATERIAL_NULL+dto.getBillNo());
                }
            }
            //如果修改物资，删除缓存




        } catch (Exception e) {
            log("推送失败" + result);
            e.printStackTrace();
            LogUtil.writeErrorLog(keyId, "saveMaterialInfo", dto, request, result, result.getMessage(), MaterialReconciliationServiceImpl.class);
            throw new BusinessException("【远程异常】" + result.getMessage());

        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void aa(String keyId, MaterialDtlDTO dto) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        dto.setOrgId(user.getOrgId());
        dto.setOrgName(user.getEnterpriseName());
        if (StringUtils.isEmpty(dto.getBillId())) {
            if (StringUtils.isEmpty(dto.getMaterialName())) {
                throw new BusinessException(500, "物料名称不能为空");
            }
            if (StringUtils.isEmpty(dto.getClassId())) {
                throw new BusinessException(500, "分类Id不能为空");
            } else {
                ProductCategory productCategory = productCategoryMapper.selectById(dto.getClassId());
                dto.setClassName(productCategory.getClassName());
                // TODO 只支持1.0
                // dto.setVersionId();
            }
        }

//        if (dto.getBillId()!=null){
//            List<Product> list = productService.lambdaQuery().eq(Product::getRelevanceId, dto.getBillId()).eq(Product::getState,1).list();
//            if (list.size() > 0) {
//                List<String> num = list.stream().map(Product::getSerialNum).collect(Collectors.toList());
//                String join = String.join(",", num);
//                throw new BusinessException(500, "不能修改该物资，该物资已经关联商品编号有："+join);}
//        }
        HashMap<String, Object> request = new HashMap<>();
        request.put("keyId", keyId);

        request.put("materialDtlInfo", dto);
        R result = null;
        try {
            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(keyId);
            iLog.setClassPackage(ProductCategoryServiceImpl.class.getName());
            iLog.setMethodName("saveMaterialInfo");
            iLog.setLocalArguments(JSON.toJSONString(request));
            iLog.setFarArguments(JSON.toJSONString(dto));
            iLog.setIsSuccess(1);
            iLog.setLogType(3);
            interfaceLogsService.create(iLog);
            log("修改物资参数" + JSON.toJSONString(request));
            result = restTemplateUtils.postPCWP2(mallConfig.prodPcwp2Url02 + CREATE_MATARIAL_URL, request);
            if (result.getCode() == null || result.getCode() != 200) {
                LogUtil.writeErrorLog(keyId, "saveMaterialInfo", dto, request, result, result.getMessage(), MaterialReconciliationServiceImpl.class);
                throw new BusinessException("【远程异常】" + result.getMessage());

            }
        } catch (Exception e) {
            log("推送失败" + result);
            e.printStackTrace();
            LogUtil.writeErrorLog(keyId, "saveMaterialInfo", dto, request, result, result.getMessage(), MaterialReconciliationServiceImpl.class);
            throw new BusinessException("【远程异常】" + result.getMessage());

        }

    }
    /**
     * 批量修改启用停用状态
     *
     * @param updateCategorySateByIdDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void thirdApiBatchUpdateClassState(UpdateCategorySateByIdDTO updateCategorySateByIdDTO) {
        String idStr = updateCategorySateByIdDTO.getKeyId();
        List<String> classIds = updateCategorySateByIdDTO.getClassIds();
        List<CategoryClassIdAndClassNameVO> path = getCategoryParentPath(classIds.get(0));
        if (!path.get(0).getClassName().equals("低值易耗品")) {
            throw new BusinessException("【本地异常】只能新增低值易耗品子分类！");
        }
        ProductCategory productCategory = lambdaQuery().eq(ProductCategory::getClassId, classIds.get(0)).select(ProductCategory::getClassLevel).one();
        if (classIds.size() == 1) {
            if (productCategory.getClassLevel() == 1) {
                throw new BusinessException("【本地异常】不能对一级分类进行操作！");
            }
        } else {
            if (productCategory.getClassLevel() == 1) {
                classIds.remove(0);
            }
        }
        // 成功保存日志
        InterfaceLogs iLog = new InterfaceLogs();
        iLog.setSecretKey(idStr);
        iLog.setClassPackage(ProductCategoryServiceImpl.class.getName());
        iLog.setMethodName("thirdApiBatchUpdateClassState");
        iLog.setLocalArguments(null);
        iLog.setFarArguments(JSON.toJSONString(updateCategorySateByIdDTO));
        iLog.setIsSuccess(1);
        iLog.setLogType(3);
        interfaceLogsService.create(iLog);
        LogUtil.writeInfoLog(idStr, "thirdApiBatchUpdateClassState", null, updateCategorySateByIdDTO, null, ProductCategoryServiceImpl.class);

        boolean update = lambdaUpdate().in(ProductCategory::getClassId, updateCategorySateByIdDTO.getClassIds()).set(ProductCategory::getGmtModified, new Date()).set(ProductCategory::getState, updateCategorySateByIdDTO.getState()).update();
        if (!update) {
            throw new BusinessException("修改失败！");
        } else {
            // 删除缓存
            ProductCategory byId = getById(updateCategorySateByIdDTO.getClassIds().get(0));
            if (byId != null) {
                String key = RedisKey.CATEGORY + "::" + byId.getProductType() + "_" + byId.getMallType() + "_getTree";
                stringRedisTemplate.delete(key);
            }
        }
    }

    @Override
    public void outputExcel(JSONObject jsonObject, HttpServletResponse response) {
        String src = mallConfig.templateFormUrl;
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String classId = (String) innerMap.get("classId");
        Integer state = (Integer) innerMap.get("state");
        LambdaQueryWrapper<ProductCategory> wrapper = new LambdaQueryWrapper<>();
//        ProductCategory one = getOne(wrapper.eq(ProductCategory::getClassId, classId).eq(ProductCategory::getState, state));
        ProductCategory one = getOne(wrapper.eq(ProductCategory::getClassName, "低值易耗品").eq(ProductCategory::getState, state).eq(ProductCategory::getClassLevel, 1));
        HashMap<String, Object> categoryMap = new HashMap<>();
        categoryMap.put("className", one.getClassName());
        categoryMap.put("classId", one.getClassId());
        categoryMap.put("state", one.getClassName());
        categoryMap.put("classLassLevel", one.getClassLevel());
        HashMap<String, String> materialInfo = getPCWPMaterial(one.getClassId(), state);
        getChildren(state, categoryMap, materialInfo);


        try {
            XSSFWorkbook wb = new XSSFWorkbook();
            XSSFSheet sheet = wb.createSheet("采购平台商城物资分类");
            XSSFRow headRow = sheet.createRow(0);
            headRow.createCell(0).setCellValue("一级");
            headRow.createCell(1).setCellValue("二级");
            headRow.createCell(2).setCellValue("三级");
            headRow.createCell(3).setCellValue("物资名称");

            int totalRowIndex = 1;
            Row row1 = sheet.createRow(totalRowIndex);
            row1.createCell(0).setCellValue(categoryMap.get("className").toString());
            // 设置行高为30个点
            ArrayList<HashMap> twoList = (ArrayList<HashMap>) categoryMap.get("children");
            for (int i = 0; i < twoList.size(); i++) {
                int twoIndex = totalRowIndex;
                Row row2 = null;
                ArrayList<HashMap> threeList = (ArrayList<HashMap>) twoList.get(i).get("children");
                if (i == 0) {
                    row1.createCell(1).setCellValue(twoList.get(i).get("className").toString());
                } else {
                    totalRowIndex++;
                    row2 = sheet.createRow(totalRowIndex);
                    row2.createCell(1).setCellValue(twoList.get(i).get("className").toString());
                }
                for (int j = 0; j < threeList.size(); j++) {
                    if (i == 0 && j == 0) {
                        row1.createCell(2).setCellValue(threeList.get(j).get("className").toString());
                        row1.createCell(3).setCellValue(threeList.get(j).get("classPath").toString());
                    } else {
                        if (j == 0) {
                            row2.createCell(2).setCellValue(threeList.get(j).get("className").toString());
                            row2.createCell(3).setCellValue(threeList.get(j).get("classPath").toString());
                        } else {
                            totalRowIndex++;
                            Row row3 = sheet.createRow(totalRowIndex);
                            row3.createCell(2).setCellValue(threeList.get(j).get("className").toString());
                            if (threeList.get(j).get("classPath") != null) {
                                row3.createCell(3).setCellValue(threeList.get(j).get("classPath").toString());
                            }
                        }
                    }
                }
                if (twoIndex != 1) {
                    sheet.addMergedRegion(new CellRangeAddress(twoIndex + 1, totalRowIndex, 1, 1));
                } else {
                    sheet.addMergedRegion(new CellRangeAddress(twoIndex, totalRowIndex, 1, 1));
                }

            }
            sheet.addMergedRegion(new CellRangeAddress(1, totalRowIndex, 0, 0));
            String fileName = URLEncoder.encode("test.xlsx", "utf-8");
            response.setContentType("application/x-msdownload; charset=UTF-8");
            response.setHeader("Content-disposition", "attachment; filename=" + fileName);
            OutputStream os = response.getOutputStream();
            wb.write(os);
            os.flush();
            os.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private HashMap<String, String> getPCWPMaterial(String classId, Integer state) {
        HashMap<String, String> materialList = new HashMap<String, String>();
        HashMap<Object, Object> hashMap = new HashMap<>();
        hashMap.put("classId", classId);
        hashMap.put("isActive", state);
        hashMap.put("pageIndex", 1);
        hashMap.put("pageSize", 5000);
        // TODO 
        // hashMap.put("versionId", );
        String url = mallConfig.prodPcwp2Url02 + PCWP2ApiUtil.MATERIAL_PURCHASE_DTL;
        Map<String, Object> data = restTemplateUtils.postHashMapPCWP2(url, hashMap);
        ArrayList<HashMap<String, Object>> list = (ArrayList) data.get("list");
        Map<String, List<HashMap<String, Object>>> materialLists = list.stream().collect(Collectors.groupingBy(item -> item.get("classId").toString()));
        materialLists.forEach((key, value) -> {
            StringBuilder stringBuilder = new StringBuilder();
            List<HashMap<String, Object>> classPathList = value;
            classPathList.stream().forEach(item -> stringBuilder.append(item.get("materialName") + "(编号:" + item.get("billNo") + ",规格:" + item.get("spec") + ",单位:" + item.get("unit") + ");").append('/'));
            materialList.put(key, stringBuilder.toString());
        });


        return materialList;
    }

    private void getChildren(Integer state, HashMap<String, Object> one, HashMap<String, String> materialInfo) {
        if (Integer.parseInt(one.get("classLassLevel").toString()) < 3) {
            LambdaQueryWrapper<ProductCategory> child = new LambdaQueryWrapper<>();
            List<ProductCategory> childs = list(child.eq(ProductCategory::getParentId, one.get("classId")).eq(ProductCategory::getState, state).eq(ProductCategory::getClassLevel, Integer.parseInt(one.get("classLassLevel").toString()) + 1));
            ArrayList<HashMap<String, Object>> hashMaps = new ArrayList<>();
            for (ProductCategory productCategory : childs) {
                HashMap<String, Object> hashMap = new HashMap<>();
                hashMap.put("className", productCategory.getClassName());
                hashMap.put("classId", productCategory.getClassId());
                hashMap.put("state", productCategory.getClassName());
                hashMap.put("classLassLevel", productCategory.getClassLevel());
                getChildren(state, hashMap, materialInfo);
                hashMaps.add(hashMap);

            }
            one.put("children", hashMaps);
        }
        if (Integer.parseInt(one.get("classLassLevel").toString()) == 3) {
            StringBuilder stringBuilder = new StringBuilder();
            HashMap<Object, Object> hashMap = new HashMap<>();
            hashMap.put("classId", one.get("classId"));
            hashMap.put("isActive", state);
            hashMap.put("pageIndex", 1);
            hashMap.put("pageSize", 5000);
//            String url = mallConfig.prodPcwp2Url02 + PCWP2ApiUtil.MATERIAL_PURCHASE_DTL;
//            Map<String, Object> data = restTemplateUtils.postHashMapPCWP2(url, hashMap);
//
//            ArrayList<Map<String, Object>> list = (ArrayList) data.get("list");
//            list.stream().forEach(item -> stringBuilder.append(item.get("materialName")).append('/'));
            one.put("classPath", materialInfo.get(one.get("classId")));
        }
    }


    @Override
    public List<MaterialVo> selectMaterialByclassId(HashMap<String, Object> dto) {

        String url = mallConfig.prodPcwp2Url02 + PCWP2ApiUtil.SELECT_ALL_MATERIAL_INFO_URL;
        List<MaterialVo> list = new ArrayList<>();
        HashMap<String, Object> r = new HashMap();
        // TODO 
        // dto.put("versionId",);
        try {
            r = restTemplateUtils.postMapPCWP2(url, dto);
        } catch (Exception e) {
            throw new BusinessException("【远程接口异常】" + e.getMessage());
        }
        Integer pageIndex = (Integer) dto.get("pageIndex") - 1;
        Integer pageSize = (Integer) dto.get("pageSize");
        Integer totalCount = (Integer) r.get("totalCount");
        if ((pageIndex * pageSize) > totalCount) {
            throw new BusinessException("该物资在基础库不存在，请通知管理员添加物资");
        }

        ArrayList<HashMap<String, Object>> list1 = (ArrayList<HashMap<String, Object>>) r.get("list");
        for (HashMap<String, Object> hashMap : list1) {
            MaterialVo person = JSONObject.parseObject(JSONObject.toJSONString(hashMap), MaterialVo.class);
            list.add(person);
        }


//        list = JSON.parseArray(date, MaterialVo.class);
        if (list != null && list.size() > 0) {
            list.stream().forEach(item -> {
                stringRedisTemplate.opsForValue().set(item.getClassId() + ":" + item.getMaterialName(), JSON.toJSONString(item));
                stringRedisTemplate.expire(item.getClassId() + ":" + item.getMaterialName(), 1800, TimeUnit.SECONDS);
            });

            return list;
        }
        return null;

    }


    @Override
    public ProductCategory getDataByClassPathName(String classpathName, int level, int state) {
        return lambdaQuery().eq(ProductCategory::getClassPath, classpathName).eq(ProductCategory::getClassLevel, level).eq(ProductCategory::getState, state).one();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void batchUpdateMaterialDtlState(String keyId, BatchUpdateMaterialDtlState dto, StringBuilder farArguments) {
        if (dto.getMaterialDtlDTOList() == null && dto.getMaterialDtlDTOList().size() <= 0) {
            throw new BusinessException(500, "请选择物资基础库数据");
        }
        List<String> ids = dto.getMaterialDtlDTOList().stream().map(MaterialDtlDTO::getBillId).collect(Collectors.toList());
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        HashMap<String, Object> request = new HashMap<>();
        request.put("userId", user.getFarUserId());
        request.put("userName", user.getUserName());
        request.put("ids", ids);
        request.put("keyId", keyId);
        request.put("state", dto.getState());
        R result = null;
        try {
            farArguments.append(request);
            log("修改物资基础库参数" + JSON.toJSONString(request));
            result = restTemplateUtils.postPCWP2(mallConfig.prodPcwp2Url02 + PCWP2ApiUtil.BATCH_UPDATE_MATARIAL_DTL_STATE, request);
            if (result.getCode() == null || result.getCode() != 200) {
                LogUtil.writeErrorLog(keyId, "batchUpdateMaterialDtlState", dto, request, result, result.getMessage(), MaterialReconciliationServiceImpl.class);
                throw new BusinessException("【远程异常】" + result.getMessage());

            }
            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(keyId);
            iLog.setClassPackage(ProductCategoryServiceImpl.class.getName());
            iLog.setMethodName("batchUpdateMaterialDtlState");
            iLog.setLocalArguments(JSON.toJSONString(request));
            iLog.setFarArguments(JSON.toJSONString(dto));
            iLog.setIsSuccess(1);
            iLog.setLogType(3);
            interfaceLogsService.create(iLog);
            Boolean state = Boolean.valueOf(dto.getState());
            updateMaterialRedis(dto, state);

        } catch (Exception e) {
            log("推送失败" + result);
            e.printStackTrace();
            LogUtil.writeErrorLog(keyId, "batchUpdateMaterialDtlState", dto, request, result, result.getMessage(), MaterialReconciliationServiceImpl.class);
            throw new BusinessException("【远程异常】" + result.getMessage());

        }


    }

    @Override
    public List<ImportMaterialExcelResultVO> platforUploadMaterialExcelFile(List<MaterialDtlInfos> materialDtlInfos, String keyId, StringBuilder farArguments) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        List<ImportMaterialExcelResultVO> vos = new ArrayList<>();
        ArrayList<HashMap> dtls = new ArrayList<>();
        for (int i = 0,count = materialDtlInfos.size();i<count;i++){
            MaterialDtlInfos materialDtlInfo = materialDtlInfos.get(i);
            ImportMaterialExcelResultVO vo = new ImportMaterialExcelResultVO();
            vo.setId(materialDtlInfo.getId());
            vo.setMaterialName(materialDtlInfo.getMaterialName());
            HashMap<String, Object> dtl = new HashMap<>();
            // 处理分类路径
            if (org.apache.commons.lang.StringUtils.isEmpty(materialDtlInfo.getClassNamePath())) {
                throw new BusinessException(400, "分类不能为空！");
            }
            String[] split = materialDtlInfo.getClassNamePath().trim().split("/");
            ProductCategory productCategory = getDataByClassPathName(materialDtlInfo.getClassNamePath().trim(), split.length, 1);
            if (productCategory == null) {
                throw new BusinessException(400, "分类不存在！");
            }
            boolean isSpecialCharMaterialName = isSpecialChar(materialDtlInfo.getMaterialName());
            MaterialDtlDTO materialDtlDTO = new MaterialDtlDTO();
            BeanUtils.copyProperties(materialDtlDTO,materialDtlInfo);
            filterMaterialDtlDTO(materialDtlDTO);
            if(isSpecialCharMaterialName){
                throw new BusinessException(500, "Excel表格第"+(i+2)+"行【物料名称】"+materialDtlInfo.getMaterialName()+"开头或者结尾不能使用单引号");
            }
            boolean isSpecialCharSpec = isSpecialChar(materialDtlInfo.getSpec());
            if(isSpecialCharSpec){
                throw new BusinessException(500, "Excel表格第"+(i+2)+"行【规格】"+materialDtlInfo.getSpec()+"开头或者结尾不能使用单引号");
            }
            boolean isSpecialCharUnit = isSpecialChar(materialDtlInfo.getUnit());
            if(isSpecialCharUnit){
                throw new BusinessException(500, "Excel表格第"+(i+2)+"行【计量单位】"+materialDtlInfo.getUnit()+"开头或者结尾不能使用单引号");
            }

            dtl.put("classId", productCategory.getClassId());
            dtl.put("className", productCategory.getClassName());
            dtl.put("isEnable", 1);
            dtl.put("orgId", user.getOrgId());
            dtl.put("orgName", user.getEnterpriseName());
            dtl.put("spec", materialDtlInfo.getSpec());
            dtl.put("unit", materialDtlInfo.getUnit());
            dtl.put("materialName", materialDtlInfo.getMaterialName());
            // TODO 物资库id
            // dtl.put("versionId",;
            dtls.add(dtl);
        }

        HashMap<String, Object> request = new HashMap<>();
        request.put("keyId", keyId);
        request.put("materialDtlInfos", dtls);
        String count = JSON.toJSONString(request);
        log("批量导入基础库" + count);
        R result = null;
        try {
            result = restTemplateUtils.postPCWP2(mallConfig.prodPcwp2Url02 + PCWP2ApiUtil.BATH_SAVE_MATERIAL_INFO_URL, request);
            if (result.getCode() == 200) {
                String s = result.getData().toString();
                vos = (ArrayList<ImportMaterialExcelResultVO>) JSON.parseArray(s, ImportMaterialExcelResultVO.class);
                ;
            }
        } catch (Exception e) {
            farArguments.append(result);
            e.printStackTrace();
            LogUtil.writeErrorLog(keyId, "platforUploadMaterialExcelFile", materialDtlInfos, count, result, result.getMessage(), MaterialReconciliationServiceImpl.class);
            throw new BusinessException("【远程异常】" + result.getMessage());
        }
        return vos;
    }

    private void filterMaterialDtlDTO(MaterialDtlDTO dto) {
        if(dto.getMaterialName()!=null){
            dto.setMaterialName(dto.getMaterialName().trim());
        }
        if(dto.getSpec()!=null){
            dto.setSpec(dto.getSpec().trim());
        }
        if(dto.getUnit()!=null){
            dto.setUnit(dto.getUnit().trim());
        }
    }

    private boolean isSpecialChar(String str) {
        boolean specialQuote  = str != null && !str.isEmpty() &&
                (str.charAt(0) == '\'' || str.charAt(str.length() - 1) == '\'');
        return specialQuote;
    }

    @Override
    public void batchUpdateCategoryLibraryState(String keyId, BatchUpdateMaterialDtlState dto, StringBuilder farArguments) {
        if (dto.getMaterialDtlDTOList() == null && dto.getMaterialDtlDTOList().size() <= 0) {
            throw new BusinessException(500, "请选择物资基础库数据");
        }
        List<String> ids = dto.getMaterialDtlDTOList().stream().map(MaterialDtlDTO::getBillId).collect(Collectors.toList());
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        HashMap<String, Object> request = new HashMap<>();
        request.put("userId", user.getFarUserId());
        request.put("userName", user.getUserName());
        request.put("ids", ids);
        request.put("keyId", keyId);
        request.put("state", dto.getState());
        R result = null;
        try {

            log("修改物资分类参数" + JSON.toJSONString(request));
            farArguments.append(request);
            result = restTemplateUtils.postPCWP2(mallConfig.prodPcwp2Url02 + PCWP2ApiUtil.BATCH_UPDATE_CATEGORY_LIBRARY_STATE, request);
            if (result.getCode() == null || result.getCode() != 200) {
                LogUtil.writeErrorLog(keyId, "batchUpdateCategoryLibraryState", dto, request, result, result.getMessage(), MaterialReconciliationServiceImpl.class);
                throw new BusinessException("【远程异常】" + result.getMessage());

            }
            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(keyId);
            iLog.setClassPackage(ProductCategoryServiceImpl.class.getName());
            iLog.setMethodName("batchUpdateCategoryLibraryState");
            iLog.setFarArguments(JSON.toJSONString(request));
            iLog.setLocalArguments(JSON.toJSONString(dto));
            iLog.setIsSuccess(1);
            iLog.setLogType(3);
            interfaceLogsService.create(iLog);
            Boolean state = Boolean.valueOf(dto.getState());
            updateMaterialRedis(dto, state);
        } catch (Exception e) {
            log("推送失败" + result);
            e.printStackTrace();
            LogUtil.writeErrorLog(keyId, "batchUpdateCategoryLibraryState", dto, request, result, result.getMessage(), MaterialReconciliationServiceImpl.class);
            throw new BusinessException("【远程异常】" + result.getMessage());

        }
    }

    private void updateMaterialRedis(BatchUpdateMaterialDtlState dto, Boolean state) {
        if (state){
            for (MaterialDtlDTO materialDtlDTO : dto.getMaterialDtlDTOList()) {
                stringRedisTemplate.opsForValue().set(MaterialLockUtils.CLASS_ID_AND_RELEVANCE_N0_LOCK_KEY +
                        materialDtlDTO.getClassId()+":" +materialDtlDTO.getBillNo(),JSONObject.toJSONString(materialDtlDTO),30, MINUTES);

            }
        }else {
            for (MaterialDtlDTO materialDtlDTO : dto.getMaterialDtlDTOList()) {
                stringRedisTemplate.opsForValue().set(MaterialLockUtils.MATERIAL_NULL
                        +materialDtlDTO.getBillNo(),"-100", 1, MINUTES);
                stringRedisTemplate.delete(MaterialLockUtils.CLASS_ID_AND_RELEVANCE_N0_LOCK_KEY + materialDtlDTO.getClassId()+":" +materialDtlDTO.getBillNo());
            }
        }
    }


    @Override
    @Transactional
    public void synchronizationProductCategory() {
        //获取pcwp所有的分类
        String url = "http://pcwp2.scrbg.com/thirdApi/thirdapi/matarialpurchase/getAllCategoryLibrary";
        R<List<Map>> r = restTemplateUtils.getPCWP2NotParams(url);
        List<Map> data = r.getData();
        List<Map> collect = data.stream().filter(item -> !item.containsKey("a927249b2810-a00f-1d43-ef73-78cbd5be")).collect(Collectors.toList());
        for (Map map : collect) {
//            lambdaQuery().eq(ProductCategory::getClassId,map.get())
        }

        //查询所有我方数据库中所有启动的1级分类
        List<ProductCategory> list = lambdaQuery().eq(ProductCategory::getClassLevel, 1).eq(ProductCategory::getState, 1).ne(ProductCategory::getClassId, "a927249b2810-a00f-1d43-ef73-78cbd5be").list();
        /**
         * 遍历分类
         */
        for (ProductCategory productCategory : list) {

        }
    }

    @Override
    public List<String> getClassIdsByVersionId(String versionId) {
        return this.lambdaQuery()
            .eq(ProductCategory::getVersionId, versionId)
            .list()
            .stream()
            .map(ProductCategory::getClassId)
            .collect(Collectors.toList());
    }
}
