package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import scrbg.meplat.mall.entity.EnterpriseInfo;
import scrbg.meplat.mall.entity.File;
import scrbg.meplat.mall.entity.ShopSupplierRele;
import scrbg.meplat.mall.entity.excelTemplate.Supplier;
import scrbg.meplat.mall.service.EnterpriseInfoService;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.excel.EasyExcelUtils;
import scrbg.meplat.mall.vo.platform.enterprise.EnterpriseArrearageVo;
import scrbg.meplat.mall.vo.user.ImportSupplerExcelResultVO;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * @描述：控制类
 * @作者: y
 * @日期: 2022-11-03
 */
@Log4j2
@RestController
@RequestMapping("/platform/enterpriseInfo")
@ApiSort(value = 500)
@Api(tags = "企业")
public class EnterpriseInfoController {

    @Autowired
    public EnterpriseInfoService enterpriseInfoService;

    @PostMapping("/findByConditionByPage")
    @ApiOperation(value = "根据实体属性分页查询(企业和个体户)")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "startDate", value = "创建时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endDate", value = "创建时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "enterpriseName", value = "企业名称", dataTypeClass = String.class),
            @DynamicParameter(name = "enterpriseType", value = "企业类型", dataTypeClass = Integer.class),
            @DynamicParameter(name = "city", value = "城市", dataTypeClass = String.class),
            @DynamicParameter(name = "isSupplier", value = "是否为供应商：0 : 否 1 ：待定  2：是", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
            @DynamicParameter(name = "orderBy", value = "排序方式", dataTypeClass = Integer.class),
    })
    public PageR<EnterpriseInfo> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = enterpriseInfoService.queryPage(jsonObject, new LambdaQueryWrapper<>());
        return PageR.success(page);
    }

    @PostMapping("/findByConditionByPageAll")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "startDate", value = "创建时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endDate", value = "创建时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "enterpriseName", value = "企业名称", dataTypeClass = String.class),
            @DynamicParameter(name = "enterpriseType", value = "企业类型", dataTypeClass = Integer.class),
            @DynamicParameter(name = "state", value = "企业状态", dataTypeClass = Integer.class),
            @DynamicParameter(name = "city", value = "城市", dataTypeClass = String.class),
            @DynamicParameter(name = "isSupplier", value = "是否供应商", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
            @DynamicParameter(name = "interiorId", value = "内部企业Id(true 内部，false(外部))", dataTypeClass = Boolean.class),
            @DynamicParameter(name = "orderBy", value = "排序方式", dataTypeClass = Integer.class),
    })
    public PageR<EnterpriseInfo> listByEntityAll(@RequestBody JSONObject jsonObject) {
        PageUtils page = enterpriseInfoService.queryPageAll(jsonObject, new LambdaQueryWrapper<>());
        return PageR.success(page);
    }


    // 检测企业蜀道企业数据
    @GetMapping("/sd/sdStatus")
    @ApiOperation(value = "蜀道企业检测")
    public R enterpriseShuDaoStatus(){
        enterpriseInfoService.enterpriseShuDaoStatus();
        return R.success();
    }


    @GetMapping("/getEnterpriseInfoTaxRate")
    @ApiOperation(value = "获取当前企业税率")
    public R getEnterpriseInfoTaxRate(){
        BigDecimal enterpriseInfoTaxRate = enterpriseInfoService.getEnterpriseInfoTaxRate();
        return R.success(enterpriseInfoTaxRate);
    }
    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "enterpriseId", value = "ID", required = false,
                    dataType = "String", paramType = "query")
    })
    public R<EnterpriseInfo> findById(@RequestParam("enterpriseId") String enterpriseId) {
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.getById(enterpriseId);
        return R.success(enterpriseInfo);
    }


    @GetMapping("/getTwoSupplierInfo")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "enterpriseId", value = "ID", required = false,
                    dataType = "String", paramType = "query")
    })
    public R<EnterpriseInfo> getTwoSupplierInfo() {
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.getById(enterpriseId);
        return R.success(enterpriseInfo);
    }

    @PostMapping("/getEnterpriseList")
    @ApiOperation(value = "根据供应商店铺查询对应关联的企业")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
    })
    public PageR<ShopSupplierRele> getEnterpriceList(@RequestBody JSONObject jsonObject) {
        PageUtils page = enterpriseInfoService.getEnterpriceList(jsonObject, new LambdaQueryWrapper<EnterpriseInfo>());
        return PageR.success(page);
    }
    @GetMapping("/selectListByEnterPriseName")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "enterpriseName", value = "供应商名称", required = false,
                    dataType = "String", paramType = "query")
    })
    public R<List<EnterpriseInfo>> selectEnterpriseListByEnterPriseName(@RequestParam("enterpriseName") String enterpriseName) {
        List<EnterpriseInfo> enterpriseInfo = enterpriseInfoService.selectListByEnterPriseName(enterpriseName);
        return R.success(enterpriseInfo);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody EnterpriseInfo enterpriseInfo) {
        enterpriseInfoService.create(enterpriseInfo);
        return R.success();
    }

    @PostMapping("/updateIsSupplier")
    @ApiOperation(value = "批量修改供应商")
    public R updateIsSupplier(@RequestBody List<EnterpriseInfo> enterpriseInfos) {
        enterpriseInfoService.updateBatchAndShopById(enterpriseInfos);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        enterpriseInfoService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")

    public R deleteBatch(@RequestBody List<String> ids) {
        enterpriseInfoService.removeByIds(ids);
        return R.success();
    }


    @GetMapping("/getInfo")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "企业id", value = "enterpriseId", required = true,
                    dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "根据登陆用户获取当前企业")
    public R<EnterpriseInfo> getInfo(String enterpriseId, Integer mallType, String shopId) {
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.getInfo(enterpriseId,mallType,shopId);
        return R.success(enterpriseInfo);
    }
    @GetMapping("/getEnterFileList")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "企业id", value = "enterpriseId", required = true,
                    dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "根据企业id获取附件列表")
    public R<List<File>> getEnterFileList(String enterpriseId) {
        List<File> files = enterpriseInfoService.getEnterFileList(enterpriseId);
        return R.success(files);
    }

    @GetMapping("/excel/supplierTemplate")
    @ApiOperation(value = "供应商导入模板")
    public void supplierTemplate(HttpServletResponse response) {
        try {
            EasyExcelUtils.writeWeb("供应商导入模板", Supplier.class, null, "供应商导入模板", response);
        } catch (Exception e) {
            log.error("异常信息：" + e.getMessage());
        }
    }

//    @PostMapping("/product/uploadSupplierExcelFile")
//    @ApiOperation(value = "导入供应商及诶扣")
//    public R uploadSupplierExcelFile(@RequestPart("file") MultipartFile file) {
//        List<ImportSupplerExcelResultVO> vos = enterpriseInfoService.uploadSupplierExcelFile(file);
//        return R.success(vos);
//    }
    @PostMapping("/excel/uploadSupplierExcelFile")
    @ApiOperation(value = "导入供应商")
    public void uploadSupplierExcelFile(@RequestPart("file") MultipartFile file,HttpServletResponse response) {
        List<ImportSupplerExcelResultVO> vos = enterpriseInfoService.uploadSupplierExcelFile(file);
        try {
            EasyExcelUtils.writeWeb2("供应商导入结果", ImportSupplerExcelResultVO.class, vos, "供应商导入结果", response);
        } catch (Exception e) {
            log.error("异常信息：" + e.getMessage());
        }
    }

    @GetMapping("/updateIsSupplierById")
    @ApiOperation(value = "设置内部用户为供应商,修改税率")
    public R updateIsSupplierById(String id, String taxRate) {
       enterpriseInfoService.updateIsSupplierById(id,taxRate);
       return R.success();
    }


    @PostMapping("/updateArrearageBatch")
    @ApiOperation(value = "批量修改欠费数据和可欠费时间")
    public R updateArrearageBatch(@RequestBody  List<EnterpriseInfo> enterpriseInfos) {
        enterpriseInfoService.updateArrearageBatch(enterpriseInfos);
        return R.success();
    }


    @PostMapping("/sd/updateBatchArrearage")
    @ApiOperation(value = "批量修改欠费数据和可欠费时间")
    public R updateBatchArrearage(@RequestBody  EnterpriseArrearageVo vo) {
        enterpriseInfoService.updateBatchArrearage(vo);
        return R.success();
    }




}

