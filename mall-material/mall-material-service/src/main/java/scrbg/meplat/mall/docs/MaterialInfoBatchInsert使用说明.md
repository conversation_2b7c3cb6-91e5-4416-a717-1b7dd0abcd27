# 物资信息批量插入功能使用说明

## 功能概述

本功能实现了将查询结果 `PcwpPageRes<MaterialNew>` 批量插入到 `material_info` 表的完整解决方案。

## 核心组件

### 1. MaterialInfo 实体类
- 路径：`scrbg.meplat.mall.entity.MaterialInfo`
- 对应数据库表：`material_info`
- 包含所有表字段的映射

### 2. MaterialInfoMapper 接口
- 路径：`scrbg.meplat.mall.mapper.MaterialInfoMapper`
- 提供批量插入方法：`batchInsert(List<MaterialInfo> materialInfoList)`
- 提供查询方法用于去重检查

### 3. MaterialInfoService 服务类
- 接口：`scrbg.meplat.mall.service.MaterialInfoService`
- 实现：`scrbg.meplat.mall.service.impl.MaterialInfoServiceImpl`
- 核心方法：
  - `batchInsertFromMaterialNew(List<MaterialNew> materialNewList)` - 从MaterialNew列表批量插入
  - `convertFromMaterialNew(MaterialNew materialNew)` - 数据转换
  - `isExist(MaterialNew materialNew)` - 重复检查

### 4. MaterialInfoBatchUtil 工具类
- 路径：`scrbg.meplat.mall.utils.MaterialInfoBatchUtil`
- 提供高级批量处理功能：
  - `processPcwpPageRes(PcwpPageRes<MaterialNew> pcwpPageRes)` - 处理分页结果
  - `processMultiplePcwpPageRes(List<PcwpPageRes<MaterialNew>> pcwpPageResList)` - 处理多页结果
  - `validateMaterialNew(MaterialNew materialNew)` - 数据验证

## 使用方式

### 1. 基本使用 - 单页批量插入

```java
@Autowired
private MaterialInfoBatchUtil materialInfoBatchUtil;

@Autowired
private PcwpService pcwpService;

// 查询数据
MaterialPageDto materialPageDto = new MaterialPageDto();
materialPageDto.setClassId("your-class-id");
materialPageDto.setPageIndex(1);
materialPageDto.setPageSize(100);

PcwpPageRes<MaterialNew> pcwpPageRes = pcwpService.queryPageMaterialDtl(materialPageDto);

// 批量插入
int insertedCount = materialInfoBatchUtil.processPcwpPageRes(pcwpPageRes);
```

### 2. 高级使用 - 多页批量插入

```java
@Autowired
private ProductService productService;

// 使用ProductService中的封装方法
int insertedCount = productService.batchInsertMaterialInfo(pcwpPageRes);
```

### 3. REST API 调用

#### 单页批量插入
```http
POST /material-info/batch-insert
Content-Type: application/json

{
    "classId": "your-class-id",
    "versionId": "your-version-id",
    "pageIndex": 1,
    "pageSize": 100,
    "isActive": 1
}
```

#### 多页批量插入
```http
POST /material-info/batch-insert-multiple-pages
Content-Type: application/json

{
    "classId": "your-class-id",
    "versionId": "your-version-id",
    "pageIndex": 1,
    "pageSize": 1000,
    "isActive": 1
}
```

#### 根据类别ID批量插入
```http
POST /material-info/batch-insert-by-class/{classId}?versionId=your-version-id
```

## 数据转换规则

### MaterialNew -> MaterialInfo 字段映射

| MaterialNew字段 | MaterialInfo字段 | 说明 |
|----------------|------------------|------|
| sort | sort | 排序字段 |
| billId | billId | 单据ID |
| billNo | billNo | 单据编号 |
| classId | classId | 类别Id |
| classIdPath | classIdPath | 类别Id路径 |
| className | className | 类别名称 |
| classNamePath | classNamePath | 类别名称路径 |
| isEnable | isEnable | 是否启用 |
| materialName | materialName | 物资名称 |
| orgId | orgId | 组织ID |
| orgName | orgName | 组织名称 |
| spec | spec | 规格 |
| topClassId | topClassId | 顶级类别Id |
| topClassName | topClassName | 顶级类别名称 |
| unit | unit | 单位 |
| versionId | versionId | 物资库版本Id |

### 系统自动填充字段

- `mallType`: 默认为 0 (物资商城)
- `gmtCreate`: 当前时间
- `gmtModified`: 当前时间
- `isDelete`: 默认为 0 (未删除)
- `founderId`: 当前登录用户ID
- `founderName`: 当前登录用户名称
- `modifyId`: 当前登录用户ID
- `modifyName`: 当前登录用户名称

## 重复数据处理

系统会自动检查重复数据，避免重复插入：

1. **按单据ID和单据编号检查**：如果 `billId` 和 `billNo` 都不为空，则根据这两个字段检查是否已存在
2. **按物资名称和类别ID检查**：如果 `materialName` 和 `classId` 都不为空，则根据这两个字段检查是否已存在

## 性能优化

1. **分批处理**：每批处理1000条记录，避免内存溢出
2. **事务控制**：使用 `@Transactional` 确保数据一致性
3. **重复检查**：在插入前过滤掉已存在的记录
4. **数据验证**：在插入前验证必填字段

## 错误处理

- 所有异常都会被捕获并记录日志
- 提供详细的错误信息返回给调用方
- 支持事务回滚，确保数据一致性

## 日志记录

系统会记录详细的操作日志：
- 批量插入开始和结束
- 每批处理的记录数
- 过滤掉的重复记录数
- 异常信息和堆栈跟踪

## 注意事项

1. 确保数据库表 `material_info` 已创建
2. 确保当前用户已登录（用于填充创建人信息）
3. 大批量数据插入时注意数据库连接超时设置
4. 建议在非业务高峰期进行大批量数据插入操作
