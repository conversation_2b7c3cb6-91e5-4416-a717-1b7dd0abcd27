package scrbg.meplat.mall.pcwp.third;

import java.util.List;
import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import scrbg.meplat.mall.pcwp.FeignConfig;
import scrbg.meplat.mall.pcwp.PcwpClient;
import scrbg.meplat.mall.pcwp.PcwpPageRes;
import scrbg.meplat.mall.pcwp.PcwpRes;
import scrbg.meplat.mall.pcwp.third.model.Material;
import scrbg.meplat.mall.pcwp.third.model.MaterialNew;
import scrbg.meplat.mall.pcwp.third.model.MaterialPageDto;

/**
 * 物资管理接口
 */
@FeignClient(name = "pcwp-material-service", url = "${mall.prodPcwp2Url02}", configuration=FeignConfig.class)
public interface PcwpMaterialClient extends PcwpClient{

    /**
     * 获取所有物资类别信息（提供给物资采购平台）
     * @return
     */
    @GetMapping("thirdapi/matarialpurchase/getAllCategoryLibrary")
    PcwpRes<List<Material>> getAllCategoryLibrary(@RequestHeader("token") String token,
                                                @RequestHeader("syscode") String syscode);
    /**
     * 获取机构对应的物资基础库版本
     * @return
     */
    @GetMapping("thirdapi/matarialpurchase/getVersionByOrgId")
    PcwpRes<Map<String, String>> getVersionByOrgId(@RequestParam String orgId,
                                                @RequestHeader("token") String token,
                                                @RequestHeader("syscode") String syscode);

    /**
     * 通过分页加载物资信息
     * @param materialPageDto
     * @param token
     * @param syscode
     * @return
     */
    @PostMapping("thirdapi/matarialpurchase/queryPageMaterialDtl")
    PcwpPageRes<MaterialNew> queryPageMaterialDtl(@RequestBody MaterialPageDto materialPageDto,
                                                  @RequestHeader("token") String token,
                                                  @RequestHeader("syscode") String syscode);
}
