package scrbg.meplat.mall.vo.user.userCenter;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class NegotiatedPriceVo {

    @ApiModelProperty(value = "报价id")
    private String negotiatedPriceId;

    @ApiModelProperty(value = "报价金额")

    private BigDecimal enquiryAmount;

    @ApiModelProperty(value = "报价类型（0求购2求租3招标）")

    private Integer type;
    @ApiModelProperty(value = "昵称")

    private String nickName;

    @ApiModelProperty(value = "头像")

    private String userImg;
    @ApiModelProperty(value = "创建日期")
    private Date gmtCreate;




}
