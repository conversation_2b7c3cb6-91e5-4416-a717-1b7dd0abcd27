package scrbg.meplat.mall.vo.platform;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.vo.PagesVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 平台订单统计
 */
@Data
public class PlatformShopCountVo  extends PagesVo {

    @ApiModelProperty(value = "订单数量")

    private String ordersCount;
    @ApiModelProperty(value = "总利润")

    private BigDecimal profitPriceTotal;
    @ApiModelProperty(value = "总成本价")

    private BigDecimal costPriceTotal;
    @ApiModelProperty(value = "订单总价格")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "实际支付订单总价格")
    private BigDecimal actualAmount;

    @ApiModelProperty(value = "商铺订单汇总")
    List<ShopCountVo> shopCountVoList;

}
