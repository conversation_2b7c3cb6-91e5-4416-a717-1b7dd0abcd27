package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.plutext.jaxb.svg11.G;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.controller.website.userCenter.UserCenterUserInfoController;
import scrbg.meplat.mall.dto.product.material.lcProduct.ProductCardZone;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.enums.product.OrderEnum;
import scrbg.meplat.mall.enums.product.ProductEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.ShoppingCartMapper;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.service.pcwpApi.Pcwp1Servise;
import scrbg.meplat.mall.util.*;
import scrbg.meplat.mall.vo.product.material.CategoryClassIdAndClassNameVO;
import scrbg.meplat.mall.vo.product.website.SubmitOrderByPlanVO;
import scrbg.meplat.mall.vo.product.website.SubmitOrderProductItemInfoByPlanVO;
import scrbg.meplat.mall.vo.product.website.WCartInfoVO;
import scrbg.meplat.mall.vo.product.website.WCartItemVO;
import scrbg.meplat.mall.vo.user.userCenter.IsSynthesizeTemporaryItemVO;
import scrbg.meplat.mall.vo.user.userCenter.IsSynthesizeTemporaryVO;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @描述：购物车 服务类
 * @作者: y
 * @日期: 2022-11-02
 */
@Service
public class ShoppingCartServiceImpl extends ServiceImpl<ShoppingCartMapper, ShoppingCart> implements ShoppingCartService {

    @Autowired
    public MallConfig mallConfig;
    @Autowired
    public UserAddressService userAddressService;

    @Autowired
    SystemParamService systemParamService;

    @Autowired
    private ProductCollectService productCollectService;
    @Autowired
    ProductCategoryService productCategoryService;

    @Autowired
    BrandService brandService;

    @Autowired
    private ProductService productService;


    @Autowired
    private ShopBusinessService shopBusinessService;

    @Autowired
    private InterfaceLogsService interfaceLogsService;

    @Autowired
    private ProductSkuService productSkuService;

    @Autowired
    private ShoppingCartMapper shoppingCartMapper;

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private RestTemplateUtils restTemplateUtils;
    @Autowired
    private ShopService shopService;

    @Autowired
    private EnterpriseInfoService enterpriseInfoService;


    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ShoppingCart> queryWrapper) {
        IPage<ShoppingCart> page = this.page(
                new Query<ShoppingCart>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(ShoppingCart shoppingCart) {
        super.save(shoppingCart);
    }

    @Override
    public void update(ShoppingCart shoppingCart) {
        super.updateById(shoppingCart);
    }


    @Override
    public ShoppingCart getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    /**
     * 清空购物车
     *
     * @param productType
     */
    @Override
    public void emptyCart(Integer productType) {
        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
        Integer mallType = mallConfig.mallType;
        baseMapper.emptyCart(userId, mallType);
//        lambdaUpdate().eq(ShoppingCart::getUserId,userId)
//                .eq(ShoppingCart::getProductType,productType)
//                .eq(ShoppingCart::getMallType,mallConfig.mallType).remove();
    }

    /**
     * 修改购物车数量
     *
     * @param id
     * @param changeNum
     */
    @Override
    public void changeCartNum(String id, BigDecimal changeNum) {
        LambdaUpdateChainWrapper<ShoppingCart> set = lambdaUpdate().eq(ShoppingCart::getCartId, id);
        if (changeNum == null) {
            set.set(ShoppingCart::getCartNum, 1);
        } else {
            set.set(ShoppingCart::getCartNum, changeNum);
        }
        set.set(ShoppingCart::getGmtModified, new Date()).update();
    }

    /**
     * 添加购物车
     *
     * @param productId
     * @param cartNum
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addCart(String productId, BigDecimal cartNum) {
        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
        ShoppingCart cart = new ShoppingCart();
        Product product = productService.getById(productId);
        //判断商品是否自营店
        List<String>  s = shopBusinessService.getStopBusiness();
        if (s.contains(product.getShopId())){
            throw  new BusinessException("该自营店的已经停止使用，请到新自营店选择商品加入购物车");
        }
        ProductSku sku = productSkuService.lambdaQuery().eq(ProductSku::getProductId, productId).one();
        if (product == null || sku == null) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "操作失败！商品不存在或已下架！");
        }
        BigDecimal stock = sku.getStock();
        if (cartNum.compareTo(stock) == 1) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "库存不足！剩余库存【" + stock + "】");
        }
        // 如果有则相加
        ShoppingCart one = lambdaQuery().eq(ShoppingCart::getProductId, productId)
                .eq(ShoppingCart::getSkuId, sku.getSkuId())
                .eq(ShoppingCart::getUserId, userId)
                .one();
        if (one != null) {

            BigDecimal cartNum1 = one.getCartNum();
            BigDecimal newCartNum = cartNum1.add(cartNum);
            one.setTaxRate(product.getTaxRate());
            lambdaUpdate().eq(ShoppingCart::getCartId, one.getCartId())
                    .set(ShoppingCart::getCartNum, newCartNum)
                    .set(ShoppingCart::getProductPrice, sku.getSellPrice())
                    .set(ShoppingCart::getGmtModified, new Date()).update();
        } else {
            cart.setTaxRate(product.getTaxRate());
            cart.setLeaseUnit(sku.getLeaseUnit());
            cart.setProductId(productId);
            cart.setShopId(product.getShopId());
            cart.setSkuId(sku.getSkuId());
            cart.setUserId(userId);
            cart.setCartNum(cartNum);
            cart.setCartTime(new Date());
            cart.setProductPrice(sku.getSellPrice());
            cart.setSkuProps(sku.getSkuName());
            cart.setProductType(product.getProductType());
            cart.setCartImg(sku.getSkuImg());
            save(cart);
        }
    }

    /**
     * 根据商品类型获取购物车列表
     *
     * @param productType
     */
    @Override
    public List<WCartInfoVO> listCart(Integer productType) {
        BigDecimal totalPrice = new BigDecimal(0);
        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
        Integer mallType = mallConfig.mallType;
        List<WCartInfoVO> vos = shoppingCartMapper.getMyCartList(null, userId, mallType);
        if (CollectionUtils.isEmpty(vos)) return vos;
        for (WCartInfoVO vo : vos) {
            List<WCartItemVO> productInfo = vo.getProductInfo();
            boolean flag = false;
            for (WCartItemVO wCartItemVO : productInfo) {
                if (wCartItemVO.getChecked() == false) {
                    flag = true;
                }
                BigDecimal productPrice = wCartItemVO.getProductPrice();
                // 分类名称
                ProductCategory productCategory = productCategoryService.getProductCategoryById(wCartItemVO.getClassId(), null);
                if (productCategory != null) wCartItemVO.setClassName(productCategory.getClassName());

                //品牌名称
                Brand brand = brandService.getById(wCartItemVO.getBrandId());
                if (brand != null) wCartItemVO.setBrandName(brand.getName());

                //规格
                ProductSku productSku = productSkuService.getById(wCartItemVO.getSkuId());
                if (productSku != null) {
                    BeanUtils.copyProperties(productSku, wCartItemVO);
                }
                wCartItemVO.setSellPrice(productPrice);
                wCartItemVO.setProductMinPrice(productPrice);
                // 计算金额
                BigDecimal multiply = wCartItemVO.getSellPrice().multiply(wCartItemVO.getCartNum().setScale(2, RoundingMode.HALF_UP));
                wCartItemVO.setNumTotalPrice(multiply);
                totalPrice = totalPrice.add(multiply);

                // 查询是否收藏
                boolean isCollectByProductId = productCollectService.getIsCollectByProductId(wCartItemVO.getProductId());
                wCartItemVO.setCollect(isCollectByProductId);
            }
            if (flag) {
                vo.setChecked(false);
            } else {
                vo.setChecked(true);
            }
        }

        vos.get(0).setTotalPrice(totalPrice);

        ShoppingCart one = lambdaQuery().eq(ShoppingCart::getUserId, userId)
                .eq(ShoppingCart::getChecked, 1).last("limit 1").one();
        if (one == null) {
            vos.get(0).setSubmitStr("请勾选商品");
        } else {
            if (one.getProductType() == 0) {
                vos.get(0).setSubmitStr("推送零星采购计划");
            }
            if (one.getProductType() == 1) {
                vos.get(0).setSubmitStr("生成大宗临购清单");
            }
        }
        return vos;
    }

    /**
     * 主键集合批量真实删除
     *
     * @param ids
     */
    @Override
    public void removeRealByIds(List<String> ids) {
        baseMapper.removeRealByIds(ids);
    }

    /**
     * 根据店铺id删除所有购物车
     *
     * @param shopId
     */
    @Override
    public void removeRealByShopId(String shopId) {
        Integer mallType = mallConfig.mallType;
        baseMapper.removeRealByShopId(shopId, mallType);
    }

    /**
     * 获取当前用户的购物车数量
     *
     * @return
     */
    @Override
    public int getCartNum() {
        Integer count = lambdaQuery().eq(ShoppingCart::getUserId, ThreadLocalUtil.getCurrentUser().getUserId()).count();
        if (count == null) {
            return 0;
        } else {
            return count;
        }
    }

    /**
     * 修改购物车租赁时长
     *
     * @param id
     * @param changeNum
     */
    @Override
    public void leaseCountCart(String id, BigDecimal changeNum) {
        LambdaUpdateChainWrapper<ShoppingCart> set = lambdaUpdate().eq(ShoppingCart::getCartId, id);
        if (changeNum != null) {
            set.set(ShoppingCart::getLeaseNum, changeNum);
        }
        set.set(ShoppingCart::getGmtModified, new Date()).update();
    }



    @Autowired
    private Pcwp1Servise pcwp1Servise;

    /**
     * 推送商品到计划
     *
     * @param cardId
     * @param farArg
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pushCardProductsArrive(List<String> cardId, String idStr, StringBuilder farArg) {
        List<ShoppingCart> list = lambdaQuery().in(ShoppingCart::getCartId, cardId).list();
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException("购物车数据不存在！");
        }
        for (ShoppingCart cart : list) {
            shopService.auditArrearageByShopId(cart.getShopId());
            String productId = cart.getProductId();
            // 查看购物车的商品是否还上架未删除
            Product product = productService.getProductById(productId, ProductEnum.STATE_PUTAWAY.getCode());
            if (product == null) {
                Product byId = productService.getProductExcludeRemarkById(productId);

                if (byId != null) {
                    throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "商品名为：【" + byId.getProductName() + "】的商品已下架或已被删除！");
                } else {
                    throw new BusinessException(OrderEnum.RESULT_CODE_500201.getCode(), OrderEnum.RESULT_CODE_500201.getRemark());
                }
            } else {
                if (product.getProductType() != 0) {
                    throw new BusinessException(500, "商品名为：【" + product.getProductName() + "】不是低值易耗品不能推送计划！");
                }
                int taxRate = cart.getTaxRate().compareTo(product.getTaxRate());
                if (taxRate != 0) {
                    throw new BusinessException(500, "【" + product.getProductName() + "】商品税率发生变化！，请删除上商品，重新添加购物车后推送计划");
                }
                ProductCategory productCategory = productCategoryService.getById(product.getClassId());
                if (productCategory == null) {
                    throw new BusinessException(500, "商品名为：【" + product.getProductName() + "】的类别不存在");
                } else {
                    String classId = productCategory.getClassId();
                    List<CategoryClassIdAndClassNameVO> categoryParentPath = productCategoryService.getCategoryParentPath(classId);
                    if (!categoryParentPath.get(0).getClassName().equals("低值易耗品")) {
                        throw new BusinessException(500, "商品名为：【" + product.getProductName() + "】不是低值易耗品不能推送计划！");
                    }
                }
                // 查询已上架未删除
                ProductSku productSku = productSkuService.getProductSkuById(cart.getSkuId(), PublicEnum.STATE_OPEN.getCode());
                if (productSku == null) {
                    // 直接查询
                    ProductSku byId = productSkuService.getById(cart.getSkuId());
                    if (byId != null) {
                        throw new BusinessException(500, "商品名为：【" + product.getProductName() + "】商品的【" + byId.getSkuName() + "】规格已下架或已被删除！");
                    } else {
                        throw new BusinessException(500, "订单保存失败！");
                    }
                } else {
                    // 如果库存不足
                    int i = cart.getCartNum().compareTo(productSku.getStock());
                    if (i == 1) {
                        throw new BusinessException(500, "【" + product.getProductName() + "】商品库存不足!");
                    }
                    BigDecimal sellPrice = productSku.getSellPrice();
                    // 判断价格 计划金额小于了现有金额
                    int priceRes = cart.getProductPrice().compareTo(sellPrice);
                    if (priceRes != 0) {
                        throw new BusinessException(500, "【" + product.getProductName() + "】商品价格发生变化！");
                    }
//                    actualAmount = actualAmount.add(sellPrice.multiply(cart.getCartNum()));
                }
            }
        }

        // 封装数据
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        HashMap<String, Object> params = new HashMap<>();
        HashMap<String, Object> entity = new HashMap<>();
        LocalDate now = LocalDate.now();
        int year = now.getYear();
        int month = now.getMonthValue();
        //pcwp1;梁俊对接 删除该字段
        entity.put("NotTaxPlanAmount", 1000);
        entity.put("PlanAmount", 1000);
        entity.put("Year", year);
        entity.put("Month", month);
        String s = DateUtil.getyyymmddHHmmssStr(new Date());
        entity.put("BillDate", s);
        entity.put("OrgId", user.getOrgId());
        entity.put("OrgName", user.getEnterpriseName());
        entity.put("RecorderId", user.getFarUserId());
        entity.put("RecorderName", user.getUserName());
        // 计划明细
        ArrayList<Map> details = new ArrayList<>();
        ArrayList<String> cartIds = new ArrayList<>();

        Map<String, List<ShoppingCart>> collect = list.stream().collect(Collectors.groupingBy(ShoppingCart::getShopId));
        BigDecimal totalAmount = new BigDecimal(0);
        BigDecimal notRateTotalAmount = new BigDecimal(0);
        BigDecimal taxRate =BigDecimal.ZERO;
        // TODO  第二自营点
        for (Map.Entry<String, List<ShoppingCart>> entry : collect.entrySet()) {
            // 商品id
            String shopId = entry.getKey();

            // 购物车数据
            List<ShoppingCart> dataList = entry.getValue();
            for (ShoppingCart shoppingCart : dataList) {
                HashMap<String, Object> d = new HashMap<>();
                Product product = productService.getProductById(shoppingCart.getProductId(), ProductEnum.STATE_PUTAWAY.getCode());
                if (taxRate .compareTo(BigDecimal.ZERO) == 0) {
                    taxRate = product.getTaxRate();
                }
                if (taxRate.compareTo(product.getTaxRate()) != 0) {
                    throw new BusinessException("商品的税率不一致！，请选择相同税率商品推送");
                }
                ProductCategory productCategory = productCategoryService.getById(product.getClassId());
                ProductSku productSku = productSkuService.getProductSkuByProductId(product.getProductId(), null).get(0);


                Shop one = shopService.lambdaQuery().eq(Shop::getShopId, shopId)
                        .select(Shop::getEnterpriseId).one();
                if (one != null) {
                    EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, one.getEnterpriseId())
                            .select(EnterpriseInfo::getSocialCreditCode, EnterpriseInfo::getShortCode,
                                    EnterpriseInfo::getInteriorId, EnterpriseInfo::getEnterpriseName, EnterpriseInfo::getTaxRate).one();

                    if (enterpriseInfo != null) {
                        //如果商品的税率不一致，则报错

                        // 如果信息代码为空则代表是内部供应商
                        d.put("CreditCode", enterpriseInfo.getSocialCreditCode());
                        d.put("StorageOrgId", enterpriseInfo.getInteriorId());
                        d.put("OrgShort", enterpriseInfo.getShortCode());
                    } else {
                        throw new BusinessException("供应商不存在！");
                    }
                } else {
                    throw new BusinessException(500, "商品【" + product.getProductName() + "】对应的店铺不存在！");
                }
                cartIds.add(shoppingCart.getCartId());
                //含税单价
                BigDecimal sellPrice = productSku.getSellPrice();
                //不含税单价
                BigDecimal price = TaxCalculator.calculateNotTarRateAmount(sellPrice, taxRate).setScale(2, RoundingMode.HALF_UP);
                if (productCategory == null) {
                    throw new BusinessException(500, "商品【" + product.getProductName() + "】的类别不存在！");
                }
//            d.put("MatterUse", "");
                d.put("ClassId", product.getClassId());

                List<CategoryClassIdAndClassNameVO> categoryParentPath = productCategoryService.getCategoryParentPath(productCategory.getClassId());
                String classPath = "";
                for (CategoryClassIdAndClassNameVO categoryClassIdAndClassNameVO : categoryParentPath) {
                    classPath += categoryClassIdAndClassNameVO.getClassName() + "/";
                }
                d.put("ClassName", classPath.substring(0, classPath.length() - 1));
                d.put("Number", shoppingCart.getCartNum());
                //不含税价格

                d.put("Price", price);
                d.put("taxPrice", sellPrice);
                BigDecimal multiplySell = sellPrice.multiply(shoppingCart.getCartNum()).setScale(2, RoundingMode.HALF_UP);

                BigDecimal notRateAmount=TaxCalculator.noTarRateItemAmount(multiplySell,price,shoppingCart.getCartNum(), taxRate);
                totalAmount = totalAmount.add(multiplySell);
                notRateTotalAmount = notRateTotalAmount.add(notRateAmount);
                d.put("TaxAmount", multiplySell);
                d.put("Amount", notRateAmount);
                // 金额
                d.put("PurchaseType", -1);
                d.put("MatterId", product.getRelevanceId());
                d.put("MatterName", product.getRelevanceName());
                d.put("Spec", productSku.getSkuName());
                d.put("MatterUnit", productSku.getUnit());
                d.put("TradeName", product.getProductName());
                d.put("TradeId", product.getProductId());
                details.add(d);
            }
        }
        //计划金额(税价合计)
        // 计划金额(不含税)
        //应pcwp要求 Amount传不含税，NoTaxPlanAmount也要改成含税TaxPlanAmount
        entity.put("PlanAmount", notRateTotalAmount);
        entity.put("TaxPlanAmount", totalAmount);
        entity.put("NotTaxPlanAmount", notRateTotalAmount);
        entity.put("TaxRate", taxRate);
        entity.put("details", details);
        params.put("data", entity);
        params.put("keyId", idStr);
        String content = JSON.toJSONString(params);
        log.error("推送零星采购计划商品新增计划：" + content);
        farArg.append(content);
        String url = mallConfig.prodPcwp2Url02 + PCWP2ApiUtil.SAVE_PLAN;
        LogUtil.writeInfoLog(idStr, "pushCardProductsArrive", cardId, params, null, ShoppingCartServiceImpl.class);
        com.scrbg.common.utils.R<Map> r = null;
        try {
            r = restTemplateUtils.postPCWP2(url, params);
        } catch (Exception e) {
            LogUtil.writeErrorLog(idStr, "pushCardProductsArrive", cardId, params, null, e.getMessage(), ShoppingCartServiceImpl.class);
            //throw new BusinessException("【远程异常】：" + e.getMessage());
            // 屏蔽抛出的异常，否则代码不往下执行
            // 处理2.0超时问题
            String planNo = null;
            if (e.getMessage().contains("Read timed out")) {
                //获取超时计划的计划编号
                planNo = pcwp1Servise.pcwp1VerifylXPlan(mallConfig.prodPcwp2Url02, idStr);
                if (StringUtils.isEmpty(planNo)) {
                    LogUtil.writeErrorLog(idStr, "pushCardProductsArrive", cardId, params, e, e.getMessage(), ShoppingCartServiceImpl.class);
                    log.error("推送计划报错：" + e.getMessage());
                    throw new BusinessException("【远程异常】：" + e.getMessage());
                }
            } else {
                LogUtil.writeErrorLog(idStr, "pushCardProductsArrive", cardId, params, e, e.getMessage(), ShoppingCartServiceImpl.class);
                log.error("推送计划报错：" + e.getMessage());
                throw new BusinessException("【远程异常】：" + e.getMessage());
            }
        }
        String planNo = null;
        if (r.getCode() == null || r.getCode() != 200) {
            if (r.getMessage().contains("Read timed out")) {
                //获取超时计划的计划编号
                planNo = pcwp1Servise.pcwp1VerifylXPlan(mallConfig.prodPcwp2Url02, idStr);
                if (StringUtils.isEmpty(planNo)) {
                    LogUtil.writeErrorLog(idStr, "pushCardProductsArrive", cardId, params, r, r.getMessage(), ShoppingCartServiceImpl.class);
                    log.error("推送计划报错：" + r.getMessage());
                    throw new BusinessException("【远程异常】：" + r.getMessage());
                }
            } else {
                LogUtil.writeErrorLog(idStr, "pushCardProductsArrive", cardId, params, r, r.getMessage(), ShoppingCartServiceImpl.class);
                log.error("推送计划报错：" + r.getMessage());
                throw new BusinessException("【远程异常】：" + r.getMessage());
            }
        }
        log.warn("推送计划返回：" + r);
        InterfaceLogs iLog = new InterfaceLogs();
        iLog.setSecretKey(idStr);
        iLog.setClassPackage(ShoppingCartServiceImpl.class.getName());
        iLog.setMethodName("pushCardProductsArrive");
        iLog.setLocalArguments(JSON.toJSONString(cardId));
        iLog.setFarArguments(content);
        interfaceLogsService.createSuccessLog(iLog);
    }


    @Autowired
    OrdersService ordersService;

    /**
     * 检查推送计划情况  pcwp2
     * 生成订单
     *
     * @param map
     * @return
     */
    @Override
    public Map checkSubmitPlanProductCondition(Map map) {
        HashMap<Object, Object> resMap = new HashMap<>();
        BigDecimal taxRate=BigDecimal.ZERO;
         //根据计划的含税和不含税反推计划
        BigDecimal TaxPlanAmount =TaxCalculator.convertToBigDecimal(map.get("TaxPlanAmount")) ;
        BigDecimal PlanAmount =TaxCalculator.convertToBigDecimal( map.get("PlanAmount"));
        taxRate = TaxCalculator.getTaxRateByTaxAndNoTax(TaxPlanAmount, PlanAmount);

        // 获取明细列表
        List<Map> details = (ArrayList<Map>) map.get("details");
        for (Map detail : details) {
            // 未消耗数量
            BigDecimal Number = new BigDecimal(detail.get("Number").toString());
            if (mallConfig.isCountPlanOrderNum == 1) {
                String dtlId = (String) detail.get("DtlId");
                if (org.apache.commons.lang.StringUtils.isBlank(dtlId)) {
                    throw new BusinessException("计划详情id为空！");
                }
                BigDecimal qty = ordersService.getOrderUseCountBySelectPlanDtlIds(dtlId);
                if (qty!=null&&qty.compareTo(BigDecimal.ZERO)==1){
                    qty = new BigDecimal(detail.get("consumeNumber").toString());
                }
                detail.put("consumeNumber", qty);
                detail.put("NotConsumeNumber", Number.subtract(qty));
                BigDecimal price = new BigDecimal(detail.get("Price").toString());
                BigDecimal consumeAmount = price.multiply(qty).setScale(2, BigDecimal.ROUND_HALF_UP);
                detail.put("consumeAmount", consumeAmount);
            }
            BigDecimal notConsumeNumber = new BigDecimal(detail.get("NotConsumeNumber").toString());
            BigDecimal ConsumeNumber = Number.subtract(notConsumeNumber);

//            BigDecimal ConsumeNumber = new BigDecimal(detail.get("ConsumeNumber").toString());
            if (Number.compareTo(ConsumeNumber) == 0) {
                // 相等
                detail.put("cause", "计划已完成！");
                detail.put("resCode", 50010);
                continue;
            }
            // 未消耗数量
            detail.put("residueNum", notConsumeNumber);
            detail.put("cause", null);
            detail.put("resCode", 200);
            //  这里应该拿到店铺id，如果是外部企业用信用代码，如果是内部用orgId
            String socialCreditCode = (String) detail.get("CreditCode");
            EnterpriseInfo enterpriseInfo = null;
            if (StringUtils.isEmpty(socialCreditCode)) {
                // 内部id
                String insideOrgId = (String) detail.get("StorageOrgId");
                enterpriseInfo = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getInteriorId, insideOrgId)
                        .select(EnterpriseInfo::getEnterpriseId, EnterpriseInfo::getShortCode).one();
                if (enterpriseInfo == null) {
                    detail.put("cause", "供应商未注册！");
                    detail.put("resCode", 50010);
                    continue;
                }
                // 装入机构简码，注意如果出问题重新登陆改机构就有机构简码
                detail.put("ShortCode", enterpriseInfo.getShortCode());
            } else {
                enterpriseInfo = enterpriseInfoService.lambdaQuery()
                        .eq(EnterpriseInfo::getSocialCreditCode, socialCreditCode)
                        .select(EnterpriseInfo::getEnterpriseId)
                        .one();
                if (enterpriseInfo == null) {
                    detail.put("cause", "供应商未注册！");
                    detail.put("resCode", 50010);
                    continue;
                }
            }
            Shop shop = shopService.lambdaQuery().eq(Shop::getEnterpriseId, enterpriseInfo.getEnterpriseId())
                    .select(Shop::getShopId).one();
            if (shop == null) {
                detail.put("cause", "店铺不存在！");
                detail.put("resCode", 50010);
                continue;
            } else {
                detail.put("shopId", shop.getShopId());
            }
            String productId = (String) detail.get("TradeId");
            // 查看购物车的商品是否还上架未删除
            Product product = productService.getProductById(productId, ProductEnum.STATE_PUTAWAY.getCode());
            if (product == null) {
                Product byId = productService.getProductExcludeRemarkById(productId);
                if (byId != null) {
                    detail.put("cause", "商品已下架！");
                    detail.put("resCode", 50010);
                } else {
                    detail.put("cause", "商品不存在！");
                    detail.put("resCode", 50010);
                }
            } else {
                ProductSku productSku = productSkuService.getProductSkuByProductId(product.getProductId(), null).get(0);
                BigDecimal residueNum = (BigDecimal) detail.get("residueNum");
                // 判断库存 计划数量大于了现有库存
                int i = residueNum.compareTo(productSku.getStock());
                if (i == 1) {
                    detail.put("cause", "【警告】购买数量大于现有库存！");
                    detail.put("resCode", 50000);
                }

            }
        }

        map.put("details", details);
        log.warn("检查计划返回：" + map);
        resMap.put("planDetail", map);
        List<SubmitOrderByPlanVO> buyVOS = new ArrayList<>();
        BigDecimal totalPrice = new BigDecimal(0);

        ArrayList<Map> newDetail = new ArrayList<>();

        Iterator<Map> iterator = details.iterator();
        while (iterator.hasNext()) {
            Map<String, Object> mapi = iterator.next();
            Integer resCode = (Integer) mapi.get("resCode");
            if (resCode != 50010) {
                // 不能直接调用删除方法删除
                newDetail.add(mapi);
            }
        }
        details = newDetail;
        if (CollectionUtils.isEmpty(details)) {
            return resMap;
        }
        log.warn("最终的计划：" + details);
        // 按店铺进行分组
        Map<Object, List<Map>> groupedDetail = details.stream()
                .collect(Collectors.groupingBy(map2 -> map2.get("shopId")));
        for (Object shopId : groupedDetail.keySet()) {
            // 店铺id
            String shopIdStr = (String) shopId;
            // 列表
            List<Map> gDetails = groupedDetail.get(shopId);
            boolean checkFlag = true;
            SubmitOrderByPlanVO buyVO = new SubmitOrderByPlanVO();
            buyVO.setShopId(shopIdStr);
            String shopName = shopService.getShopNameById(shopIdStr);
            buyVO.setShopName(shopName);
            buyVO.setChecked(true);
            List<SubmitOrderProductItemInfoByPlanVO> productInfos = new ArrayList<>();
            for (Map gDetail : gDetails) {
                Integer resCode = (Integer) gDetail.get("resCode");
                SubmitOrderProductItemInfoByPlanVO productInfo = new SubmitOrderProductItemInfoByPlanVO();

                // 这个不需要返回，因为需要计算
//                productInfo.setAccount(new BigDecimal(gDetail.get("Amount").toString()));

                productInfo.setMatterId(gDetail.get("MatterId").toString());
                productInfo.setMatterName(gDetail.get("MatterName").toString());
                productInfo.setShopName(shopName);
                productInfo.setResCode(resCode);
                productInfo.setCause((String) gDetail.get("cause"));

                productInfo.setBillNo((String) map.get("BillNo"));
                productInfo.setBillId((String) gDetail.get("BillId"));
                productInfo.setDtlId((String) gDetail.get("DtlId"));

                productInfo.setStorageId((String) gDetail.get("StorageId"));
                productInfo.setStorageName((String) gDetail.get("StorageName"));
                productInfo.setStorageOrgId((String) gDetail.get("StorageOrgId"));
                productInfo.setCreditCode((String) gDetail.get("CreditCode"));
                productInfo.setShortCode((String) gDetail.get("ShortCode"));

                productInfo.setChecked(true);
                String productId = (String) gDetail.get("TradeId");
                Product product = productService.getProductById(productId, ProductEnum.STATE_PUTAWAY.getCode());
                ProductSku productSku = productSkuService.getProductSkuByProductId(product.getProductId(), null).get(0);
                BeanUtils.copyProperties(product, productInfo);
                BeanUtils.copyProperties(productSku, productInfo);
                //默认价格为推送计划的价格
                productInfo.setPrice(new BigDecimal(gDetail.get("Price").toString()));
                BigDecimal residueNum = (BigDecimal) gDetail.get("residueNum");
                BigDecimal sellPrise = new BigDecimal(gDetail.get("Price").toString());
                BigDecimal taxPrice = new BigDecimal(gDetail.get("taxPrice").toString());
                BigDecimal number = new BigDecimal(gDetail.get("number").toString());

                BigDecimal rateAmount = number.multiply(taxPrice);
                BigDecimal Amount = new BigDecimal(gDetail.get("Amount").toString());
                //该税率来源于计划
                productInfo.setTaxRate(taxRate);
                taxRate = TaxCalculator.getTaxRateByTaxAndNoTax(rateAmount, Amount);
                productInfo.setTaxRate(taxRate);

                productInfo.setSellPrice(sellPrise);
                productInfo.setCartNum(residueNum);
                productInfo.setCartMaxNum(residueNum);



                // 默认购买数量都是剩余下单的数量,
                BigDecimal numTotalPrice = residueNum.multiply(productSku.getSellPrice()).setScale(2, RoundingMode.HALF_UP);
                productInfo.setNumTotalPrice(numTotalPrice);
                totalPrice = totalPrice.add(numTotalPrice);
                // 只是警告不让选中
                if (resCode == 50000) {
                    // 等于1是库存太少,使用现有的库存
                    BigDecimal stock = productSku.getStock();
                    productInfo.setCartNum(stock);
                    productInfo.setCartMaxNum(stock);
                    BigDecimal multiply = stock.multiply(productSku.getSellPrice().setScale(2, RoundingMode.HALF_UP));
                    productInfo.setNumTotalPrice(multiply);
                    productInfo.setChecked(false);
                    checkFlag = false;
                }
                productInfos.add(productInfo);
            }
            buyVO.setProductInfo(productInfos);
            // 商品都没有错误商品才是选中
            if (checkFlag) {
                buyVO.setChecked(true);
            }
            // 店铺存在才添加店铺商品
            buyVOS.add(buyVO);
        }

        // 放置总金额
        if (!CollectionUtils.isEmpty(buyVOS)) {
            buyVOS.get(0).setTotalPrice(totalPrice);
        }

        resMap.put("buyProductList", buyVOS);
        log.warn("检查商品返回：" + buyVOS);
        return resMap;
    }


    @Override
    public void pushCardProductsArriveRollBack(String idStr) {
        com.scrbg.common.utils.R r = null;
        try {
            log.warn("回滚计划：" + mallConfig.prodPcwp2Url02 + "/thirdapi/sporadicPurchasePlan/rollBackSavePlan?keyId=" + idStr);
            if (r == null || r.getCode() != 200) {
                InterfaceLogs iLog = new InterfaceLogs();
                iLog.setSecretKey(idStr);
                iLog.setClassPackage(UserCenterUserInfoController.class.getName());
                iLog.setMethodName("pushCardProductsArriveRollBack");
                iLog.setLocalArguments(JSON.toJSONString(idStr));
                iLog.setFarArguments(idStr);
                iLog.setErrorInfo("远程异常，推送计划回滚失败");
                interfaceLogsService.rollBackFailLog(iLog);
            }
            r = restTemplateUtils.getPCWP2NotParams(mallConfig.prodPcwp2Url02 + "/thirdapi/sporadicPurchasePlan/rollBackSavePlan?keyId=" + idStr);
            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(idStr);
            iLog.setClassPackage(UserCenterUserInfoController.class.getName());
            iLog.setMethodName("pushCardProductsArriveRollBack");
            iLog.setLocalArguments(JSON.toJSONString(idStr));
            iLog.setFarArguments(idStr);
            iLog.setResult("pcwp计划回滚成功");
            interfaceLogsService.rollBackSuccessLog(iLog);
        } catch (Exception e) {
            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(idStr);
            iLog.setClassPackage(UserCenterUserInfoController.class.getName());
            iLog.setMethodName("pushCardProductsArriveRollBack");
            iLog.setLocalArguments(JSON.toJSONString(idStr));
            iLog.setFarArguments(idStr);
            iLog.setErrorInfo(e.getMessage());
            interfaceLogsService.rollBackFailLog(iLog);
        }
    }

    /**
     * 修改购物车选中状态
     *
     * @param id
     * @param checked
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateChecked(String id, Integer checked) {
        lambdaUpdate().eq(ShoppingCart::getCartId, id)
                .set(ShoppingCart::getChecked, checked).update();
        if (checked == 1) {

            Integer count =0;
            String userId = ThreadLocalUtil.getCurrentUser().getUserId();
            ShoppingCart byId = getById(id);
            //大宗物资暂时不用比较税率
            if (byId.getProductType()==0){
                 count = lambdaQuery().eq(ShoppingCart::getUserId, userId)
                        .eq(ShoppingCart::getChecked, 1)
                        .and(wrapper -> wrapper
                                .ne(ShoppingCart::getProductType, byId.getProductType())
                                .or()
                                .ne(ShoppingCart::getTaxRate, byId.getTaxRate())
                        )
                        .count();
                if (count > 0) {
                    throw new BusinessException("购物车只能勾选一种类型的商品或者只能选择同一种税率！");
                }
            }else {
                count = lambdaQuery().eq(ShoppingCart::getUserId, userId)
                        .eq(ShoppingCart::getChecked, 1)
                        .and(wrapper -> wrapper
                                .ne(ShoppingCart::getProductType, byId.getProductType())
                        )
                        .count();
                if (count > 0) {
                    throw new BusinessException("购物车只能勾选一种类型的商品");
                }
            }

        }
    }


    /**
     * 检查购物车商品是否可创建大宗临购
     * <p>
     * 1、校验商品当前金额和购物车金额是否一致
     * 2、校验大宗临购清单总金额是否超过系统配置的金额
     *
     * @param ids
     * @return
     */
    @Override
    public IsSynthesizeTemporaryVO isSynthesizeTemporary(List<String> ids) {
        List<ShoppingCart> list = lambdaQuery().in(ShoppingCart::getCartId, ids).list();
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException("购物车信息不存在！");
        }
        // 明细
        List<IsSynthesizeTemporaryItemVO> vos = new ArrayList<>();
        // 实际总金额
        BigDecimal actualAmount = new BigDecimal(0);
        String shopId = list.get(0).getShopId();
        for (ShoppingCart cart : list) {
            String productId = cart.getProductId();
            // 查看购物车的商品是否还上架未删除
            Product product = productService.getProductById(productId, ProductEnum.STATE_PUTAWAY.getCode());
            if (product == null) {
                Product byId = productService.getProductExcludeRemarkById(productId);
                if (byId != null) {
                    throw new BusinessException("商品名为：【" + byId.getProductName() + "】的商品已下架或已被删除！");
                } else {
                    throw new BusinessException(OrderEnum.RESULT_CODE_500201.getCode(), OrderEnum.RESULT_CODE_500201.getRemark());
                }
            } else {
                Integer productType = product.getProductType();
                if (productType != 1) {
                    throw new BusinessException("商品名为：【" + product.getProductName() + "】不属于大宗临购不能生成");
                }
                ProductSku productSku = productSkuService.getProductSkuById(cart.getSkuId(), PublicEnum.STATE_OPEN.getCode());
                String zonePath = "";
                if (cart.getProductPrice().compareTo(productSku.getSellPrice()) != 0) {
                    throw new BusinessException("商品名为：【" + product.getProductName() + "】的商品价格发生变化，请处理后再试！");
                }
                if (!shopId.equals(cart.getShopId())) {
                    throw new BusinessException("只能选择一家店铺的商品！");
                }
                IsSynthesizeTemporaryItemVO it = new IsSynthesizeTemporaryItemVO();

                String secondUnit = productSku.getSecondUnit();
                if (StringUtils.isEmpty(secondUnit)) {
                    it.setIsTwoUnit(0);
                    it.setQty(cart.getCartNum());
                } else {
                    it.setIsTwoUnit(1);
                    it.setTwoUnit(secondUnit);
                    it.setSecondUnitNum(productSku.getSecondUnitNum());
                    it.setTwoUnitNum(cart.getCartNum().multiply(productSku.getSecondUnitNum()).setScale(4, BigDecimal.ROUND_HALF_UP));
                    // 计算出真实数量
                    it.setQty(cart.getCartNum());
                }
                actualAmount = actualAmount.add(productSku.getSellPrice().multiply(it.getQty()).setScale(2, RoundingMode.HALF_UP));

                // 封装数据
                it.setProductName(product.getProductName());
                it.setProductSn(product.getSerialNum());
                it.setProductId(product.getProductId());
                it.setZoneId(cart.getZoneId());
                it.setZoneAddr(cart.getZoneAddr());
                it.setZonePath(zonePath);
                it.setMaterialId(product.getRelevanceId());
                it.setMaterialSn(product.getRelevanceNo());
                it.setMaterialName(product.getRelevanceName());
                it.setSupplierOrgId(product.getSupperBy());
                it.setSupplierName(product.getSupplierName());
                it.setClassId(product.getClassId());
                if (StringUtils.isEmpty(product.getClassId())) {
                    throw new BusinessException("商品名为：【" + product.getProductName() + "】分类不存在！");
                }
                ProductCategory productCategory = productCategoryService.getById(product.getClassId());
                it.setClassName(productCategory.getClassName());
                List<CategoryClassIdAndClassNameVO> categoryParentPath = productCategoryService.getCategoryParentPath(product.getClassId());
                String classPath = "";
                String classPathId = "";
                if (CollectionUtils.isEmpty(categoryParentPath)) {
                    throw new BusinessException("分类不存在！");
                } else {
                    if (categoryParentPath.size() == 1) {
                        it.setClassNamePath(categoryParentPath.get(0).getClassName());
                        it.setClassIdPath(categoryParentPath.get(0).getClassId());
                    } else {
                        for (CategoryClassIdAndClassNameVO categoryClassIdAndClassNameVO : categoryParentPath) {
                            if (!org.springframework.util.StringUtils.isEmpty(categoryClassIdAndClassNameVO.getClassName())) {
                                classPath += categoryClassIdAndClassNameVO.getClassName() + "/";
                                classPathId += categoryClassIdAndClassNameVO.getClassId() + "/";
                            }
                        }
                        it.setClassNamePath(classPath.substring(0, classPath.length() - 1));
                        it.setClassIdPath(classPathId.substring(0, classPathId.length() - 1));
                    }
                }
                it.setSpec(productSku.getSkuName());
                // 不推送材质
                it.setTexture(product.getProductTexture());
                it.setBrandId(product.getBrandId());
                it.setBrandName(product.getBrandName());
                it.setUnit(productSku.getUnit());
                it.setCostPrice(productSku.getCostPrice());
                it.setReferencePrice(productSku.getSellPrice());
                it.setSynthesizePrice(productSku.getSellPrice());
                it.setReferenceAmount(it.getQty().multiply(productSku.getSellPrice()).setScale(2, RoundingMode.HALF_UP));
                it.setSynthesizeAmount(it.getQty().multiply(productSku.getSellPrice()).setScale(2, RoundingMode.HALF_UP));
                vos.add(it);
            }
        }
        SystemParam systemParam = systemParamService.lambdaQuery().eq(SystemParam::getCode, PublicEnum.SYNTHESIZE_TEMPORARY_AMOUNT_MAX.getRemark()).one();
        String keyValue = systemParam.getKeyValue();
        // 比较总金额不能超过
        BigDecimal maxAmount = new BigDecimal(keyValue);
        // 单位是万，需要乘以
        if (maxAmount.multiply(new BigDecimal("10000")).compareTo(actualAmount) == -1) {
            throw new BusinessException("选择商品总金额超过平台限制金额！限制最大金额为：" + maxAmount.toPlainString() + "万");
        }


        Shop shop = shopService.lambdaQuery().eq(Shop::getShopId, shopId).select(Shop::getShopId, Shop::getEnterpriseId).one();
        EnterpriseInfo en = enterpriseInfoService.getById(shop.getEnterpriseId());
        IsSynthesizeTemporaryVO vo = new IsSynthesizeTemporaryVO();
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        vo.setOrgFarId(user.getOrgId());
        vo.setOrgId(user.getEnterpriseId());
        vo.setOrgName(user.getEnterpriseName());
        vo.setSupplierOrgFarId(en.getInteriorId());
        vo.setSupplierOrgId(en.getEnterpriseId());
        vo.setSupplierOrgName(en.getEnterpriseName());
        vo.setSupplierCreditCode(en.getSocialCreditCode());
        vo.setSupplierShortCode(en.getShortCode());
        vo.setReferenceSumAmount(actualAmount);
        vo.setSynthesizeSumAmount(actualAmount);
        vo.setState(0);
        vo.setDtls(vos);
        return vo;
    }


    @Override
    public void addCartZone(ProductCardZone productCardZone) {
        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        ShoppingCart cart = new ShoppingCart();
        String productId = productCardZone.getProductId();
        BigDecimal cartNum = productCardZone.getCartNum();
        Product product = productService.getById(productId);
        ProductSku sku = productSkuService.lambdaQuery().eq(ProductSku::getProductId, productId).one();
        if (product == null || sku == null) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "操作失败！商品不存在或已下架！");
        }
        BigDecimal stock = sku.getStock();
        if (cartNum.compareTo(stock) == 1) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "库存不足！剩余库存【" + stock + "】");
        }
        //设置销售价格
        cart.setProductPrice(sku.getSellPrice());

        // 如果有则相加
        ShoppingCart one = lambdaQuery().eq(ShoppingCart::getProductId, productId)
                .eq(ShoppingCart::getSkuId, sku.getSkuId())
                .eq(ShoppingCart::getUserId, userId)
                .eq(ShoppingCart::getZoneId, productCardZone.getZoneId())
                .eq(ShoppingCart::getZoneAddr, productCardZone.getDetailAddress())
                .one();
        if (one != null) {
            BigDecimal cartNum1 = one.getCartNum();
            BigDecimal newCartNum = cartNum1.add(cartNum);
            lambdaUpdate().eq(ShoppingCart::getCartId, one.getCartId())
                    .set(ShoppingCart::getCartNum, newCartNum)
                    .set(ShoppingCart::getProductPrice, cart.getProductPrice())
                    .set(ShoppingCart::getZoneAddr, productCardZone.getDetailAddress())
                    .set(ShoppingCart::getGmtModified, new Date()).update();
        } else {
            cart.setLeaseUnit(sku.getLeaseUnit());
            cart.setProductId(productId);
            cart.setShopId(product.getShopId());
            cart.setSkuId(sku.getSkuId());
            cart.setUserId(userId);
            cart.setCartNum(cartNum);
            cart.setCartTime(new Date());
            cart.setSkuProps(sku.getSkuName());
            cart.setProductType(product.getProductType());
            cart.setCartImg(sku.getSkuImg());
            save(cart);
        }
    }
}
