package scrbg.meplat.mall.vo.product.website;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-12-07 14:14
 */
@Data
public class WBrandVO {

    @ApiModelProperty(value = "品牌id")

    private String brandId;

    @ApiModelProperty(value = "品牌名")

    private String name;

    @ApiModelProperty(value = "品牌logo地址")

    private String logo;

    @ApiModelProperty(value = "介绍")

    private String descript;

}
