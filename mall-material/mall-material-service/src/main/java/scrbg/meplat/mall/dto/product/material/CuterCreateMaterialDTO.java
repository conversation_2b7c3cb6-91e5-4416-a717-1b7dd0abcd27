package scrbg.meplat.mall.dto.product.material;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-11-29 12:55
 */
@Data
public class CuterCreateMaterialDTO {

    /**
     * product
     */

    @ApiModelProperty(value = "店铺id",required = true)
    @NotEmpty(message = "店铺id不能为空！")
    private String shopId;
    @ApiModelProperty(value = "商品名称",required = true)
    @NotEmpty(message = "商品名称不能为空！")
    private String productName;

    @ApiModelProperty(value = "商品编码（外部商品编码）")
    private String outerProductCode;

    @ApiModelProperty(value = "分类路径（xxxx/xxx/xx）", required = true)
    @NotEmpty(message = "分类路径不能为空！")
    private String classNamePath;
    @ApiModelProperty(value = "物资编号", required = true)
    @NotEmpty(message = "物资编号！")
    private String materialNo;

    @ApiModelProperty(value = "商品的最低价",required = false)
    private BigDecimal productMinPrice;

//    @ApiModelProperty(value = "品牌id",required = true)
//    @NotEmpty(message = "品牌id不能为空！")
//    private String brandId;

    @ApiModelProperty(value = "品牌名称",required = false)
//    @NotEmpty(message = "品牌名称不能为空！")
    private String brandName;
    @ApiModelProperty(value = "店铺排序")
    private Integer shopSort;

    @ApiModelProperty(value = "商品描述（html）")
    private String productDescribe;

    /**
     * file
     */

    @ApiModelProperty(value = "商品主图（传入一个即可）", required = true)
    @NotEmpty(message = "商品主图不能为空！")
    @Valid
    private List<ImportProductVO> adminFile;

    @ApiModelProperty(value = "商品小图（传入一个即可）",required = true)
    @NotEmpty(message = "商品小图不能为空！")
    @Valid
    private List<ImportProductVO> minFile;

    @ApiModelProperty(value = "商品图片",required = true)
    @NotEmpty(message = "商品图片不能为空！")
    @Valid
    private List<ImportProductVO> productFiles;

    /**
     * sku
     */

    @ApiModelProperty(value = "规格型号", required = true)
    @NotEmpty(message = "规格型号不能为空！")
    private String skuName;

    @ApiModelProperty(value = "成本价",required = true)
    @NotNull(message = "成本价不能为空！")
    private BigDecimal costPrice;

    @ApiModelProperty(value = "原价",required = true)
    @NotNull(message = "原价不能为空！")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "销售价格",required = true)
    @NotNull(message = "销售价格不能为空！")
    private BigDecimal sellPrice;

    @ApiModelProperty(value = "库存",required = true)
    @NotNull(message = "库存不能为空！")
    private BigDecimal stock;

    @ApiModelProperty(value = "计量单位",required = true)
    @NotEmpty(message = "计量单位不能为空！")
    private String unit;



    @ApiModelProperty(value = "是否上架审核（0否1是，默认0）",required = false)
    private Integer isPutaway;


    @ApiModelProperty(value = "物资基础库名称",required = false)
    private String basicsMaterialName;

    @ApiModelProperty(value = "物资基础编号",required = true)
    @NotEmpty(message = "物资基础库编号不能为空！")
    private String basicsMaterialNo;


}
