package scrbg.meplat.mall.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

/**
 * @描述：物资验收明细
 * @作者: ye
 * @日期: 2023-07-26
 */
@ApiModel(value = "物资验收明细")
@Data
@TableName("material_reconciliation_dtl")
public class MaterialReconciliationDtl extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "对账明细ID")
    private String reconciliationDtlId;

    @ApiModelProperty(value = "对账编号")
    @TableField(exist = false)
    private String reconciliationNo;

    @ApiModelProperty(value = "对账ID")

    private String reconciliationId;
    @ApiModelProperty(value = "订单项")

    private String orderItemId;


    @ApiModelProperty(value = "源单id（pcwp验收明细id）")

    private String sourceDtlId;


    @ApiModelProperty(value = "物资类别id(1级类别id/2级类别id/..)")

    private String materialClassId;


    @ApiModelProperty(value = "物资类别名称(1级类别名称/2级类别名称/..)")

    private String materialClassName;


    @ApiModelProperty(value = "物资id")

    private String materialId;


    @ApiModelProperty(value = "商城物资id")

    private String materialMallId;


    @ApiModelProperty(value = "物资名称")

    private String materialName;
    @ApiModelProperty(value = "商品名称")

    private String productName;



    @ApiModelProperty(value = "规格型号")

    private String spec;


    @ApiModelProperty(value = "计量单位")

    private String unit;


    @ApiModelProperty(value = "材质")

    private String texture;


    @ApiModelProperty(value = "对账单价（含税）")

    private BigDecimal price;


    @ApiModelProperty(value = "对账单价（不含税）")

    private BigDecimal noRatePrice;

    @ApiModelProperty(value = "对账数量")

    private BigDecimal quantity;


    @ApiModelProperty(value = "对账总金额")

    private BigDecimal acceptanceAmount;

    @ApiModelProperty(value = "税额（含税）")

    private BigDecimal taxAmount;


    @ApiModelProperty(value = "对账总金额（不含税）")

    private BigDecimal acceptanceNoRateAmount;


    @ApiModelProperty(value = "已结算金额")

    private BigDecimal settledAmount;


    @ApiModelProperty(value = "到货网价（商城使用，如果是大宗临购，大宗临购浮动价格使用）（如果是pcwp新增）")

    private BigDecimal freightPrice;


    @ApiModelProperty(value = "固定费用（商城使用）（如果是大宗临购，大宗临购浮动价格使用）")

    private BigDecimal fixationPrice;


    @ApiModelProperty(value = "状态")

    private Integer state;


    @ApiModelProperty(value = "订单id")

    private String orderId;


    @ApiModelProperty(value = "订单编号")

    private String orderSn;


    @ApiModelProperty(value = "源单据总金额（废弃）")

    private BigDecimal sourceAmount;


    @ApiModelProperty(value = "源单据总数量（保存第一次新增的数量）（废弃）")

    private BigDecimal sourceQuantity;


    @ApiModelProperty(value = "修改类型（计算金额使用，null:未修改单价，1:修改了单价）（商城拆单使用，新增请忽略）")
    @TableField(exist = false)
    private Integer updateType;


    @ApiModelProperty(value = "pcwp收料单id（商城新增）")

    private String receiptBillId;

    @ApiModelProperty(value = "pcwp收料单编号（商城新增）")

    private String receiptBillSn;

    @ApiModelProperty(value = "pcwp收料明细id（商城新增）")

    private String receiptBillDtlId;

    @ApiModelProperty(value = "pcwp收料单单据日期（商城新增）")

    private Date receivingDate;

    @ApiModelProperty(value = "pcwp收料单单据日期（商城新增）（excel导出使用）")
    @TableField(exist = false)
    private String receivingDateStr;

    @ApiModelProperty(value = "仓库id（pcwp1需要使用）")

    private String warehouseId;

    @ApiModelProperty(value = "仓库名称（pcwp1需要使用）")

    private String warehouseName;


    @ApiModelProperty(value = "出厂价（大宗临购固定价格使用）")

    private BigDecimal outFactoryPrice;

    @ApiModelProperty(value = "运杂费（大宗临购固定价格使用）")

    private BigDecimal transportPrice;

    @ApiModelProperty(value = "发票总净额  计算发票")
    @TableField(exist = false)
    private BigDecimal reconciliationTotalAmount;
    @ApiModelProperty(value = "税率")
    @TableField(exist = false)
    private BigDecimal taxRate;
    @ApiModelProperty(value = "订单明细id（大宗临购使用）")

    private String orderDtlId;
    @ApiModelProperty(value = "商品id（）")

    private String tradeId;

    //乐观锁
    @Version
    private Integer version;


    public String getReceivingDateStr() {
        if (this.receivingDate != null) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String dateString = dateFormat.format(this.receivingDate);
            return dateString;
        } else {
            return "";
        }
    }
}
