package scrbg.meplat.mall.vo.platform.enterprise;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class EnterpriseArrearageVo {

    @ApiModelProperty(value = "可欠费额度元")
    private BigDecimal arrearage;

    @ApiModelProperty(value = "欠费过期时间时长")
    private Integer arrearageDateNum;

    @ApiModelProperty(value = "欠欠费过期时间类型（1天2月3年）")
    private Integer arrearageDateType;


    @ApiModelProperty(value = "公司ids")
    private List<String> enterpriseIds;

}
