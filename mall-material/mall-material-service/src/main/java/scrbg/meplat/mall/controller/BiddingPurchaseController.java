package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.config.auth.IsRole;
import scrbg.meplat.mall.config.auth.RoleEnum;
import scrbg.meplat.mall.config.logRecording.LogRecord;
import scrbg.meplat.mall.config.logRecording.enums.BusinessType;
import scrbg.meplat.mall.config.logRecording.enums.OperatorType;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.dto.bidding.AuditBidingInfoDTO;
import scrbg.meplat.mall.dto.bidding.BatchUpdateBiddingItemInfoDTO;
import scrbg.meplat.mall.dto.bidding.CreateBidingOrderItemsByBiddingIdDTO;
import scrbg.meplat.mall.service.BiddingPurchaseService;
import scrbg.meplat.mall.entity.BiddingPurchase;
import scrbg.meplat.mall.vo.bidding.BiddingPurchaseAndItemVO;
import scrbg.meplat.mall.vo.bidding.BiddingPurchaseInfoVO;
import scrbg.meplat.mall.vo.bidding.PlatformBiddingPurchaseInfoVO;

import javax.validation.Valid;
import java.util.List;

/**
 * @描述：竞价采购表控制类
 * @作者: ye
 * @日期: 2023-07-11
 */
@RestController
@RequestMapping("/")
@Api(tags = "竞价采购表")
public class BiddingPurchaseController {

    @Autowired
    public BiddingPurchaseService biddingPurchaseService;

    @PostMapping("biddingPurchase/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<BiddingPurchase> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = biddingPurchaseService.queryPage(jsonObject, new LambdaQueryWrapper<BiddingPurchase>());
        return PageR.success(page);
    }

    @GetMapping("biddingPurchase/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<BiddingPurchase> findById(String id) {
        BiddingPurchase biddingPurchase = biddingPurchaseService.getById(id);
        return R.success(biddingPurchase);
    }

    @PostMapping("biddingPurchase/create")
    @ApiOperation(value = "新增")
    @LogRecord(title = "竞价采购管理",businessType = BusinessType.INSERT,operatorType = OperatorType.MANAGE)
    public R save(@RequestBody BiddingPurchase biddingPurchase) {
        biddingPurchaseService.create(biddingPurchase);
        return R.success();
    }

    @PostMapping("biddingPurchase/update")
    @ApiOperation(value = "修改")
    @LogRecord(title = "竞价采购管理",businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE)

    public R update(@RequestBody BiddingPurchase biddingPurchase) {
        biddingPurchaseService.update(biddingPurchase);
        return R.success();
    }

    @GetMapping("biddingPurchase/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    @LogRecord(title = "竞价采购管理",businessType = BusinessType.DELETE,operatorType = OperatorType.MANAGE)

    public R delete(String id) {
        biddingPurchaseService.delete(id);
        return R.success();
    }


    @PostMapping("biddingPurchase/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    @LogRecord(title = "竞价采购管理",businessType = BusinessType.DELETE,operatorType = OperatorType.MANAGE)

    public R deleteBatch(@RequestBody List<String> ids) {
        biddingPurchaseService.removeByIds(ids);
        return R.success();
    }


    @PostMapping("shopManage/biddingPurchase/listMyCreateBiding")
    @ApiOperation(value = "查询我发布竞价列表-店铺")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<BiddingPurchase> listMyCreateBiding(@RequestBody JSONObject jsonObject) {
        PageUtils page = biddingPurchaseService.listMyCreateBiding(jsonObject, new LambdaQueryWrapper<BiddingPurchase>());
        return PageR.success(page);
    }


    @GetMapping("shopManage/biddingPurchase/getBiddingPurchaseInfo")
    @ApiOperation(value = "获取竞价详细信息")
    public R<BiddingPurchaseInfoVO> getBiddingPurchaseInfo(String biddingSn) {
        BiddingPurchaseInfoVO vo = biddingPurchaseService.getBiddingPurchaseInfo(biddingSn);
        return R.success(vo);
    }


    @PostMapping("shopManage/biddingPurchase/createBidingOrderItemsByBiddingId")
    @ApiOperation(value = "根据竞价id追加竞价明细")
    @NotResubmit
    @LogRecord(title = "竞价采购管理",businessType = BusinessType.INSERT,operatorType = OperatorType.MANAGE)
    public R createBidingOrderItemsByBiddingId(@Valid @RequestBody CreateBidingOrderItemsByBiddingIdDTO dto) {
        biddingPurchaseService.createBidingOrderItemsByBiddingId(dto);
        return R.success();
    }
    @PostMapping("shopManage/biddingPurchase/createInventoryBidingByBiddingId")
    @ApiOperation(value = "根据竞价id追加清单竞价明细")
    @NotResubmit
    @LogRecord(title = "竞价采购管理",businessType = BusinessType.INSERT,operatorType = OperatorType.MANAGE)

    public R createInventoryBidingByBiddingId(@Valid @RequestBody CreateBidingOrderItemsByBiddingIdDTO dto) {
        biddingPurchaseService.createInventoryBidingByBiddingId(dto);
        return R.success();
    }
    @PostMapping("shopManage/biddingPurchase/deleteBidingOrderItemsByBiddingId")
    @ApiOperation(value = "根据竞价id删除竞价明细")
    @LogRecord(title = "竞价采购管理",businessType = BusinessType.DELETE,operatorType = OperatorType.MANAGE)

    public R deleteBidingOrderItemsByBiddingId(@RequestBody List<String> biddingProductIds) {
        biddingPurchaseService.deleteBidingOrderItemsByBiddingId(biddingProductIds);
        return R.success();
    }


    @GetMapping("shopManage/biddingPurchase/deleteBidingByBiddingId")
    @ApiOperation(value = "删除竞价")
    @LogRecord(title = "竞价采购管理",businessType = BusinessType.DELETE,operatorType = OperatorType.MANAGE)

    public R deleteBidingByBiddingId(String biddingId) {
        biddingPurchaseService.deleteBidingByBiddingId(biddingId);
        return R.success();
    }



    @PostMapping("shopManage/biddingPurchase/batchUpdateBiddingItemInfo")
    @ApiOperation(value = "根据竞价明细修改明细信息")
    @LogRecord(title = "竞价采购管理",businessType = BusinessType.DELETE,operatorType = OperatorType.MANAGE)

    public R batchUpdateBiddingItemInfo(@RequestBody List<BatchUpdateBiddingItemInfoDTO> dtos) {
        biddingPurchaseService.batchUpdateBiddingItemInfo(dtos);
        return R.success();
    }

    @PostMapping("shopManage/biddingPurchase/submitBidingByIds")
    @ApiOperation(value = "提交审核竞价信息")
    @NotResubmit
    @IsRole(roleName = RoleEnum.ROLE_15)
    @LogRecord(title = "竞价采购管理",businessType = BusinessType.DELETE,operatorType = OperatorType.MANAGE)

    public R submitBidingByIds(@RequestBody List<String> bidingIds) {
        biddingPurchaseService.submitBidingByIds(bidingIds);
        return R.success();
    }


    @GetMapping("shopManage/biddingPurchase/isHitBidingSubmit")
    @ApiOperation(value = "中标提交审核")
    @NotResubmit
    @LogRecord(title = "竞价采购管理",businessType = BusinessType.DELETE,operatorType = OperatorType.MANAGE)

    public R isHitBidingSubmit(String bidRecordId, String biddingId) {
        biddingPurchaseService.isHitBidingSubmit(bidRecordId, biddingId);
        return R.success();
    }

    @GetMapping("shopManage/biddingPurchase/loseEfficacyBidding")
    @ApiOperation(value = "流标")
    @NotResubmit
    @LogRecord(title = "竞价采购管理",businessType = BusinessType.DELETE,operatorType = OperatorType.MANAGE)

    public R loseEfficacyBidding(String biddingId) {
        biddingPurchaseService.loseEfficacyBidding(biddingId);
        return R.success();
    }



    // 平台

    @PostMapping("platform/biddingPurchase/listAllCreateBiding")
    @ApiOperation(value = "查询发布竞价列表-平台")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<BiddingPurchase> listAllCreateBiding(@RequestBody JSONObject jsonObject) {
        PageUtils page = biddingPurchaseService.listAllCreateBiding(jsonObject, new LambdaQueryWrapper<BiddingPurchase>());
        return PageR.success(page);
    }


    @PostMapping("platform/biddingPurchase/auditBidingInfo")
    @ApiOperation(value = "审核竞价")
    @IsRole(roleName = RoleEnum.ROLE_6)
    @LogRecord(title = "竞价采购管理",businessType = BusinessType.DELETE,operatorType = OperatorType.MANAGE)

    public R auditBidingInfo(@RequestBody AuditBidingInfoDTO dto) {
        biddingPurchaseService.auditBidingInfo(dto);
        return R.success();
    }
    @PostMapping("platform/biddingPurchase/auditHitBidding")
    @ApiOperation(value = "中标审核竞价")
    @IsRole(roleName = RoleEnum.ROLE_7)
    @LogRecord(title = "竞价采购管理",businessType = BusinessType.DELETE,operatorType = OperatorType.MANAGE)

    public R auditHitBidding(@RequestBody AuditBidingInfoDTO dto) {
        biddingPurchaseService.auditHitBidding(dto);
        return R.success();
    }

    @GetMapping("platform/biddingPurchase/getPlatformBiddingPurchaseInfo")
    @ApiOperation(value = "平台获取竞价详细信息")
    public R<PlatformBiddingPurchaseInfoVO> getPlatformBiddingPurchaseInfo(String biddingSn) {
        PlatformBiddingPurchaseInfoVO vo = biddingPurchaseService.getPlatformBiddingPurchaseInfo(biddingSn);
        return R.success(vo);
    }



    //  前台
    @GetMapping("frontStage/biddingPurchase/getBidingDetail")
    @ApiOperation(value = "查询竞价详情")
    public R<BiddingPurchaseAndItemVO> getBidingDetail(String biddingSn) {
        BiddingPurchaseAndItemVO vo = biddingPurchaseService.getBidingDetail(biddingSn);
        return R.success(vo);
    }

    @GetMapping("frontStage/biddingPurchase/putBidingSupplierInfo")
    @ApiOperation(value = "参与竞价")
    @LogRecord(title = "竞价采购管理",businessType = BusinessType.INSERT,operatorType = OperatorType.MANAGE)
    public R putBidingSupplierInfo(String biddingSn) {
        biddingPurchaseService.putBidingSupplierInfo(biddingSn);
        return R.success();
    }

    @GetMapping("frontStage/biddingPurchase/checkIsBiddingSupplier")
    @ApiOperation(value = "检查是否参与竞价")
    public R checkIsBiddingSupplier(String biddingSn) {
        biddingPurchaseService.checkIsBiddingSupplier(biddingSn);
        return R.success();
    }

}














