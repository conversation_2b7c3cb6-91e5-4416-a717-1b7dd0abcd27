package scrbg.meplat.mall.vo.ship;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class MaterialShipDtlVo {

    @ApiModelProperty(value = "物资名称")
    private  String materialName;


    @ApiModelProperty(value = "材质")
    private  String texture;


    @ApiModelProperty(value = "单位")
    private  String unit;


    @ApiModelProperty(value = "规格")
    private  String skuName;

    @ApiModelProperty(value = "数量")
    private BigDecimal shipNum;



}
