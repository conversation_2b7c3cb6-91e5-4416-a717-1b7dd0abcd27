package scrbg.meplat.mall.vo.product.website;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2023-02-27 9:09
 */
@Data
public class ProductDetailDealRecordVO {
    @ApiModelProperty(value = "订单id")
    private String orderId;

    @ApiModelProperty(value = "订单号")

    private String orderSn;

    @ApiModelProperty(value = "订单项id")

    private String orderItemId;

    @ApiModelProperty(value = "用户id")

    private String userId;

    @ApiModelProperty(value = "完成时间")

    private Date flishTime;

    @ApiModelProperty(value = "商品名称")

    private String productName;

    @ApiModelProperty(value = "商品价格")

    private BigDecimal productPrice;

    @ApiModelProperty(value = "购买数量")

    private BigDecimal buyCounts;

    @ApiModelProperty(value = "头像(图片地址)")
    private String userImg;

    @ApiModelProperty(value = "昵称")
    private String nickName;


}
