package scrbg.meplat.mall.config.scheduled;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import lombok.extern.log4j.Log4j2;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.MaterialMonthSupplyPlan;
import scrbg.meplat.mall.entity.MaterialMonthSupplyPlanDtl;
import scrbg.meplat.mall.entity.OrderSelectPlan;
import scrbg.meplat.mall.entity.Orders;
import scrbg.meplat.mall.service.MaterialMonthSupplyPlanDtlService;
import scrbg.meplat.mall.service.MaterialMonthSupplyPlanService;
import scrbg.meplat.mall.service.MaterialReconciliationDtlService;
import scrbg.meplat.mall.service.OrderItemService;
import scrbg.meplat.mall.service.OrderSelectPlanService;
import scrbg.meplat.mall.service.OrdersService;
import scrbg.meplat.mall.service.ProductCategoryService;
import scrbg.meplat.mall.service.SynthesizeTemporaryDtlService;
import scrbg.meplat.mall.util.RestTemplateUtils;

@Log4j2
@EnableScheduling //开启定时器功能
@Component
public class MaterialMonthSupplyPlanConfig {


    @Autowired
    MallConfig mallConfig;

    @Autowired
    MaterialMonthSupplyPlanService materialMonthSupplyPlanService;
    @Autowired
    MaterialMonthSupplyPlanDtlService materialMonthSupplyPlanDtlService;
    @Autowired
    OrdersService ordersService;

    @Autowired
    ProductCategoryService productCategoryService;
    @Autowired
    OrderSelectPlanService orderSelectPlanService;
    /**
     * 完结计划，每小时执行一次 定时任务暂停
     */
//    @Scheduled(cron = "0 0 * * * *")
    public void closeMonthSupplyPlan() {
        //月供应计划是否完结  （1 完结计划  0 pcwp收料记录合同数量）
       if (mallConfig.selectMaterialMonthSupplyPlan==1){
           //1.查询所有未完结的月计划
           List<MaterialMonthSupplyPlan> materialMonthSupplyPlanlist = materialMonthSupplyPlanService.lambdaQuery()
                   .eq(MaterialMonthSupplyPlan::getIsClose, 0)
                   .select(MaterialMonthSupplyPlan::getPlanId,MaterialMonthSupplyPlan::getPlanNo).list();
           if (materialMonthSupplyPlanlist.size()>0){
               ArrayList<String> planIdList = new ArrayList<>();
               for (MaterialMonthSupplyPlan materialMonthSupplyPlan : materialMonthSupplyPlanlist) {
                   //查询所有计划明细
                   List<MaterialMonthSupplyPlanDtl> dtlList = materialMonthSupplyPlanDtlService.lambdaQuery()
                           .eq(MaterialMonthSupplyPlanDtl::getPlanId, materialMonthSupplyPlan.getPlanId()).list();
                   if (dtlList.size()>0){
                       boolean flag=true;
                       //统计下单数量和计划明细数量对比，计划明细全部相等，可以判断计划是否完结（本期数量=已下单数量）
                       for (MaterialMonthSupplyPlanDtl dtl : dtlList) {
                           BigDecimal qty = ordersService.getOrderUseCountBySelectPlanDtlIds(dtl.getPlanDtlId());
                           //本期数量！=已下单数量 ,计划未完成
                           if (dtl.getThisPlanQty().compareTo(qty)!=0){
                               flag=false;
                               break;
                           }
                       }
                       // 计划 本期数量=已下单数量,判断订单状态
                       if (flag){
                           //查询计划有多少订单，判断订单是否已完成
                           List<OrderSelectPlan> list = orderSelectPlanService.lambdaQuery()
                                   .eq(OrderSelectPlan::getBillNo, materialMonthSupplyPlan.getPlanNo())
                                   .eq(OrderSelectPlan::getPlanType, 12)
                                   .select(OrderSelectPlan::getOrderId)
                                   .groupBy(OrderSelectPlan::getOrderId).list();
                           Set<String> orderIds = list.stream().map(OrderSelectPlan::getOrderId).collect(Collectors.toSet());
                           //计算订单未完成的个数,数量未0，代表订单全部为已完成。记录计划id,计划已完成
                           Integer count = ordersService.lambdaQuery()
                                   .in(Orders::getOrderId, orderIds).ne(Orders::getState, 10)
                                   .count();
                           if (count==0){
                               planIdList.add(materialMonthSupplyPlan.getPlanId());
                           }
                           //判断订单状态，所有订单已完成，订单完成
                       }
                   }
               }
               if (planIdList.size()>0){
                   //修改计划完结状态
                   materialMonthSupplyPlanService.lambdaUpdate()
                           .in(MaterialMonthSupplyPlan::getPlanId,planIdList)
                           .set(MaterialMonthSupplyPlan::getIsClose,1).update();

               }




           }
       }


    }


    /**
     * 每天1点同步分类，除低值易耗品外的分类
     */
    @Scheduled(cron = "0 0 1 * * ?") // 每天凌晨2点执行
    public void synchronizationProductCategory(){
        productCategoryService.synchronizationProductCategory();

    }
    @Autowired
    RestTemplateUtils restTemplateUtils;
    @Autowired
    OrderItemService orderItemService;
    @Autowired
    MaterialReconciliationDtlService materialReconciliationDtlService;
    @Autowired
    SynthesizeTemporaryDtlService synthesizeTemporaryDtlService;
//    @Scheduled(cron = "0 0 3 * * *")
//    public  void changMatailAndProduct() {
//        List<Product> productList = productService.lambdaQuery()
//                .select(Product::getRelevanceId,Product::getRelevanceNo,Product::getClassId)
//                .groupBy(Product::getRelevanceId).list();
////        ArrayList<Product> productList = new ArrayList<>();
////        Product product1 = new Product();
////        product1.setRelevanceNo("L.05.23.01.150");
////        productList.add(product1);
//        String basicsMaterialUrl = mallConfig.prodPcwp2Url02 + BASICS_MATERIAL_LIST;
//        for (Product product : productList) {
//            HashMap<Object, Object> paramsMap = new HashMap<>();
//            paramsMap.put("pageIndex", 0);
//            paramsMap.put("pageSize", 10);
//            paramsMap.put("materialNo", product.getRelevanceNo());
//            PageUtils<Map> r = null;
//            try {
//                r = restTemplateUtils.postPCWP2PageR(basicsMaterialUrl, paramsMap);
//            } catch (Exception e) {
//                throw new BusinessException("【远程异常】获取基础库物资错误：" + e.getMessage());
//            }
//            List<Map> list = r.getList();
//            if (CollectionUtils.isEmpty(list)) {
//                System.out.println("没有找到物资：" + product.getRelevanceNo());
//            }
//            for (Map map : list) {
//                productService.lambdaUpdate().set(Product::getClassId, map.get("classId"))
//                        .set(Product::getClassPath, map.get("classIdPath"))
//                        .set(Product::getRelevanceName, map.get("materialName"))
//                        .set(Product::getClassPath, map.get("classIdPath"))
//                        .eq(Product::getRelevanceId, map.get("billId")).update();
//                synthesizeTemporaryDtlService.lambdaUpdate()
//                        .set(SynthesizeTemporaryDtl::getMaterialName, map.get("materialName"))
//                        .set(SynthesizeTemporaryDtl::getMaterialSn, map.get("billNo"))
//                        .set(SynthesizeTemporaryDtl::getUnit, map.get("unit")!=null? map.get("unit"):null)
//                        .set(SynthesizeTemporaryDtl::getSpec, map.get("spec")!=null? map.get("spec"):null)
//                        .eq(SynthesizeTemporaryDtl::getMaterialId, map.get("billId")).update();
//                orderItemService.lambdaUpdate()
//                        .set(OrderItem::getRelevanceName, map.get("materialName"))
//                        .set(OrderItem::getClassId, map.get("classId"))
//                        .set(OrderItem::getRelevanceNo,  map.get("billNo"))
//                        .set(OrderItem::getClassPathId, map.get("classIdPath"))
//                        .set(OrderItem::getClassPathName, map.get("classNamePath"))
//                        .eq(OrderItem::getRelevanceId, map.get("billId")).update();
//                materialReconciliationDtlService.lambdaUpdate().eq(MaterialReconciliationDtl::getMaterialId, map.get("billId"))
//                        .set(MaterialReconciliationDtl::getMaterialName, map.get("materialName"))
//                        .set(MaterialReconciliationDtl::getMaterialClassId, map.get("classIdPath"))
//                        .set(MaterialReconciliationDtl::getMaterialClassName, map.get("classNamePath")).update();
//            }
//        }
//
//
//    }
}
