package scrbg.meplat.mall.service.impl;

import scrbg.meplat.mall.entity.UserReEnterprise;
import scrbg.meplat.mall.mapper.UserReEnterpriseMapper;
import scrbg.meplat.mall.service.UserReEnterpriseService;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import org.springframework.transaction.annotation.Transactional;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import java.util.List;
/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2025-03-05
 */
@Service
public class UserReEnterpriseServiceImpl extends ServiceImpl<UserReEnterpriseMapper, UserReEnterprise> implements UserReEnterpriseService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<UserReEnterprise> queryWrapper) {
        IPage<UserReEnterprise> page = this.page(
        new Query<UserReEnterprise>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void create(UserReEnterprise userReEnterprise) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(userReEnterprise);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void update(UserReEnterprise userReEnterprise) {
        super.updateById(userReEnterprise);
    }


    @Override
    public UserReEnterprise getById(String id) {
        return super.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void delete(String id) {
        super.removeById(id);
    }

     @Override
     @Transactional(rollbackFor = Exception.class)
     @NotResubmit
     public void deleteBatch(List<String> ids ) {
        super.removeByIds(ids);
         }
}
