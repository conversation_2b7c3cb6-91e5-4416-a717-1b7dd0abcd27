package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.config.logRecording.LogRecord;
import scrbg.meplat.mall.config.logRecording.enums.BusinessType;
import scrbg.meplat.mall.config.logRecording.enums.OperatorType;
import scrbg.meplat.mall.entity.AdPicture;
import scrbg.meplat.mall.service.AdPictureService;

import java.util.List;

/**
 * @描述：广告图片控制类
 * @作者: y
 * @日期: 2022-11-08
 */
@RestController
@RequestMapping("/platform/adPicture")
@ApiSort(value = 500)
@Api(tags = "广告图片（后台）")
public class AdPictureController {
    @Autowired
    MallConfig mallConfig;
    @Autowired
    public AdPictureService adPictureService;

    /**
     * 新增单条数据
     *
     * @param adPicture
     * @return
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @LogRecord(title = "广告图管理",businessType = BusinessType.INSERT,operatorType = OperatorType.MANAGE)
    public R save(@RequestBody AdPicture adPicture) {
        adPicture.setState(2);
        adPicture.setMallType(mallConfig.mallType);
        adPictureService.saveOrUpdate(adPicture);
        return R.success();
    }

    /**
     * 批量更新广告图信息
     *
     * @param
     * @return
     */
    @PostMapping("/updateBatchById")
    @ApiOperation(value = "批量更新店铺信息")
    @LogRecord(title = "广告图管理",businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE)
    public R update(@RequestBody List<AdPicture> adPictures) {
        adPictureService.updateBatchById(adPictures);
        return R.success();
    }

    /**
     * 根据主键删除
     *
     * @param id
     * @return
     */
    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    @LogRecord(title = "广告图管理",businessType = BusinessType.DELETE,operatorType = OperatorType.MANAGE)
    public R delete(String id) {
        adPictureService.delete(id);
        return R.success();
    }


    @PostMapping("/updateByPublish")
    @ApiOperation(value = "批量发布")
    @LogRecord(title = "广告图管理",businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE)
    public R updatePublish(@RequestBody List<String> ids) {
        adPictureService.updateByPublish(ids, "1");
        return R.success();
    }

    @PostMapping("/updateNotPublish")
    @ApiOperation(value = "批量取消发布")
    @LogRecord(title = "广告图管理",businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE)
    @ApiImplicitParam(name = "type", value = "1：发布  2：未发布", required = true,
            dataType = "Integer", paramType = "query")
    public R updateNotPublish(@RequestBody List<String> ids) {
        adPictureService.updateByPublish(ids, "2");
        return R.success();
    }

    /**
     * 根据主键批量删除数据
     *
     * @param ids
     * @return
     */
    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    @LogRecord(title = "广告图管理",businessType = BusinessType.DELETE,operatorType = OperatorType.MANAGE)

    public R deleteBatch(@RequestBody List<String> ids) {
        adPictureService.removeByIds(ids);
        return R.success();
    }


    /**
     * 通过图片条件获取该条件所有图片并分页
     *
     * @param
     * @return
     */
    @PostMapping("/findByConditionByPage")
    @ApiOperation(value = "根据条件获取图片并分页")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "pictureType", value = "图片链接类型", dataTypeClass = Integer.class),
            @DynamicParameter(name = "useType", value = "图片面显示位置", dataTypeClass = Integer.class),
            @DynamicParameter(name = "state", value = "广告状态", dataTypeClass = Integer.class),
            @DynamicParameter(name = "mallType", value = "商城类型", dataTypeClass = String.class),
            @DynamicParameter(name = "orderBy", value = "排序方式", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
    })
    public PageR<AdPicture> findByConditionByPage(@RequestBody JSONObject jsonObject) {
        PageUtils page = adPictureService.queryAdPicturePage(jsonObject, new LambdaQueryWrapper<>());
        return PageR.success(page);
    }


}
