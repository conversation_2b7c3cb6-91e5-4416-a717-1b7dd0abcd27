package scrbg.meplat.mall.dto.content;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
* Description:  内容更新DTO
* date: 2022/11/7 15:55
* @author: sund
*/
@Data
public class UpdateContentStateDTO {

    @ApiModelProperty(value = "内容id",required = true)
    @NotNull(message = "内容id不能为空！")
    private List<String> contentId;

    @ApiModelProperty(value = "内容状态（1已发布 2未发布）",required = true)
    @NotNull(message = "内容状态不能为空！")
    @Max(value = 2, message = "内容状态输入错误！")
    @Min(value = 1, message = "内容状态输入错误！")
    private Integer state;

}
