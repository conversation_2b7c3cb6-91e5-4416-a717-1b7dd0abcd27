package scrbg.meplat.mall.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import scrbg.meplat.mall.entity.EnterpriseQualification;
import scrbg.meplat.mall.entity.File;
import scrbg.meplat.mall.mapper.EnterpriseQualificationMapper;
import scrbg.meplat.mall.service.EnterpriseQualificationService;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import org.springframework.transaction.annotation.Transactional;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import scrbg.meplat.mall.service.FileService;

import java.util.List;
/**
 * @描述：企业--资质证书表 服务类
 * @作者: ye
 * @日期: 2025-03-06
 */
@Service
public class EnterpriseQualificationServiceImpl extends ServiceImpl<EnterpriseQualificationMapper, EnterpriseQualification> implements EnterpriseQualificationService{
  @Autowired
    FileService fileService;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<EnterpriseQualification> queryWrapper) {
        IPage<EnterpriseQualification> page = this.page(
        new Query<EnterpriseQualification>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void create(EnterpriseQualification enterpriseQualification) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(enterpriseQualification);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void update(EnterpriseQualification enterpriseQualification) {
        super.updateById(enterpriseQualification);
    }


    @Override
    public EnterpriseQualification getById(String id) {
        return super.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void delete(String id) {
        super.removeById(id);
    }

     @Override
     @Transactional(rollbackFor = Exception.class)
     @NotResubmit
     public void deleteBatch(List<String> ids ) {
        super.removeByIds(ids);
         }



}
