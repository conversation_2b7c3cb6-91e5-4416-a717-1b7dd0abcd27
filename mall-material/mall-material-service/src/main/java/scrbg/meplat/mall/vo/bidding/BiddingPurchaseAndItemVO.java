package scrbg.meplat.mall.vo.bidding;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-07-24 18:50
 */
@Data
public class BiddingPurchaseAndItemVO {

    @ApiModelProperty(value = "竞价采购id")
    private String biddingId;

    @ApiModelProperty(value = "竞价采购编号")

    private String biddingSn;


    @ApiModelProperty(value = "竞价来源类型（1订单2商品）")

    private Integer biddingSourceType;


    @ApiModelProperty(value = "标题")

    private String title;


    @ApiModelProperty(value = "竞价采购类型（1公开竞价2邀请竞价）")

    private Integer type;


    @ApiModelProperty(value = "发布时间")

    private Date startTime;


    @ApiModelProperty(value = "截止时间")

    private Date endTime;


    @ApiModelProperty(value = "联系人名称")

    private String linkName;


    @ApiModelProperty(value = "联系电话")

    private String linkPhone;


    @ApiModelProperty(value = "竞价说明")

    private String biddingExplain;



    @ApiModelProperty(value = "时间状态（1未开始2进行中3已结束）")

    private Integer biddingState;


    @ApiModelProperty(value = "公示状态（0未发布1已发布）暂时废弃")

    private Integer publicityState;


    @ApiModelProperty(value = "创建机构id")

    private String createOrgId;

    @ApiModelProperty(value = "创建机构名称")

    private String createOrgName;


    @ApiModelProperty(value = "明细")
    private List<BiddingPurchaseItemVO> vos;

    @ApiModelProperty(value = "商品类型：0物资 （所有商品都是物资，只有下单才会根据分类自动生成不同的订单）最新改动：商品类型：0 低值易耗品 1大宗临购")

    private Integer productType;
    @ApiModelProperty(value = "价格类型（1浮动价格2固定价格）大宗临购使用")

    private Integer billType;
}
