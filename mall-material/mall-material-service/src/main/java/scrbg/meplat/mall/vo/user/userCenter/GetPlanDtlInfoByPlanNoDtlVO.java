package scrbg.meplat.mall.vo.user.userCenter;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023-06-30 9:25
 */
@Data
public class GetPlanDtlInfoByPlanNoDtlVO {

    @ApiModelProperty(value = "计划明细id")
    private String planDtlId;

    @ApiModelProperty(value = "物资id")

    private String materialId;
    @ApiModelProperty(value = "分类路径名称（xxx/xxx/xxx）")
    private String classPathName;

    @ApiModelProperty(value = "分类路径id（xxx/xxx/xxx）")
    private String classPathId;


    @ApiModelProperty(value = "计划id")
    private String planId;

    @ApiModelProperty(value = "计划编号")
    private String planNo;
    @ApiModelProperty(value = "物资名称")

    private String materialName;


    @ApiModelProperty(value = "规格型号")

    private String spec;


    @ApiModelProperty(value = "计量单位")

    private String unit;


    @ApiModelProperty(value = "材质")

    private String texture;


    @ApiModelProperty(value = "本期计划数量")

    private BigDecimal thisPlanQty;

    @ApiModelProperty(value = "本期计划数量（源值默认返回一样，用于前端控制本期数量修改）")

    private BigDecimal oldThisPlanQty;


    @ApiModelProperty(value = "计划明细总数量")

    private BigDecimal sourceQty;


    @ApiModelProperty(value = "计划状态（0草稿1待审核2通过3未通过4已作废）")

    private Integer state;


    @ApiModelProperty(value = "合同明细id")

    private String contractDtlId;

    @ApiModelProperty(value = "已下单数量")

    private BigDecimal orderQty;

    @ApiModelProperty(value = "已收货数量")

    private BigDecimal confirmCounts;


    // 计算的

    @ApiModelProperty(value = "剩余可用数量（计算的数量也计算了本身）")

    private BigDecimal maxQty;

    @ApiModelProperty(value = "合同总共已使用数量")

    private BigDecimal useQty;

    @ApiModelProperty(value = "选择数量（前端生成订单使用）")

    private BigDecimal selectQty;


    @ApiModelProperty(value = "变更数量（默认是本期数量）")

    private BigDecimal changeQty;



    @ApiModelProperty(value = "明细修改状态（1新增2修改3删除）默认修改")

    private Integer dtlUpdateState;

    @ApiModelProperty(value = "二级供应商id")

    private String twoSupplierId;

    @ApiModelProperty(value = "二级供应商名称")

    private String twoSupplierName;



}
