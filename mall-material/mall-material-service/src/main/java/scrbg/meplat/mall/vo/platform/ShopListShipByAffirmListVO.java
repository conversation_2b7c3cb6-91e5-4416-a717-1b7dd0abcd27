package scrbg.meplat.mall.vo.platform;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @create 2023-06-27 10:20
 */
@Data
public class ShopListShipByAffirmListVO {


    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "商品价格")
    private BigDecimal productPrice;

    @ApiModelProperty(value = "交易数量")
    private BigDecimal number;

    @ApiModelProperty(value = "退货数量")
    private BigDecimal returnCounts;

    @ApiModelProperty(value = "交易总金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "交易完成时间")
    private String finishDateStr;


    @ApiModelProperty(value = "查询合计金额")
    private BigDecimal countAmount;


    @ApiModelProperty(value = "发货单明细id")
    private String dtlId;
    public BigDecimal getNumber() {
        return number.subtract(returnCounts);
    }

    public BigDecimal getAmount() {
        return amount.subtract(returnCounts.multiply(productPrice).setScale(2, RoundingMode.HALF_UP)
    );
    }
}
