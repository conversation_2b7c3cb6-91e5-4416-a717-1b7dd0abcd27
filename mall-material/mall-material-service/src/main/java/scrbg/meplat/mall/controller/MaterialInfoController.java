package scrbg.meplat.mall.controller;

import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.pcwp.PcwpPageRes;
import scrbg.meplat.mall.pcwp.PcwpService;
import scrbg.meplat.mall.pcwp.third.model.MaterialNew;
import scrbg.meplat.mall.pcwp.third.model.MaterialPageDto;
import scrbg.meplat.mall.service.ProductService;
import scrbg.meplat.mall.service.MaterialInfoService;

/**
 * @描述：物资信息管理控制器
 * @作者: AI Assistant
 * @日期: 2025-07-25
 */
@Api(tags = "物资信息管理")
@RestController
@RequestMapping("/material-info")
@Slf4j
public class MaterialInfoController {

    @Autowired
    private ProductService productService;

    @Autowired
    private PcwpService pcwpService;

    @Autowired
    private MaterialInfoService materialInfoService;

    /**
     * 批量插入物资信息到material_info表
     * @param materialPageDto 查询参数
     * @return 插入结果
     */
    @ApiOperation("批量插入物资信息")
    @PostMapping("/batch-insert")
    public R batchInsertMaterialInfo(@RequestBody MaterialPageDto materialPageDto) {
        try {
            log.info("开始批量插入物资信息，查询参数：{}", materialPageDto);

            // 1. 调用第三方接口查询物资数据
            PcwpPageRes<MaterialNew> pcwpPageRes = pcwpService.queryPageMaterialDtl(materialPageDto);
            
            if (pcwpPageRes == null) {
                return R.error("查询物资数据失败，返回结果为空");
            }

            // 2. 批量插入到material_info表
            int insertedCount = productService.batchInsertMaterialInfo(pcwpPageRes);

            log.info("批量插入物资信息完成，插入记录数：{}", insertedCount);
            return R.ok("批量插入成功").put("insertedCount", insertedCount);

        } catch (Exception e) {
            log.error("批量插入物资信息失败", e);
            return R.error("批量插入失败：" + e.getMessage());
        }
    }

    /**
     * 批量插入多页物资信息
     * @param materialPageDto 查询参数
     * @return 插入结果
     */
    @ApiOperation("批量插入多页物资信息")
    @PostMapping("/batch-insert-multiple-pages")
    public R batchInsertMultiplePages(@RequestBody MaterialPageDto materialPageDto) {
        try {
            log.info("开始批量插入多页物资信息，查询参数：{}", materialPageDto);

            int totalInserted = 0;
            int currentPage = materialPageDto.getPageIndex() != null ? materialPageDto.getPageIndex() : 1;
            int pageSize = materialPageDto.getPageSize() != null ? materialPageDto.getPageSize() : 100;

            // 设置分页参数
            materialPageDto.setPageIndex(currentPage);
            materialPageDto.setPageSize(pageSize);

            while (true) {
                // 查询当前页数据
                PcwpPageRes<MaterialNew> pcwpPageRes = pcwpService.queryPageMaterialDtl(materialPageDto);
                
                if (pcwpPageRes == null || pcwpPageRes.getList() == null || pcwpPageRes.getList().isEmpty()) {
                    log.info("第{}页没有数据，结束批量插入", currentPage);
                    break;
                }

                // 批量插入当前页数据
                int insertedCount = materialInfoService.batchInsertFromMaterialNew(pcwpPageRes.getList());
                totalInserted += insertedCount;

                log.info("第{}页插入完成，插入记录数：{}", currentPage, insertedCount);

                // 检查是否还有下一页
                if (currentPage >= pcwpPageRes.getTotalPage()) {
                    log.info("已处理完所有页面，总页数：{}", pcwpPageRes.getTotalPage());
                    break;
                }

                // 准备处理下一页
                currentPage++;
                materialPageDto.setPageIndex(currentPage);
            }

            log.info("批量插入多页物资信息完成，总插入记录数：{}", totalInserted);
            return R.ok("批量插入成功").put("totalInserted", totalInserted);

        } catch (Exception e) {
            log.error("批量插入多页物资信息失败", e);
            return R.error("批量插入失败：" + e.getMessage());
        }
    }

    /**
     * 根据类别ID批量插入物资信息
     * @param classId 类别ID
     * @param versionId 版本ID
     * @return 插入结果
     */
    @ApiOperation("根据类别ID批量插入物资信息")
    @PostMapping("/batch-insert-by-class/{classId}")
    public R batchInsertByClassId(@PathVariable String classId, 
                                  @RequestParam(required = false) String versionId) {
        try {
            log.info("开始根据类别ID批量插入物资信息，classId：{}, versionId：{}", classId, versionId);

            // 构建查询参数
            MaterialPageDto materialPageDto = new MaterialPageDto();
            materialPageDto.setClassId(classId);
            materialPageDto.setVersionId(versionId);
            materialPageDto.setIsActive(1); // 只查询启用的物资
            materialPageDto.setPageIndex(1);
            materialPageDto.setPageSize(1000); // 每页1000条

            int totalInserted = 0;
            int currentPage = 1;

            while (true) {
                materialPageDto.setPageIndex(currentPage);
                
                // 查询当前页数据
                PcwpPageRes<MaterialNew> pcwpPageRes = pcwpService.queryPageMaterialDtl(materialPageDto);
                
                if (pcwpPageRes == null || pcwpPageRes.getList() == null || pcwpPageRes.getList().isEmpty()) {
                    break;
                }

                // 批量插入当前页数据
                int insertedCount = materialInfoService.batchInsertFromMaterialNew(pcwpPageRes.getList());
                totalInserted += insertedCount;

                log.info("类别{}第{}页插入完成，插入记录数：{}", classId, currentPage, insertedCount);

                // 检查是否还有下一页
                if (currentPage >= pcwpPageRes.getTotalPage()) {
                    break;
                }

                currentPage++;
            }

            log.info("根据类别ID批量插入完成，classId：{}，总插入记录数：{}", classId, totalInserted);
            return R.ok("批量插入成功").put("classId", classId).put("totalInserted", totalInserted);

        } catch (Exception e) {
            log.error("根据类别ID批量插入物资信息失败，classId：{}", classId, e);
            return R.error("批量插入失败：" + e.getMessage());
        }
    }
}
