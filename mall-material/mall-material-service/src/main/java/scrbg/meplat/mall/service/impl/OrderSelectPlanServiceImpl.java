package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import io.seata.common.util.StringUtils;
import org.docx4j.wml.P;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.OrderSelectPlanMapper;
import scrbg.meplat.mall.service.EnterpriseInfoService;
import scrbg.meplat.mall.service.OrderItemService;
import scrbg.meplat.mall.service.OrderSelectPlanService;
import scrbg.meplat.mall.service.OrdersService;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2023-02-28
 */
@Service
public class OrderSelectPlanServiceImpl extends ServiceImpl<OrderSelectPlanMapper, OrderSelectPlan> implements OrderSelectPlanService {

    @Autowired
    OrderItemService orderItemService;
    @Autowired
    OrdersService ordersService;

    @Autowired
    EnterpriseInfoService enterpriseInfoService;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<OrderSelectPlan> queryWrapper) {
        IPage<OrderSelectPlan> page = this.page(
                new Query<OrderSelectPlan>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(OrderSelectPlan orderSelectPlan) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(orderSelectPlan);
    }

    @Override
    public void update(OrderSelectPlan orderSelectPlan) {
        super.updateById(orderSelectPlan);
    }


    @Override
    public OrderSelectPlan getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    /**
     * 恢复计划数量
     *
     * @param orderItemId
     */
    @Override
    public void recoverPlanNum(List<String> orderItemId) {
        List<OrderSelectPlan> orderSelectPlans = lambdaQuery().in(OrderSelectPlan::getOrderItemId, orderItemId).list();
        if(CollectionUtils.isEmpty(orderSelectPlans)){
            throw  new BusinessException(400,"订单计划不存在！");
        }else {
            for (OrderSelectPlan orderSelectPlan : orderSelectPlans) {

            }

        }
    }

    @Override
    public OrderSelectPlan getDataByOrderSn(String orderSn) {
        LambdaQueryWrapper<OrderSelectPlan> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderSelectPlan::getOrderSn,orderSn);
        List<OrderSelectPlan> list = list(wrapper);
        if (list!=null&&list.size()>0){
            return list.get(0);
        }
        return null;

    }

    /**
     * 获取可对账的合同或计划列表
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils getContactPlanPageList(JSONObject jsonObject, LambdaQueryWrapper<OrderSelectPlan> q) {
        q.eq(OrderSelectPlan::getOrgId, ThreadLocalUtil.getCurrentUser().getOrgId());
        String keywords = (String) jsonObject.get("keywords");
        String storageName = (String) jsonObject.get("storageName");
        Integer productType = (Integer) jsonObject.get("productType");
        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(OrderSelectPlan::getContractNo, keywords.trim())
                        .or()
                        .like(OrderSelectPlan::getStorageName, keywords.trim())
                        .or()
                        .like(OrderSelectPlan::getBillNo, keywords.trim());
            });
        }
        if (StringUtils.isNotBlank(storageName)) {
            q.eq(OrderSelectPlan::getStorageName,storageName);
        }
        if(productType == null) {
            throw new BusinessException("请携带类型！");
        }

        q.eq(OrderSelectPlan::getProductType,productType);
        // 计划
        if(productType == 10) {
            q.groupBy(OrderSelectPlan::getBillId,OrderSelectPlan::getBillNo,OrderSelectPlan::getStorageName);
        }
        if(productType == 12) {
            q.groupBy(OrderSelectPlan::getContractId,OrderSelectPlan::getContractNo, OrderSelectPlan::getStorageName);
        }
        if(productType == 13) {
            q.groupBy(OrderSelectPlan::getBillId,OrderSelectPlan::getBillNo,OrderSelectPlan::getStorageName);
        }
        q.orderByDesc(OrderSelectPlan::getGmtCreate);
        IPage<OrderSelectPlan> page = this.page(
                new Query<OrderSelectPlan>().getPage(jsonObject),
                q
        );
        if(!CollectionUtils.isEmpty(page.getRecords())) {
            for (OrderSelectPlan record : page.getRecords()) {
//                String shortCode = record.getShortCode();
//                String creditCode = record.getCreditCode();
//                if(StringUtils.isNotBlank(shortCode)) {
                    Orders byId = ordersService.getById(record.getOrderId());

//                    EnterpriseInfo one = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getShortCode, shortCode)
//                            .select(EnterpriseInfo::getEnterpriseId, EnterpriseInfo::getTaxRate).one();
                    record.setTaxRate(byId.getTaxRate());
                    //修改，采用订单的税率，不在使用商品的税率
//                }
//                if(StringUtils.isNotBlank(creditCode)) {
//                    EnterpriseInfo one = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getSocialCreditCode, creditCode)
//                            .select(EnterpriseInfo::getEnterpriseId, EnterpriseInfo::getTaxRate).one();
//                    record.setTaxRate(one.getTaxRate());
//                }
            }
        }
        return new PageUtils(page);
    }

    /**
     * 获取可对账的合同或计划列表（获取供应商的）
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils supplierGetContactPlanPageList(JSONObject jsonObject, LambdaQueryWrapper<OrderSelectPlan> q) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String enterpriseId = user.getEnterpriseId();
        // 查询机构信息
        EnterpriseInfo e = enterpriseInfoService.lambdaQuery()
                .eq(EnterpriseInfo::getEnterpriseId, enterpriseId)
                .select(EnterpriseInfo::getInteriorId, EnterpriseInfo::getShortCode, EnterpriseInfo::getSocialCreditCode).one();
        Integer isInterior = user.getIsInterior();
        if (isInterior == 1) {
            // 内部供应商
            q.eq(OrderSelectPlan::getShortCode, e.getShortCode());
        }
        if (isInterior == 0) {
            // 外部供应商
            q.eq(OrderSelectPlan::getCreditCode, e.getSocialCreditCode());
        }

        String keywords = (String) jsonObject.get("keywords");
        String orgName = (String) jsonObject.get("orgName");
        if (StringUtils.isNotBlank(orgName)){
            q.eq(OrderSelectPlan::getOrgName,orgName);
        }
        Integer productType = (Integer) jsonObject.get("productType");
        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(OrderSelectPlan::getContractNo, keywords.trim())
                        .or()
                        .like(OrderSelectPlan::getStorageName, keywords.trim())
                        .or()
                        .like(OrderSelectPlan::getBillNo, keywords.trim());
            });
        }
        if(productType == null) {
            throw new BusinessException("请携带类型！");
        }
        q.eq(OrderSelectPlan::getProductType,productType);
        // 计划
        if(productType == 10) {
            q.groupBy(OrderSelectPlan::getBillId,OrderSelectPlan::getBillNo,OrderSelectPlan::getOrderSn, OrderSelectPlan::getOrderId);
        }
        if(productType == 12) {
            q.groupBy(OrderSelectPlan::getContractId,OrderSelectPlan::getContractNo,OrderSelectPlan::getOrderSn, OrderSelectPlan::getOrderId);
        }
        if(productType == 13) {
            q.groupBy(OrderSelectPlan::getBillId,OrderSelectPlan::getBillNo,OrderSelectPlan::getStorageName,OrderSelectPlan::getOrderId);
        }
        q.orderByDesc(OrderSelectPlan::getGmtCreate);
        IPage<OrderSelectPlan> page = this.page(
                new Query<OrderSelectPlan>().getPage(jsonObject),
                q
        );
        if(!CollectionUtils.isEmpty(page.getRecords())) {
            for (OrderSelectPlan record : page.getRecords()) {
                Orders byId = ordersService.getById(record.getOrderId());
                record.setTaxRate(byId.getTaxRate());
//                String shortCode = record.getShortCode();
//                String creditCode = record.getCreditCode();
//                if(StringUtils.isNotBlank(shortCode)) {
//                    EnterpriseInfo one = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getShortCode, shortCode)
//                            .select(EnterpriseInfo::getEnterpriseId, EnterpriseInfo::getTaxRate).one();
//                    record.setTaxRate(one.getTaxRate());
//                }
//                if(StringUtils.isNotBlank(creditCode)) {
//                    EnterpriseInfo one = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getSocialCreditCode, creditCode)
//                            .select(EnterpriseInfo::getEnterpriseId, EnterpriseInfo::getTaxRate).one();
//                    record.setTaxRate(one.getTaxRate());
//                }
            }
        }
        return new PageUtils(page);
    }


    @Override
    public OrderSelectPlan getDataByorderItemId(String orderItemId) {
        LambdaQueryWrapper<OrderSelectPlan> q = new LambdaQueryWrapper<>();
        q.eq(OrderSelectPlan::getOrderItemId,orderItemId);
        OrderSelectPlan one = getOne(q);
        return one;
    }


    @Override
    public PageUtils supplierGetEnterprisePageList(JSONObject jsonObject, LambdaQueryWrapper<OrderSelectPlan> q) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String enterpriseId = user.getEnterpriseId();
        // 查询机构信息
        EnterpriseInfo e = enterpriseInfoService.lambdaQuery()
                .eq(EnterpriseInfo::getEnterpriseId, enterpriseId)
                .select(EnterpriseInfo::getInteriorId, EnterpriseInfo::getShortCode, EnterpriseInfo::getSocialCreditCode).one();
        Integer isInterior = user.getIsInterior();
        if (isInterior == 1) {
            // 内部供应商
            q.eq(OrderSelectPlan::getShortCode, e.getShortCode());
        }
        String keywords = (String) jsonObject.get("keywords");
        Integer productType = (Integer) jsonObject.get("productType");
        if(productType == null) {
            throw new BusinessException("请携带类型！");
        }
        q.eq(OrderSelectPlan::getProductType,productType);
        // 计划
//        if(productType == 10) {
//            q.groupBy(OrderSelectPlan::getBillId,OrderSelectPlan::getBillNo,OrderSelectPlan::getOrderSn, OrderSelectPlan::getOrderId);
//        }
//        if(productType == 12) {
//            q.groupBy(OrderSelectPlan::getContractId,OrderSelectPlan::getContractNo,OrderSelectPlan::getOrderSn, OrderSelectPlan::getOrderId);
//        }
//        if(productType == 13) {
//            q.groupBy(OrderSelectPlan::getBillId,OrderSelectPlan::getBillNo,OrderSelectPlan::getStorageName);
//        }
        q.orderByDesc(OrderSelectPlan::getGmtCreate);
            q.select(OrderSelectPlan::getOrgName,OrderSelectPlan::getOrgId);
            q.groupBy(OrderSelectPlan::getOrgName);
            if (StringUtils.isNotBlank(keywords)) {
                q.and((t) -> {
                    t.like(OrderSelectPlan::getOrgName, keywords.trim());
                });

            }

        if(productType == 10) {
            q.isNotNull(OrderSelectPlan::getBillId);
        }
        if(productType == 12) {
            q.isNotNull(OrderSelectPlan::getContractId);
        }
        if(productType == 13) {
            q.isNotNull(OrderSelectPlan::getBillId);
        }
        IPage<OrderSelectPlan> page = this.page(
                new Query<OrderSelectPlan>().getPage(jsonObject),
                q
        );

        return new PageUtils(page);
    }


    @Override
    public PageUtils getContactSupplierPageList(JSONObject jsonObject, LambdaQueryWrapper<OrderSelectPlan> q) {
        q.eq(OrderSelectPlan::getOrgId, ThreadLocalUtil.getCurrentUser().getOrgId());
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        String keywords = (String) jsonObject.get("keywords");
        Integer productType = (Integer) jsonObject.get("productType");
        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(OrderSelectPlan::getContractNo, keywords.trim())
                        .or()
                        .like(OrderSelectPlan::getStorageName, keywords.trim())
                        .or()
                        .like(OrderSelectPlan::getBillNo, keywords.trim());
            });
        }
        q.groupBy(OrderSelectPlan::getStorageId,OrderSelectPlan::getStorageName);
        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(OrderSelectPlan::getStorageName, keywords.trim());
            });
        }
        q.select(OrderSelectPlan::getStorageId,OrderSelectPlan::getStorageName);
        if(productType == null) {
            throw new BusinessException("请携带类型！");
        }
        q.eq(OrderSelectPlan::getProductType,productType);
        // 计划
        if(productType == 10) {
            q.isNotNull(OrderSelectPlan::getBillId);
        }
        if(productType == 12) {
            q.isNotNull(OrderSelectPlan::getContractId);
        }
        if(productType == 13) {
            q.isNotNull(OrderSelectPlan::getBillId);
        }
        IPage<OrderSelectPlan> page = this.page(
                new Query<OrderSelectPlan>().getPage(jsonObject),
                q
        );
        return new PageUtils<>(page);
    }


    @Override
    public List<Orders> selectOrderListByPlanNo(JSONObject jsonObject) {
        LambdaQueryChainWrapper<OrderSelectPlan> q = lambdaQuery();
        q.eq(OrderSelectPlan::getOrgId, ThreadLocalUtil.getCurrentUser().getOrgId());
        String planNo = (String) jsonObject.get("planNo");
        Integer productType = (Integer) jsonObject.get("productType");
        if(productType == null) {
            throw new BusinessException("请携带类型！");
        }
        q.eq(OrderSelectPlan::getProductType,productType);
        // 计划
        if(productType == 10) {
            q.isNotNull(OrderSelectPlan::getBillId);
        }
        if(productType == 12) {
            q.isNotNull(OrderSelectPlan::getContractId);
            q.eq(OrderSelectPlan::getProductType, productType);
        }
        if(productType == 13) {
            q.isNotNull(OrderSelectPlan::getBillId);
        }
        if (StringUtils.isNotBlank(planNo)){
            q.eq(OrderSelectPlan::getBillNo,planNo);
        }
        q.select(OrderSelectPlan::getOrderId).groupBy(OrderSelectPlan::getOrderId);
        List<OrderSelectPlan> list = q.list();
        if (list.size()>0){
            List<String> ids = list.stream().map(OrderSelectPlan::getOrderId).collect(Collectors.toList());
            List<Orders> ordersList = ordersService.lambdaQuery().in(Orders::getOrderId, ids).
                    select(Orders::getOrderSn,Orders::getState,
                            MustBaseEntity::getGmtCreate,Orders::getSuccessDate,Orders::getSupplierName,Orders::getUntitled)
                    .orderByAsc(Orders::getState).list();

            return ordersList;

        }
        return new ArrayList<>();
    }
}
