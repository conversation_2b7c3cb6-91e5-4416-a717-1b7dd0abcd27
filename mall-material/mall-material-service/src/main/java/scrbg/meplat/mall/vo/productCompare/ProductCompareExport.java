package scrbg.meplat.mall.vo.productCompare;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.converters.url.UrlImageConverter;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

import java.io.Serializable;
import java.math.BigDecimal;
import java.net.URL;

/**
 * @program: maill_api
 * @description: 比价记录导出
 * @author: 代文翰
 * @create: 2023-12-12 15:09
 **/
@Data
public class ProductCompareExport implements Serializable {
    // 行高

    // 表头
    private String title;

    // 小图
    private String productMinImg;

    // 商品名称
    private String productName;

    // 规格型号
    private String skuName;


    // 品牌名称
    private String brandName;

    // 计量单位
    private String unit;

    // 销售价
    private BigDecimal sellPrice;
    // 供应商名称
    private String supplierName;

}
