package scrbg.meplat.mall.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @create 2023-03-01 16:46
 */
@Data
public class UpdateOrderPriceDTO {

    @ApiModelProperty(value = "订单项id",required = true)
    @NotEmpty(message = "订单项id不能为空！")
    private String orderItemId;

    @ApiModelProperty(value = "新价格",required = true)
    @NotNull(message = "新价格不能为空！")
    private BigDecimal productPrice;
}
