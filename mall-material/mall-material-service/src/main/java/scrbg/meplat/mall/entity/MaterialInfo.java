package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @描述：物资信息表
 * @作者: AI Assistant
 * @日期: 2025-07-25
 */
@ApiModel(value = "物资信息表")
@Data
@TableName("material_info")
public class MaterialInfo extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "排序字段")
    private Integer sort;

    @ApiModelProperty(value = "单据ID")
    private String billId;

    @ApiModelProperty(value = "单据编号")
    private String billNo;

    @ApiModelProperty(value = "类别Id")
    private String classId;

    @ApiModelProperty(value = "类别Id路径")
    private String classIdPath;

    @ApiModelProperty(value = "类别名称")
    private String className;

    @ApiModelProperty(value = "类别名称路径")
    private String classNamePath;

    @ApiModelProperty(value = "是否启用 0:停用, 1:启用")
    private Integer isEnable;

    @ApiModelProperty(value = "物资名称")
    private String materialName;

    @ApiModelProperty(value = "组织ID")
    private String orgId;

    @ApiModelProperty(value = "组织名称")
    private String orgName;

    @ApiModelProperty(value = "规格")
    private String spec;

    @ApiModelProperty(value = "顶级类别Id")
    private String topClassId;

    @ApiModelProperty(value = "顶级类别名称")
    private String topClassName;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "物资库版本Id")
    private String versionId;

    @ApiModelProperty(value = "商城类型")
    private Integer mallType;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "创建人名称")
    private String founderName;

    @ApiModelProperty(value = "创建人Id")
    private String founderId;

    @ApiModelProperty(value = "修改人名称")
    private String modifyName;

    @ApiModelProperty(value = "修改人Id")
    private String modifyId;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "逻辑删除 -1: 删除 0:未删除")
    private Integer isDelete;
}
