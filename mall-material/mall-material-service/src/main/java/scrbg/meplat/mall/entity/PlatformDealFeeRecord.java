package scrbg.meplat.mall.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
import scrbg.meplat.mall.vo.fee.DealFeePayDtlList;

/**
 * @描述：平台交易费缴费记录
 * @作者: ye
 * @日期: 2024-01-24
 */
@ApiModel(value="平台交易费缴费记录")
@Data
@TableName("platform_deal_fee_record")
public class PlatformDealFeeRecord extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "缴费记录id")
    private String dealFeeRecordId;

    @ApiModelProperty(value = "缴费记录编号")

    private String dealFeeRecordUn;


    @ApiModelProperty(value = "店铺id")

    private String shopId;


    @ApiModelProperty(value = "店铺名称")

    private String shopName;


    @ApiModelProperty(value = "企业id")

    private String enterpriseId;


    @ApiModelProperty(value = "企业名称")

    private String enterpriseName;


    @ApiModelProperty(value = "缴费金额")

    private BigDecimal payAmount;


    @ApiModelProperty(value = "缴费类型(1线下2线上3其他)")

    private Integer payType;


    @ApiModelProperty(value = "审核时间")

    private Date auditOpenTime;


    @ApiModelProperty(value = "记录类型（1店铺交易服务费2合同履约服务费用）")

    private Integer recordType;


    @ApiModelProperty(value = "状态（0草稿1待审核2审核通过3审核未通过）")

    private Integer state;


    @ApiModelProperty(value = "本次缴费多余退回余额金额")

    private BigDecimal returnBalance;


    @ApiModelProperty(value = "乐观锁")
    @Version
    private Integer version;


    @ApiModelProperty(value = "附件")
    @TableField(exist = false)
    private List<PlatformFeeFile> files;

    @ApiModelProperty(value = "是否提交")
    @TableField(exist = false)
    private int submitAud;

    @ApiModelProperty(value = "审核历史")
    @TableField(exist = false)
    private List<AuditRecord> auditRecords;


    @ApiModelProperty(value = "缴费明细")
    @TableField(exist = false)
    private List<PlatformDealFeeRecordDtl> dtls;


    @ApiModelProperty(value = "缴费明细查询返回")
    @TableField(exist = false)
    private List<DealFeePayDtlList> dtlVOs;

}