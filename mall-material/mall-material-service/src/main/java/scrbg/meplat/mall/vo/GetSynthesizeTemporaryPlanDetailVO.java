package scrbg.meplat.mall.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-10-13 9:32
 */
@Data
public class GetSynthesizeTemporaryPlanDetailVO {

    @ApiModelProperty(value = "大宗临购单id")
    private String synthesizeTemporaryId;

    @ApiModelProperty(value = "大宗临购单编号")

    private String synthesizeTemporarySn;


    @ApiModelProperty(value = "采购单位内部id")

    private String orgFarId;


    @ApiModelProperty(value = "采购单位id")

    private String orgId;


    @ApiModelProperty(value = "采购单位名称")

    private String orgName;


    @ApiModelProperty(value = "供应商内部机构id")

    private String supplierOrgFarId;


    @ApiModelProperty(value = "供应商机构id")

    private String supplierOrgId;


    @ApiModelProperty(value = "供应商机构名称")

    private String supplierOrgName;


    @ApiModelProperty(value = "供应商信用代码")

    private String supplierCreditCode;


    @ApiModelProperty(value = "供应商机构简码")

    private String supplierShortCode;

    @ApiModelProperty(value = "省")

    private String province;


    @ApiModelProperty(value = "市")

    private String city;


    @ApiModelProperty(value = "县、区")

    private String county;

    @ApiModelProperty(value = "项目收货地址")

    private String receiverAddress;

    @ApiModelProperty(value = "清单类型（1浮动价格2固定价格）")

    private Integer billType;

    @ApiModelProperty(value = "参考总金额（不会变化）")

    private BigDecimal referenceSumAmount;


    @ApiModelProperty(value = "综合总金额（会变化，网价+固定费+管理费）")

    private BigDecimal synthesizeSumAmount;


    @ApiModelProperty(value = "提交时间")

    private Date submitTime;


    @ApiModelProperty(value = "审核时间（供应商确认时间）")

    private Date auditTime;


    @ApiModelProperty(value = "状态（0草稿1已提交2供应商已确认3已推送大宗临购计划）")

    private Integer state;


    @ApiModelProperty(value = "采购单位是否删除（0否1是）这个删除是指供应商已确认后删除操作，采购单位不可见")

    private Integer orgIsDelete;


    @ApiModelProperty(value = "货款支付周期（单位月）")

    private Integer paymentWeek;

    @ApiModelProperty(value = "超期垫资利息（%）")

    private BigDecimal outPhaseInterest;

    @ApiModelProperty(value = "明细")
    private List<GetSynthesizeTemporaryPlanDetailItemVO> details;



    // 计划的信息

    @ApiModelProperty(value = "计划id")

    private String billId;

    @ApiModelProperty(value = "计划编号")

    private String billNo;

    @ApiModelProperty(value = "商城大宗清单id")

    private String sourceBillId;

    @ApiModelProperty(value = "商城大宗清单编号")

    private String sourceBillNo;

    @ApiModelProperty(value = "pcwp计划备注")

    private String remarks;

    @ApiModelProperty(value = "pcwp计划含税金额")

    private BigDecimal totalAmount;

    @ApiModelProperty(value = "pcwp计划不含税金额")

    private BigDecimal amount;

    @ApiModelProperty(value = "税率")

    private BigDecimal taxRate;

    @ApiModelProperty(value = "机构简码")

    private String orgShort;
    @ApiModelProperty(value = "信用代码")

    private String creditCode;


    @ApiModelProperty(value = "计划日期")

    private String planDate;

    @ApiModelProperty(value = "商品供应商机构名称")

    private String supplierName;


    /**
     * 提交生成订单使用
     */

    @ApiModelProperty(value = "支付方式 1线上支付 2内部结算 3线下转账", required = true)
    @NotNull(message = "支付方式不能为空！")
    private Integer payWay;

    @ApiModelProperty(value = "收货人",required = true)
    @NotEmpty(message = "收货人不能为空！")
    private String receiverName;

    @ApiModelProperty(value = "收货人手机号",required = true)
    @NotEmpty(message = "收货人手机号不能为空！")
    private String receiverMobile;

    @ApiModelProperty(value = "收货地址",required = true)
    @NotEmpty(message = "收货地址不能为空！")
    private String subOrderAddress;


    @ApiModelProperty(value = "订单备注")
    private String orderRemark;
}
