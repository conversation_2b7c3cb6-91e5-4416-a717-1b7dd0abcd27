package scrbg.meplat.mall.controller;
import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.mall.config.logRecording.LogRecord;
import scrbg.meplat.mall.config.logRecording.enums.BusinessType;
import scrbg.meplat.mall.config.logRecording.enums.OperatorType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.service.EnterpriseBankAccountsService;
import scrbg.meplat.mall.entity.EnterpriseBankAccounts;

import java.util.List;

/**
 * @描述：企业-对公账户表控制类
 * @作者: ye
 * @日期: 2025-03-06
 */
@RestController
@RequestMapping("/enterpriseBankAccounts")
@Api(tags = "企业-对公账户表")
public class EnterpriseBankAccountsController{

@Autowired
public EnterpriseBankAccountsService enterpriseBankAccountsService;

@PostMapping("/listByEntity")
@ApiOperation(value = "根据实体属性分页查询")
@DynamicParameters(name = "根据实体属性分页查询", properties = {
        @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
        @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
})
public PageR<EnterpriseBankAccounts> listByEntity(@RequestBody JSONObject jsonObject){
        PageUtils page= enterpriseBankAccountsService.queryPage(jsonObject,new LambdaQueryWrapper<EnterpriseBankAccounts>());
        return PageR.success(page);
        }

@GetMapping("/findById")
@ApiOperation(value = "根据主键查询")
@ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "ID", required = true,
                dataType = "String", paramType = "query")
})
public R<EnterpriseBankAccounts> findById(String id){
    EnterpriseBankAccounts enterpriseBankAccounts = enterpriseBankAccountsService.getById(id);
        return R.success(enterpriseBankAccounts);
        }

@PostMapping("/create")
@ApiOperation(value = "新增")
@LogRecord(title = "xx管理",businessType = BusinessType.INSERT,operatorType = OperatorType.MANAGE)

public R save(@RequestBody EnterpriseBankAccounts enterpriseBankAccounts){
    enterpriseBankAccountsService.create(enterpriseBankAccounts);
        return R.success();
        }

@PostMapping("/update")
@ApiOperation(value = "修改")
@LogRecord(title = "xx管理",businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE)

public R update(@RequestBody EnterpriseBankAccounts enterpriseBankAccounts){
    enterpriseBankAccountsService.update(enterpriseBankAccounts);
        return R.success();
        }

@GetMapping("/delete")
@ApiOperation(value = "根据主键删除")
@ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "ID", required = true,
                dataType = "String", paramType = "query")
})
@LogRecord(title = "xx管理",businessType = BusinessType.DELETE,operatorType = OperatorType.MANAGE)

public R delete(String id){
    enterpriseBankAccountsService.delete(id);
        return R.success();
        }


@PostMapping("/deleteBatch")
@ApiOperation(value = "根据主键批量删除")
@LogRecord(title = "xx管理",businessType = BusinessType.DELETE,operatorType = OperatorType.MANAGE)

public R deleteBatch(@RequestBody List<String> ids){
    enterpriseBankAccountsService.removeByIds(ids);
        return R.success();
        }
        }

