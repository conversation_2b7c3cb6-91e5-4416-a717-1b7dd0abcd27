package scrbg.meplat.mall.util.poi.exp;

import com.itextpdf.text.DocumentException;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.util.excel.excel2pdf.Excel2Pdf;
import scrbg.meplat.mall.util.excel.excel2pdf.ExcelObject;
import scrbg.meplat.mall.util.poi.exception.PoiElErrorCode;
import scrbg.meplat.mall.util.poi.exp.context.PoiExporterContext;
import scrbg.meplat.mall.util.poi.exp.function.FunctionRegister;
import scrbg.meplat.mall.util.poi.exp.processor.RowProcessorStrategy;
import scrbg.meplat.mall.util.poi.log.Log;

import java.io.*;
import java.net.MalformedURLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * excel导出类
 */
public class PoiExporter {
	private static final Logger logger = LoggerFactory.getLogger(PoiExporter.class);

	/**
	 * 向StandardEvaluationContext中注册内部函数
	 */
	static {
		FunctionRegister.registerInternalFunction();
	}

	public static void export(XSSFWorkbook wb, Map<String, Object> rootObjectMap) {
		Long start = System.currentTimeMillis();
		PoiExporterContext peContext = new PoiExporterContext(new SpelExpressionParser(), rootObjectMap);
		// 分sheet进行处理
		for (int i = 0; i < wb.getNumberOfSheets(); i++) {
			XSSFSheet sheet = wb.getSheetAt(i);
			// 开始行结束行
			int j = sheet.getFirstRowNum();
			// 每行
			while (j <= sheet.getLastRowNum()) {
				try {
					XSSFRow row = sheet.getRow(j);
					if (row == null) {
						j++;
						continue;
					}
					int dealRows = RowProcessorStrategy.getRowProcessor(row).dealRow(row, peContext);
					if(dealRows == 0) {
						dealRows = 1;
					}
					j = j + dealRows;
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}

		long end = System.currentTimeMillis();
		logger.info(Log.op("PoiEl#parse").msg("PoiEl解析模板耗时[{0}]ms", (end - start)).toString());
	}

	/**
	 * 导出到指定地方 des
	 * @param templateFile
	 * @param rootObjectMap
	 * @param des
	 * @return
	 */
	public static XSSFWorkbook export2Destination(File templateFile, Map<String, Object> rootObjectMap, OutputStream des){
		InputStream in = null;
		try {
			in = new FileInputStream(templateFile);
		} catch (FileNotFoundException e) {
			throw new BusinessException("excel导出失败"+ e.getMessage());
		}
		return export2Destination(in, rootObjectMap, des);
	}

	/**
	 * 导出到指定地方 des
	 * @param templateInputStream 模板
	 * @param rootObjectMap 数据
	 * @param des 导出的位置
	 * @return
	 */
	public static XSSFWorkbook export2Destination(InputStream templateInputStream, Map<String, Object> rootObjectMap, OutputStream des){
		XSSFWorkbook wb = null;
		try {
			wb = new XSSFWorkbook(templateInputStream);
		} catch (IOException e) {
			throw new BusinessException(e.getMessage());
		}
		PoiExporter.export(wb, rootObjectMap);
		// 关闭资源
		try {
			wb.write(des);
		} catch (IOException e) {
			throw PoiElErrorCode.SYSTEM_ERROR.exp(e);
		}catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				des.flush();
				des.close();
			}catch (Exception e) {
				throw PoiElErrorCode.SYSTEM_ERROR.exp(e);
			}
		}
		return wb;
	}


}
