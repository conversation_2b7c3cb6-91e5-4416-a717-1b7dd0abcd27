package scrbg.meplat.mall.vo.platform;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/***
 * 店铺利润统计
 */
@Data
public class ShopCountVo {

    @ApiModelProperty(value = "序列号")
    private String serialNum;

    @ApiModelProperty(value = "店铺名称")

    private String shopName;
    @ApiModelProperty(value = "订单数量")

    private String ordersCount;
    @ApiModelProperty(value = "总利润")

    private BigDecimal profitPriceTotal;

    @ApiModelProperty(value = "总成本价")

    private BigDecimal costPriceTotal;
    @ApiModelProperty(value = "订单总价格")

    private BigDecimal totalAmount;

    @ApiModelProperty(value = "实际支付订单总价格")
    private BigDecimal actualAmount;

}
