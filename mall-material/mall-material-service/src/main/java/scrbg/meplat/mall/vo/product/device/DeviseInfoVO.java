package scrbg.meplat.mall.vo.product.device;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.File;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022-11-29 12:55
 */
@Data
public class DeviseInfoVO  {
    @ApiModelProperty(value = "商品id")
    private String productId;

    @ApiModelProperty(value = "商品编码")
    private String serialNum;

    @ApiModelProperty(value = "商品名称")

    private String productName;


    @ApiModelProperty(value = "店铺id")

    private String shopId;


    @ApiModelProperty(value = "分类id")

    private String classId;


    @ApiModelProperty(value = "商品描述")

    private String productDescribe;


    @ApiModelProperty(value = "商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备")

    private Integer productType;


    @ApiModelProperty(value = "商品状态（0待上架 1已上架 2已下架）")

    private Integer state;


    @ApiModelProperty(value = "商品关键字（,分隔）")

    private String productKeyword;


    @ApiModelProperty(value = "商品运费类型（0商家包邮）")

    private Integer productTransportType;


    @ApiModelProperty(value = "商品的最低价")

    private BigDecimal productMinPrice;


    @ApiModelProperty(value = "商品库id")

    private String productInventoryId;


    @ApiModelProperty(value = "关联外部id")

    private String relevanceId;


    @ApiModelProperty(value = "品牌id")

    private String brandId;


    @ApiModelProperty(value = "上架时间")
    private Date putawayDate;


    @ApiModelProperty(value = "销量")

    private BigDecimal soldNum;


    @ApiModelProperty(value = "商品访问量")

    private Integer productVisitNum;


    @ApiModelProperty(value = "省")

    private String province;


    @ApiModelProperty(value = "市")

    private String city;


    @ApiModelProperty(value = "县、区")

    private String county;


    @ApiModelProperty(value = "详细地址")

    private String detailedAddress;


    @ApiModelProperty(value = "经度")

    private BigDecimal longitude;


    @ApiModelProperty(value = "纬度")

    private BigDecimal latitude;


    @ApiModelProperty(value = "店铺排序")

    private Integer shopSort;


    @ApiModelProperty(value = "综合排序")

    private Integer synthesisSort;


    @ApiModelProperty(value = "商品小图")

    private String productMinImg;


    @ApiModelProperty(value = "关联名称（用于关联外部的名称唯一不修改）")

    private String relevanceName;

    @ApiModelProperty(value = "关联编号")

    private String relevanceNo;

    @ApiModelProperty(value = "是否完成商品编辑（0否1是）")

    private Integer isCompletion;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "商城类型：0物资商场, 1设备商城 ")
    private Integer mallType;

    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;

    @ApiModelProperty(value = "更新时间")
    private Date gmtModified;

    @ApiModelProperty(value = "创建人名称")
    private String founderName;

    @ApiModelProperty(value = "创建人Id")
    private String founderId;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "逻辑删除 -1: 删除 0:未删除")
    private Integer isDelete;

    @ApiModelProperty(value = "出厂日期")
    private Date leaveFactory;

    @ApiModelProperty(value = "质量、成色")
    private Integer quality;

    @ApiModelProperty(value = "是否周材")
    private Integer isWeekMaterials;
    /**
     * 新加
     */

    @ApiModelProperty(value = "分类路径")
    private List<String> classPath;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;


    /**
     * file
     */
    @ApiModelProperty(value = "商品主图")
    private List<File> adminFile;

    @ApiModelProperty(value = "商品小图")
    private List<File> minFile;

    @ApiModelProperty(value = "商品图片")
    private List<File> productFiles;

    /**
     * sku
     */

    @ApiModelProperty(value = "skuid")
    private String skuId;

    @ApiModelProperty(value = "sku名称")
    private String skuName;

    @ApiModelProperty(value = "成本价")
    private BigDecimal costPrice;

    @ApiModelProperty(value = "原价")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "销售价格")
    private BigDecimal sellPrice;

    @ApiModelProperty(value = "库存")
    private BigDecimal stock;

    @ApiModelProperty(value = "规格单位")
    private String unit;

    @ApiModelProperty(value = "租赁时长")
    private BigDecimal leaseNum;

    @ApiModelProperty(value = "租赁单位（天月年）")
    private String leaseUnit;

    @ApiModelProperty(value = "结算价")
    private BigDecimal settlePrice;

    @ApiModelProperty(value = "属性")
    private Map attrMap;
}
