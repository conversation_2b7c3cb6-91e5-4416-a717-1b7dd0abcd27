package scrbg.meplat.mall.vo.user.userCenter;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-01-18 15:01
 */
@Data
public class OpenShopInfoVO {


    @ApiModelProperty(value = "店铺id")
    private String shopId;

    @ApiModelProperty(value = "序列号")
    private String serialNum;

    @ApiModelProperty(value = "店铺名称")

    private String shopName;

    @ApiModelProperty(value = "省")

    private String province;


    @ApiModelProperty(value = "市")

    private String city;


    @ApiModelProperty(value = "县、区")

    private String county;


    @ApiModelProperty(value = "详细地址")

    private String detailedAddress;

    @ApiModelProperty(value = "主营业务")

    private String mainBusiness;

    @ApiModelProperty(value = "审核失败原因")

    private String failReason;



    @ApiModelProperty(value = "联系人")

    private String linkMan;

    @ApiModelProperty(value = "联系电话")

    private String contactNumber;
}
