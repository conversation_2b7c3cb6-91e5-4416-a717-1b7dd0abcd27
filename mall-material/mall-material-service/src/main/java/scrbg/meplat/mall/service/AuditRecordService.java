package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.AuditRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.AuditRecord;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import scrbg.meplat.mall.entity.SupplierReconciliation;

/**
 * @描述：审核记录 服务类
 * @作者: ye
 * @日期: 2023-06-27
 */
public interface AuditRecordService extends IService<AuditRecord> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<AuditRecord> queryWrapper);

        void create(AuditRecord auditRecord);

        void update(AuditRecord auditRecord);

        AuditRecord getById(String id);

        void delete(String id);

        /**
         * 更具审核状态船家女审核列表 (不通过)
         * @param relevanceId 关联id
         * @param resultType 关联类型（1月供计划2月供变更计划3竞价采购提交4竞价采购中标5对账单6二级对账单
         * @param auditResult   审核结果
         */
        public void createDataNoPass(String relevanceId, int resultType, String auditResult);
        /**
         * 更具审核状态船家女审核列表 (通过)
         * @param relevanceId 关联id
         * @param resultType 关联类型（1月供计划2月供变更计划3竞价采购提交4竞价采购中标5对账单6二级对账单
         */
        public void createDataPass(String relevanceId, int resultType);
}
