package scrbg.meplat.mall.vo.bidding;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @program: maill_api
 * @description: 已经中标的数据对象
 * @author: 代文翰
 * @create: 2024-01-30 15:06
 **/
@Data
public class HitBidVo {
    @ApiModelProperty(value = "供应商id（本地）")

    private String supplierId;


    @ApiModelProperty(value = "供应商名称（本地）")

    private String supplierName;
    @ApiModelProperty(value = "竞价采购id")
    private String biddingId;
    @ApiModelProperty(value = "竞价采购商品编号")

    private String productSn;

    @ApiModelProperty(value = "不含税到场单价")

    private BigDecimal bidPrice;
    @ApiModelProperty(value = "含税到场单价")

    private BigDecimal bidRatePrice;
    @ApiModelProperty(value = "税率")

    private BigDecimal taxRate;
    @ApiModelProperty(value = "价格类型（1浮动价格2固定价格）大宗临购使用")

    private Integer billType;
    @ApiModelProperty(value = "网价（浮动价格使用）")

    private BigDecimal netPrice;

    @ApiModelProperty(value = "固定费用（浮动价格使用）")

    private BigDecimal fixationPrice;

    @ApiModelProperty(value = "出厂价（固定价格使用）")

    private BigDecimal outFactoryPrice;

    @ApiModelProperty(value = "运杂费（固定价格使用）")

    private BigDecimal transportPrice;
}
