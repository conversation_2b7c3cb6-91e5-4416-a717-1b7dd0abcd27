package scrbg.meplat.mall.dto.thirdapi;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.excelTemplate.Material;

import java.util.List;

@Data
public class BatchUpdateMaterialDtlState {


    @ApiModelProperty(value = "物资id")
    private List<MaterialDtlDTO> MaterialDtlDTOList;
    @ApiModelProperty(value = "物资编号")
    private List<String> billNo;
    @ApiModelProperty(value = "状态")
    private String state;
}
