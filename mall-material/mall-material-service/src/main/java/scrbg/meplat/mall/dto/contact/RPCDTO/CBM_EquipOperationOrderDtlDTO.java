package scrbg.meplat.mall.dto.contact.RPCDTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023-04-21 10:29
 */
@Data
public class CBM_EquipOperationOrderDtlDTO {

    @ApiModelProperty(value = "订单ID")
    private String billId;
    @ApiModelProperty(value = "订单明细id")
    private String OrderDtlId;
    @ApiModelProperty(value = "合同装备明细ID")
    private String contractDtlId;
    @ApiModelProperty(value = "装备id")
    private String itemId;
    @ApiModelProperty(value = "装备名称")
    private String itemName;
    @ApiModelProperty(value = "规格型号")
    private String size;
    @ApiModelProperty(value = "单位")
    private String unit;
    @ApiModelProperty(value = "本次下订单数量")
    private BigDecimal thisOrderQty;
    @ApiModelProperty(value = "本次下订单单价")
    private BigDecimal thisOrderPrice;
    @ApiModelProperty(value = "本次下订单金额")
    private BigDecimal thisOrderAmount;

}
