package scrbg.meplat.mall.vo.user.userCenter;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.AuditRecord;
import scrbg.meplat.mall.entity.MaterialMonthSupplyPlanChange;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-06-30 9:24
 */
@Data
public class GetPlanDtlInfoByPlanNoVO {

    @ApiModelProperty(value = "计划Id")
    private String planId;

    @ApiModelProperty(value = "计划编号")

    private String planNo;


    @ApiModelProperty(value = "计划日期")
    @JsonFormat(pattern = "yyyy-MM")
    private Date planDate;


    @ApiModelProperty(value = "计划创建日期")
    private Date planCreateDate;


    @ApiModelProperty(value = "业务类型（物资采购合同0、事实合同1、零星采购计划2）")

    private Integer businessType;


    @ApiModelProperty(value = "合同id")

    private String contractId;


    @ApiModelProperty(value = "合同编号")

    private String contractNo;


    @ApiModelProperty(value = "供应商id（pcwp）")

    private String supplierId;


    @ApiModelProperty(value = "供应商名称")

    private String supplierName;


    @ApiModelProperty(value = "机构id（pcwp）")

    private String orgId;


    @ApiModelProperty(value = "机构名称")

    private String orgName;


    @ApiModelProperty(value = "计划状态（0草稿1待审核2通过3未通过4已作废）")

    private Integer state;


    @ApiModelProperty(value = "备注")
    private String remarks;


    @ApiModelProperty(value = "本地供应商id")

    private String localSupplierId;

    @ApiModelProperty(value = "本地机构id")

    private String localOrgId;


    @ApiModelProperty(value = "计划明细")

    private List<GetPlanDtlInfoByPlanNoDtlVO> dtls;


    @ApiModelProperty(value = "变更计划列表")

    private List<MaterialMonthSupplyPlanChange> planChanges;


    @ApiModelProperty(value = "审核历史")

    private List<AuditRecord> auditList;


    @ApiModelProperty(value = "pcwp版本区分（1:1.0，2:2.0）")

    private Integer pcwpVersion;


    @ApiModelProperty(value = "变更次数")
    private int alterationCount;

    @ApiModelProperty(value = "是否完结  1 完结  0 未完结")
    private Integer isClose;

}
