package scrbg.meplat.mall.vo.shopManage.reportForms;


import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;

@Data
public class ProductFromVo {
    @ApiModelProperty(value = "商品id")
    private String productId;
    @ApiModelProperty(value = "商品编码")
    private String serialNum;
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;
    @ApiModelProperty(value = "商品名称")
    private String productName;
    @ApiModelProperty(value = "成本价")
    @TableField(exist = false)
    private BigDecimal costPrice;
    @ApiModelProperty(value = "原价")
    @TableField(exist = false)
    private BigDecimal originalPrice;
    @ApiModelProperty(value = "销售价格")
    @TableField(exist = false)
    private BigDecimal sellPrice;

    @ApiModelProperty(value = "差价")
    @TableField(exist = false)
    private BigDecimal profitPrice;

    @ApiModelProperty(value = "上架时间")
    private Date putawayDate;

    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;

    @ApiModelProperty(value = "交易完成时间")
    @TableField(exist = false)
    private String putawayDateStr;

    @ApiModelProperty(value = "交易完成时间")
    @TableField(exist = false)
    private String gmtCreateStr;
}
