package scrbg.meplat.mall.pcwp;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;
import scrbg.meplat.mall.pcwp.third.PcwpMaterialClient;
import scrbg.meplat.mall.pcwp.third.model.Material;
import scrbg.meplat.mall.pcwp.third.model.MaterialNew;
import scrbg.meplat.mall.pcwp.third.model.MaterialPageDto;

@Slf4j
@Service
public class PcwpServiceImpl implements PcwpService {

    @Autowired
    private PcwpMaterialClient pcwpMaterialClient;

    private static final String SYS_CODE_PCWP2 = "pcwp2";

    /**
     * 固定token
     */
    @Value("${mall.thirdApiToken}")
    private String token;

    @Override
    public PcwpRes<List<Material>> getAllCategoryLibrary() {
        // 根据前端代码 /PCWP2/thirdapi/ 开头的url请求header里都要添加syscode=pcwp2
        return pcwpMaterialClient.getAllCategoryLibrary(token, SYS_CODE_PCWP2);
    }

    @Override
    public PcwpPageRes<MaterialNew> queryPageMaterialDtl(MaterialPageDto materialPageDto) {
        return pcwpMaterialClient.queryPageMaterialDtl(materialPageDto,token, SYS_CODE_PCWP2);
    }

    @Override
    public PcwpRes<Map<String, String>> getVersionByOrgId(String orgId) {
        return pcwpMaterialClient.getVersionByOrgId(orgId, token, SYS_CODE_PCWP2);
    }

}
