package scrbg.meplat.mall.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023-05-19 13:56
 */
@Data
public class SubmitPlanAndOrderDTO {

    @ApiModelProperty(value = "商品id")
    private String productId;

    @ApiModelProperty(value = "数量")
    private BigDecimal buyNum;

    @ApiModelProperty(value = "金额")
    private BigDecimal account;

    @ApiModelProperty(value = "计划id")
    private String BillId;

    @ApiModelProperty(value = "计划编号")
    private String BillNo;

    @ApiModelProperty(value = "计划明细id")
    private String DtlId;
}
