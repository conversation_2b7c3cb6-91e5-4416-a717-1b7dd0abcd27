package scrbg.meplat.mall.service.impl;

import scrbg.meplat.mall.entity.EnterprisePerformance;
import scrbg.meplat.mall.mapper.EnterprisePerformanceMapper;
import scrbg.meplat.mall.service.EnterprisePerformanceService;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import org.springframework.transaction.annotation.Transactional;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import java.util.List;
/**
 * @描述：企业-业绩表 服务类
 * @作者: ye
 * @日期: 2025-03-06
 */
@Service
public class EnterprisePerformanceServiceImpl extends ServiceImpl<EnterprisePerformanceMapper, EnterprisePerformance> implements EnterprisePerformanceService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<EnterprisePerformance> queryWrapper) {
        IPage<EnterprisePerformance> page = this.page(
        new Query<EnterprisePerformance>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void create(EnterprisePerformance enterprisePerformance) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(enterprisePerformance);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void update(EnterprisePerformance enterprisePerformance) {
        super.updateById(enterprisePerformance);
    }


    @Override
    public EnterprisePerformance getById(String id) {
        return super.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void delete(String id) {
        super.removeById(id);
    }

     @Override
     @Transactional(rollbackFor = Exception.class)
     @NotResubmit
     public void deleteBatch(List<String> ids ) {
        super.removeByIds(ids);
         }
}
