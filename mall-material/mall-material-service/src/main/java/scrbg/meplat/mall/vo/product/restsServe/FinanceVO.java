package scrbg.meplat.mall.vo.product.restsServe;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.File;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022-11-29 12:55
 */
@Data
public class FinanceVO {

    @ApiModelProperty(value = "商品id")
    private String productId;

    @ApiModelProperty(value = "商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备 6维修服务 7金融服务 8保险服务")
    private Integer productType;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "商品简介")
    private String productIntro;

    @ApiModelProperty(value = "商品描述")
    private String productDescribe;

    @ApiModelProperty(value = "金额")
    private BigDecimal productMinPrice;

    @ApiModelProperty(value = "商品状态（0待上架 1已上架 2已下架）")

    private Integer state;
    /**
     * file
     */
    @ApiModelProperty(value = "商品主图")
    private List<File> adminFile;

    @ApiModelProperty(value = "属性")
    private Map attr;


}
