package scrbg.meplat.mall.dto.product.material;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-12-02 9:18
 */
@Data
public class ImportMaterialDTO {

    @ApiModelProperty(value = "商品类型 0物资 2周材（物资）")
    private Integer productType;

    @ApiModelProperty(value = "分类id")
    private String classId;

    /**
     *  外部导入
     */
    @ApiModelProperty(value = "商品库id")
    private String productInventoryId;


    @ApiModelProperty(value = "主键物资id")
    private String relevanceId;

    @ApiModelProperty(value = "物资编号")
    private String relevanceNo;

    @ApiModelProperty(value = "物资名称")
    private String productTitle;

    @ApiModelProperty(value = "规格型号")
    private String specTitle;

    @ApiModelProperty(value = "计量单位")
    private String unit;

}
