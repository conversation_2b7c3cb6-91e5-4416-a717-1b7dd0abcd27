package scrbg.meplat.mall.dto.outer;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.File;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-02-27 16:42
 */
@Data
public class UpdateUsedDeviceDTO {

    @ApiModelProperty(value = "商品id",required = true)
    @NotEmpty(message = "商品id不能为空！")
    private String productId;


    @ApiModelProperty(value = "店铺id",required = true)
    @NotEmpty(message = "店铺id不能为空！")
    private String shopId;

    @ApiModelProperty(value = "关联名称（用于关联外部计划设备名称）",required = true)
    @NotEmpty(message = "关联名称不能为空！")
    private String relevanceName;

    @ApiModelProperty(value = "分类路径（父到子顺序）",required = true)
    @NotEmpty(message = "分类路径不能为空！")
    private List<String> classPath;

    @ApiModelProperty(value = "商品名称",required = true)
    @NotEmpty(message = "商品名称不能为空！")
    private String productName;
    @ApiModelProperty(value = "商品关键字")
    private String productKeyword;

    @ApiModelProperty(value = "分类id",required = true)
    @NotEmpty(message = "分类id不能为空！")
    private String classId;

    @ApiModelProperty(value = "商品的最低价",required = false)
    private BigDecimal productMinPrice;

    @ApiModelProperty(value = "品牌id",required = true)
    @NotEmpty(message = "品牌id不能为空！")
    private String brandId;

    @ApiModelProperty(value = "省",required = true)
    @NotEmpty(message = "省不能为空！")
    private String province;

    @ApiModelProperty(value = "市",required = true)
    @NotEmpty(message = "市不能为空！")
    private String city;

    @ApiModelProperty(value = "县、区",required = true)
    @NotEmpty(message = "县、区不能为空！")
    private String county;

    @ApiModelProperty(value = "详细完整地址",required = true)
    @NotEmpty(message = "详细完整地址不能为空！")
    private String detailedAddress;

    @ApiModelProperty(value = "店铺排序")
    private Integer shopSort;

    @ApiModelProperty(value = "商品描述")
    private String productDescribe;

    @ApiModelProperty(value = "出厂日期",required = true)
    @NotNull(message = "出厂日期不能为空！")
    private Date leaveFactory;

    @ApiModelProperty(value = "质量、成色（存储数值）",required = true)
    @NotNull(message = "质量、成色不能为空！")
    private Integer quality;

    private Integer state;

    /**
     * file
     */

    @ApiModelProperty(value = "商品主图(传入一个)", required = true)
    @NotEmpty(message = "商品主图不能为空！")
    private List<File> adminFile;

    @ApiModelProperty(value = "商品小图（传入一个）", required = true)
    @NotEmpty(message = "商品小图不能为空！")
    private List<File> minFile;

    @ApiModelProperty(value = "商品图片",required = true)
    @NotEmpty(message = "商品图片不能为空！")
    private List<File> productFiles;

    /**
     * sku
     */
    @ApiModelProperty(value = "skuId",required = true)
    @NotEmpty(message = "skuId不能为空！")
    private String skuId;

    @ApiModelProperty(value = "规格名称",required = true)
    @NotEmpty(message = "规格名称不能为空！")
    private String skuName;

    @ApiModelProperty(value = "成本价",required = true)
    @NotNull(message = "成本价不能为空！")
    private BigDecimal costPrice;

    @ApiModelProperty(value = "原价",required = true)
    @NotNull(message = "原价不能为空！")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "销售价格",required = true)
    @NotNull(message = "销售价格不能为空！")
    private BigDecimal sellPrice;

    @ApiModelProperty(value = "库存",required = true)
    @NotNull(message = "库存不能为空！")
    private BigDecimal stock;

    @ApiModelProperty(value = "规格单位",required = true)
    @NotEmpty(message = "规格单位不能为空！")
    private String unit;

    @ApiModelProperty(value = "结算价")
    private BigDecimal settlePrice;
}
