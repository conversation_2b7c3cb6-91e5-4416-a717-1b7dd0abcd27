<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.OrdersMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.Orders" id="OrdersMap">
        <result property="orderId" column="order_id"/>
        <result property="orderSn" column="order_sn"/>
        <result property="shopName" column="shop_name"/>
        <result property="shopId" column="shop_id"/>
        <result property="userId" column="user_id"/>
        <result property="untitled" column="untitled"/>
        <result property="receiverName" column="receiver_name"/>
        <result property="receiverMobile" column="receiver_mobile"/>
        <result property="receiverAddress" column="receiver_address"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="actualAmount" column="actual_amount"/>
        <result property="dealType" column="deal_type"/>
        <result property="payType" column="pay_type"/>
        <result property="payWay" column="pay_way"/>
        <result property="orderRemark" column="order_remark"/>
        <result property="state" column="state"/>
        <result property="orderBillState" column="order_bill_state"/>
        <result property="deliveryType" column="delivery_type"/>
        <result property="deliveryFlowId" column="delivery_flow_id"/>
        <result property="orderFreight" column="order_freight"/>
        <result property="isDelete" column="is_delete"/>
        <result property="payTime" column="pay_time"/>
        <result property="deliveryTime" column="delivery_time"/>
        <result property="flishTime" column="flish_time"/>
        <result property="cancelTime" column="cancel_time"/>
        <result property="closeType" column="close_type"/>
        <result property="tradeNo" column="trade_no"/>
        <result property="productType" column="product_type"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderName" column="founder_name"/>
        <result property="founderId" column="founder_id"/>
        <result property="remarks" column="remarks"/>
        <result property="sort" column="sort"/>
        <result property="mallType" column="mall_type"/>

    </resultMap>

    <select id="listUserOrderPageList" resultType="scrbg.meplat.mall.vo.user.userCenter.UserOrderPageListVO"
            parameterType="map">
        select
        o.`order_id`,
        o.`order_sn`,
        o.`shop_id`,
        o.`enterprise_id`,
        o.`enterprise_name`,
        oi.`order_item_id`,
        oi.`product_id`,
        oi.`product_name` untitled,
        o.`receiver_name`,
        oi.`total_amount` actualAmount,
        o.`state`,
        o.`order_bill_state`,
        o.`flish_time`,
        o.`gmt_create`,
        oi.`buy_counts`,
        oi.`product_img`,
        oi.`is_comment`,
        oi. `product_type`,
        oi. `invoice_state`,
        oi. `return_state`,
        oi. `return_counts`,
        oi.`pcwp_return`,
        oi.ship_counts,
        oi.confirm_counts
        from orders o
        inner join order_item oi on o.order_id = oi.order_id and o.is_delete = 0 and oi.is_delete = 0
        <where>
             o.mall_type = #{dto.mallType} and o.parent_order_id is null
        <if test="dto.enterpriseIds != null and dto.enterpriseIds.size()>0">
          and   o.enterprise_id in
            <foreach collection="dto.enterpriseIds" item="itemType" open="(" close=")" separator=",">
                #{itemType}
            </foreach>
        </if>
            <if test="dto.state != null">
                and o.`state` = #{dto.state}
            </if>
            <if test="dto.isComment != null">
                and oi.`is_comment` = #{dto.isComment}
            </if>
            <if test="dto.productType != null">
                and o.`product_type` = #{dto.productType}
            </if>
            <if test="dto.startDate != null and dto.startDate != '' and dto.endDate != null and dto.endDate != ''">
                and o.`gmt_create` between #{dto.startDate} and #{dto.endDate}
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (o.`untitled` LIKE CONCAT('%',#{dto.keywords},'%') or oi.`product_sn` LIKE
                CONCAT('%',#{dto.keywords},'%') or o.`order_sn` LIKE CONCAT('%',#{dto.keywords},'%'))
            </if>
        </where>
        order by o.`gmt_create` desc ,oi.order_item_id desc
    </select>

    <select id="listUserOrderPageListCount" resultType="int"
            parameterType="map">
        select count(*)
        from orders o
        inner join order_item oi on o.order_id = oi.order_id and o.is_delete = 0 and oi.is_delete = 0
        <where>
            o.mall_type = #{dto.mallType} and o.parent_order_id is null
            <if test="dto.enterpriseIds != null and dto.enterpriseIds.size()>0">
                and   o.enterprise_id in
                <foreach collection="dto.enterpriseIds" item="itemType" open="(" close=")" separator=",">
                    #{itemType}
                </foreach>
            </if>
            <if test="dto.state != null">
                and o.`state` = #{dto.state}
            </if>
            <if test="dto.isComment != null">
                and oi.`is_comment` = #{dto.isComment}
            </if>
            <if test="dto.productType != null">
                and o.`product_type` = #{dto.productType}
            </if>
            <if test="dto.startDate != null and dto.startDate != '' and dto.endDate != null and dto.endDate != ''">
                and o.`gmt_create` between #{dto.startDate} and #{dto.endDate}
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (o.`untitled` LIKE CONCAT('%',#{dto.keywords},'%') or oi.`product_sn` LIKE
                CONCAT('%',#{dto.keywords},'%') or o.`order_sn` LIKE CONCAT('%',#{dto.keywords},'%'))
            </if>
        </where>
    </select>
    <select id="findOrderByorderSn" resultType="scrbg.meplat.mall.entity.Orders">
        select *  from orders  where order_sn=#{orderSn}
    </select>

    <select id="getPlatformWeekOrdersCount" resultType="scrbg.meplat.mall.vo.platform.PlatformOrdersCountVO">
        SELECT
            DATE_FORMAT( flish_time, '%Y-%u' ) AS WEEK,
            count(*) AS count
        FROM
            orders
        <where>
            mall_type = #{dto.mallType} and `state` = 6
            <if test="dto.shopId != null">
                and `shop_id` = #{dto.shopId}
            </if>
            <if test="dto.orderClass != null and dto.orderClass !='' ">
                and `order_class` != #{dto.orderClass}
            </if>
            <if test="dto.startDate != null and dto.startDate != '' and dto.endDate != null and dto.endDate != ''">
                and `flish_time` between #{dto.startDate} and #{dto.endDate}
            </if>
        </where>
        GROUP BY
            WEEK
        ORDER BY WEEK
    </select>

    <select id="getPlatformOrderTotalCount" resultType="scrbg.meplat.mall.vo.platform.PlatformShopCountVo">
        select count(order_id) as ordersCount,
               sum(cost_price_total) as costPriceTotal ,
               sum(profit_price_total) as profitPriceTotal,
               sum(total_amount) as totalAmount,
               sum(actual_amount) as actualAmount
        from orders
        where  ${ew.sqlSegment}
    </select>
    <select id="getPlatformShopOrderCount" resultType="scrbg.meplat.mall.vo.platform.ShopCountVo">
        select s.shop_name, count(o.order_id) as ordersCount,
               sum(o.cost_price_total) as costPriceTotal ,
               sum(o.profit_price_total) as profitPriceTotal,
               sum(o.total_amount) as totalAmount,
               sum(o.actual_amount) as actualAmount
        from orders  o
                 join shop    s
                      on   o.shop_id=s.shop_id
            where  ${ew.sqlSegment}
    </select>
    <resultMap id="getShopManageOrderOutZIPDataMap" type="scrbg.meplat.mall.vo.order.GetShopManageOrderOutZIPDataVO">
        <result column="supplier_name" property="supplierName"/>
        <result column="enterprise_name" property="enterpriseName"/>
        <result column="enterprise_name" property="enterpriseName"/>
        <collection property="dataList" ofType="scrbg.meplat.mall.vo.order.GetShopManageOrderOutZIPDataItemVO">
            <result property="orderSn" column="order_sn"/>
            <result property="createTime" column="gmt_create"/>
            <result property="productName" column="product_name"/>
            <result property="skuName" column="sku_name"/>
            <result property="unit" column="unit"/>
            <result property="buyCounts" column="buy_counts"/>
            <result property="productPrice" column="product_price"/>
            <result property="noRateAmount" column="no_rate_amount"/>
            <result property="totalAmount" column="total_amount"/>
            <result property="orderRemark" column="order_remark"/>
        </collection>
    </resultMap>

    <select id="getShopManageOrderOutZIPData"
            resultMap="getShopManageOrderOutZIPDataMap">
        SELECT
            o.supplier_name ,
            o.enterprise_name ,
            o.order_sn ,
            o.gmt_create ,
            oi.product_name ,
            oi.sku_name ,
            oi.unit ,
            oi.buy_counts ,
            oi.product_price ,
            oi.no_rate_amount ,
            oi.total_amount ,
            o.order_remark
        FROM
            `orders` o
                INNER JOIN order_item oi on o.order_id = oi.order_id
        where ${ew.sqlSegment}
    </select>



    <resultMap id="getShopManageOrderOutZIPDataMap2" type="scrbg.meplat.mall.vo.order.GetShopManageOrderOutZIPVO">
        <result column="supplier_name" property="supplierName"/>
        <result column="enterprise_name" property="enterpriseName"/>
        <result column="out_phase_interest" property="outPhaseInterest"/>
        <result column="payment_week" property="paymentWeek"/>
        <result column="success_date" property="successDate"/>
        <result property="orderSn" column="order_sn"/>
        <collection property="dataList" ofType="scrbg.meplat.mall.vo.order.GetShopManageOrderOutZIPDataItemVO">
            <result property="orderSn" column="order_sn"/>
            <result property="createTime" column="gmt_create"/>
            <result property="texture" column="texture"/>
            <result property="relevanceName" column="relevance_name"/>
            <result property="productName" column="product_name"/>
            <result property="skuName" column="sku_name"/>
            <result property="unit" column="unit"/>
            <result property="buyCounts" column="buy_counts"/>
            <result property="productPrice" column="product_price"/>
            <result property="noRateAmount" column="no_rate_amount"/>
            <result property="totalAmount" column="total_amount"/>
            <result property="orderRemark" column="order_remark"/>
        </collection>
    </resultMap>

    <select id="selectOutOrderList"
            resultMap="getShopManageOrderOutZIPDataMap2">
        SELECT
            o.supplier_name ,
            o.enterprise_name ,
            o.order_sn ,
            o.gmt_create ,
            o.payment_week,
            o.out_phase_interest,
            o.success_date,
            oi.product_name ,
            oi.sku_name ,
            oi.relevance_name ,
            oi.texture ,
            oi.unit ,
            oi.buy_counts ,
            oi.product_price ,
            oi.no_rate_amount ,
            oi.total_amount ,
            o.order_remark
        FROM
            `orders` o
                INNER JOIN order_item oi on o.order_id = oi.order_id
        where ${ew.sqlSegment}
    </select>
    <select id="getPlatformShopOrderCountExcel" resultType="scrbg.meplat.mall.vo.platform.ShopCountVo">
        select s.shop_name, count(o.order_id) as ordersCount,
               sum(o.cost_price_total) as costPriceTotal ,
               sum(o.profit_price_total) as profitPriceTotal,
               sum(o.total_amount) as totalAmount,
               sum(o.actual_amount) as actualAmount
        from orders  o
                 join shop    s
                      on   o.shop_id=s.shop_id
        where  ${ew.sqlSegment}
    </select>

</mapper>
