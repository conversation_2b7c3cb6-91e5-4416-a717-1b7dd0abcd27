<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.DealOrderInfoMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.DealOrderInfo" id="DealOrderInfoMap">
        <result property="dealOrderInfoId" column="deal_order_info_id"/>
        <result property="shopId" column="shop_id"/>
        <result property="shopName" column="shop_name"/>
        <result property="buyOrgId" column="buy_org_id"/>
        <result property="bugOrgName" column="bug_org_name"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="orderSn" column="order_sn"/>
        <result property="orderId" column="order_id"/>
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="number" column="number"/>
        <result property="amount" column="amount"/>
        <result property="costAmount" column="cost_amount"/>
        <result property="finishDate" column="finish_date"/>
        <result property="skuName" column="sku_name"/>
        <result property="unit" column="unit"/>
        <result property="dealType" column="deal_type"/>
        <result property="orderFinishDate" column="order_finish_date"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="sort" column="sort"/>
        <result property="mallType" column="mall_type"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>


</mapper>