<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.MaterialInfoMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.MaterialInfo" id="MaterialInfoMap">
        <id property="id" column="id"/>
        <result property="sort" column="sort"/>
        <result property="billId" column="bill_id"/>
        <result property="billNo" column="bill_no"/>
        <result property="classId" column="class_id"/>
        <result property="classIdPath" column="class_id_path"/>
        <result property="className" column="class_name"/>
        <result property="classNamePath" column="class_name_path"/>
        <result property="isEnable" column="is_enable"/>
        <result property="materialName" column="material_name"/>
        <result property="orgId" column="org_id"/>
        <result property="orgName" column="org_name"/>
        <result property="spec" column="spec"/>
        <result property="topClassId" column="top_class_id"/>
        <result property="topClassName" column="top_class_name"/>
        <result property="unit" column="unit"/>
        <result property="versionId" column="version_id"/>
        <result property="mallType" column="mall_type"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderName" column="founder_name"/>
        <result property="founderId" column="founder_id"/>
        <result property="modifyName" column="modify_name"/>
        <result property="modifyId" column="modify_id"/>
        <result property="remarks" column="remarks"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

    <!-- 批量插入物资信息 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO material_info (
            sort, bill_id, bill_no, class_id, class_id_path, class_name, class_name_path,
            is_enable, material_name, org_id, org_name, spec, top_class_id, top_class_name,
            unit, version_id, mall_type, gmt_create, gmt_modified, founder_name, founder_id,
            modify_name, modify_id, remarks, is_delete
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.sort}, #{item.billId}, #{item.billNo}, #{item.classId}, #{item.classIdPath},
                #{item.className}, #{item.classNamePath}, #{item.isEnable}, #{item.materialName},
                #{item.orgId}, #{item.orgName}, #{item.spec}, #{item.topClassId}, #{item.topClassName},
                #{item.unit}, #{item.versionId}, #{item.mallType}, #{item.gmtCreate}, #{item.gmtModified},
                #{item.founderName}, #{item.founderId}, #{item.modifyName}, #{item.modifyId},
                #{item.remarks}, #{item.isDelete}
            )
        </foreach>
    </insert>

    <!-- 根据单据ID和单据编号查询物资信息 -->
    <select id="selectByBillIdAndBillNo" resultMap="MaterialInfoMap">
        SELECT * FROM material_info 
        WHERE bill_id = #{billId} AND bill_no = #{billNo} AND is_delete = 0
    </select>

    <!-- 根据物资名称和类别ID查询物资信息 -->
    <select id="selectByMaterialNameAndClassId" resultMap="MaterialInfoMap">
        SELECT * FROM material_info 
        WHERE material_name = #{materialName} AND class_id = #{classId} AND is_delete = 0
    </select>

</mapper>
