<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.InvoiceRecordMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.InvoiceRecord" id="InvoiceRecordMap">
        <result property="invoiceRecordId" column="invoice_record_id"/>
        <result property="state" column="state"/>
        <result property="invoiceType" column="invoice_type"/>
        <result property="riseType" column="rise_type"/>
        <result property="userName" column="user_name"/>
        <result property="userAddress" column="user_address"/>
        <result property="userPhone" column="user_phone"/>
        <result property="email" column="email"/>
        <result property="company" column="company"/>
        <result property="dutyParagraph" column="duty_paragraph"/>
        <result property="registerAddress" column="register_address"/>
        <result property="registerPhone" column="register_phone"/>
        <result property="bank" column="bank"/>
        <result property="bankAccount" column="bank_account"/>
        <result property="isDelete" column="is_delete"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="sort" column="sort"/>
    </resultMap>


</mapper>
