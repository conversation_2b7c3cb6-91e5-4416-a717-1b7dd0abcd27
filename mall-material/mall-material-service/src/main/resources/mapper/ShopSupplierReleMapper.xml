<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.ShopSupplierReleMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.ShopSupplierRele" id="ShopSupplierReleMap">
        <result property="shopSupplierReleId" column="shop_supplier_rele_id"/>
        <result property="shopId" column="shop_id"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="sort" column="sort"/>
        <result property="mallType" column="mall_type"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

    <select id="listShopListBySupplierId" resultType="scrbg.meplat.mall.entity.ShopSupplierRele">
        select s.shop_id,
        s.shop_Name
        from shop_supplier_rele ss
        inner join shop s on ss.shop_id = s.shop_id
        <where>
            ss.is_delete = 0 and ss.supplier_id = #{innerMap.enterpriseId}
            <if test="innerMap.shopName != null and innerMap.shopName != ''">
                and s.`shop_name` LIKE CONCAT('%',#{innerMap.shopName},'%')
            </if>
            <if test="innerMap.shopIds!= null and innerMap.shopIds.size() > 0">
                and s.`shop_id` not in
                <foreach item="shopId" collection="innerMap.shopIds" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
        </where>
        GROUP BY s.shop_name, s.shop_id
    </select>

    <select id="listShopListBySupplierIdCount" resultType="int">
        SELECT
        count(*)
        FROM
        (select s.shop_name
        from shop_supplier_rele ss
        inner join shop s on ss.shop_id = s.shop_id
        <where>
            ss.is_delete = 0 and ss.supplier_id = #{innerMap.enterpriseId}
            <if test="innerMap.shopName != null and innerMap.shopName != ''">
                and s.`shop_name` LIKE CONCAT('%',#{innerMap.shopName},'%')
            </if>
        </where>
        GROUP BY s.shop_name) total
    </select>


</mapper>
