package scrbg.meplat.mall;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import scrbg.meplat.mall.entity.MaterialInfo;
import scrbg.meplat.mall.pcwp.PcwpPageRes;
import scrbg.meplat.mall.pcwp.PcwpService;
import scrbg.meplat.mall.pcwp.third.model.MaterialNew;
import scrbg.meplat.mall.pcwp.third.model.MaterialPageDto;
import scrbg.meplat.mall.service.MaterialInfoService;
import scrbg.meplat.mall.service.ProductService;
import scrbg.meplat.mall.utils.MaterialInfoBatchUtil;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @描述：物资信息批量插入测试类
 * @作者: AI Assistant
 * @日期: 2025-07-25
 */
@Slf4j
@SpringBootTest
public class MaterialInfoBatchInsertTest {

    @Autowired
    private MaterialInfoService materialInfoService;

    @Autowired
    private MaterialInfoBatchUtil materialInfoBatchUtil;

    @Autowired
    private PcwpService pcwpService;

    @Autowired
    private ProductService productService;

    /**
     * 测试基本的批量插入功能
     */
    @Test
    public void testBasicBatchInsert() {
        log.info("开始测试基本批量插入功能");

        // 创建测试数据
        List<MaterialInfo> materialInfoList = createTestMaterialInfoList(5);

        // 执行批量插入
        int insertedCount = materialInfoService.batchInsert(materialInfoList);

        log.info("批量插入完成，插入记录数：{}", insertedCount);
        assert insertedCount == 5;
    }

    /**
     * 测试从MaterialNew转换并批量插入
     */
    @Test
    public void testBatchInsertFromMaterialNew() {
        log.info("开始测试从MaterialNew转换并批量插入");

        // 创建测试数据
        List<MaterialNew> materialNewList = createTestMaterialNewList(3);

        // 执行批量插入
        int insertedCount = materialInfoService.batchInsertFromMaterialNew(materialNewList);

        log.info("从MaterialNew批量插入完成，插入记录数：{}", insertedCount);
        assert insertedCount >= 0; // 可能因为重复检查而插入数量小于原始数量
    }

    /**
     * 测试数据转换功能
     */
    @Test
    public void testConvertFromMaterialNew() {
        log.info("开始测试数据转换功能");

        // 创建测试MaterialNew对象
        MaterialNew materialNew = createTestMaterialNew("TEST001", "测试物资001");

        // 执行转换
        MaterialInfo materialInfo = materialInfoService.convertFromMaterialNew(materialNew);

        // 验证转换结果
        assert materialInfo != null;
        assert materialInfo.getBillId().equals(materialNew.getBillId());
        assert materialInfo.getMaterialName().equals(materialNew.getMaterialName());
        assert materialInfo.getMallType() == 0;
        assert materialInfo.getIsDelete() == 0;

        log.info("数据转换测试通过，转换后的MaterialInfo：{}", materialInfo);
    }

    /**
     * 测试重复检查功能
     */
    @Test
    public void testIsExist() {
        log.info("开始测试重复检查功能");

        // 创建测试数据并插入
        MaterialNew materialNew = createTestMaterialNew("TEST002", "测试物资002");
        materialInfoService.batchInsertFromMaterialNew(List.of(materialNew));

        // 检查是否存在
        boolean exists = materialInfoService.isExist(materialNew);
        log.info("重复检查结果：{}", exists);

        // 再次尝试插入相同数据
        int insertedCount = materialInfoService.batchInsertFromMaterialNew(List.of(materialNew));
        log.info("重复插入结果，插入记录数：{}", insertedCount);
        assert insertedCount == 0; // 应该为0，因为数据已存在
    }

    /**
     * 测试工具类处理分页结果
     */
    @Test
    public void testProcessPcwpPageRes() {
        log.info("开始测试工具类处理分页结果");

        // 创建模拟的分页结果
        PcwpPageRes<MaterialNew> pcwpPageRes = createMockPcwpPageRes();

        // 使用工具类处理
        int insertedCount = materialInfoBatchUtil.processPcwpPageRes(pcwpPageRes);

        log.info("工具类处理分页结果完成，插入记录数：{}", insertedCount);
        assert insertedCount >= 0;
    }

    /**
     * 测试通过第三方接口查询并批量插入（需要网络连接）
     */
    @Test
    public void testQueryAndBatchInsert() {
        log.info("开始测试通过第三方接口查询并批量插入");

        try {
            // 构建查询参数
            MaterialPageDto materialPageDto = new MaterialPageDto();
            materialPageDto.setPageIndex(1);
            materialPageDto.setPageSize(10);
            materialPageDto.setIsActive(1);

            // 查询数据
            PcwpPageRes<MaterialNew> pcwpPageRes = pcwpService.queryPageMaterialDtl(materialPageDto);

            if (pcwpPageRes != null && pcwpPageRes.getList() != null && !pcwpPageRes.getList().isEmpty()) {
                // 批量插入
                int insertedCount = productService.batchInsertMaterialInfo(pcwpPageRes);
                log.info("通过第三方接口查询并批量插入完成，插入记录数：{}", insertedCount);
            } else {
                log.warn("第三方接口返回数据为空");
            }

        } catch (Exception e) {
            log.error("测试第三方接口查询失败，可能是网络问题", e);
            // 这个测试可能因为网络问题失败，不影响其他测试
        }
    }

    /**
     * 创建测试用的MaterialInfo列表
     */
    private List<MaterialInfo> createTestMaterialInfoList(int count) {
        List<MaterialInfo> list = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();

        for (int i = 1; i <= count; i++) {
            MaterialInfo materialInfo = new MaterialInfo();
            materialInfo.setBillId("TEST_BILL_" + i);
            materialInfo.setBillNo("TEST_NO_" + i);
            materialInfo.setMaterialName("测试物资_" + i);
            materialInfo.setClassId("TEST_CLASS_" + i);
            materialInfo.setClassName("测试类别_" + i);
            materialInfo.setIsEnable(1);
            materialInfo.setUnit("个");
            materialInfo.setSpec("标准规格");
            materialInfo.setMallType(0);
            materialInfo.setGmtCreate(now);
            materialInfo.setGmtModified(now);
            materialInfo.setIsDelete(0);
            materialInfo.setFounderId("TEST_USER");
            materialInfo.setFounderName("测试用户");
            materialInfo.setModifyId("TEST_USER");
            materialInfo.setModifyName("测试用户");

            list.add(materialInfo);
        }

        return list;
    }

    /**
     * 创建测试用的MaterialNew列表
     */
    private List<MaterialNew> createTestMaterialNewList(int count) {
        List<MaterialNew> list = new ArrayList<>();

        for (int i = 1; i <= count; i++) {
            MaterialNew materialNew = createTestMaterialNew("NEW_BILL_" + i, "新测试物资_" + i);
            list.add(materialNew);
        }

        return list;
    }

    /**
     * 创建测试用的MaterialNew对象
     */
    private MaterialNew createTestMaterialNew(String billId, String materialName) {
        MaterialNew materialNew = new MaterialNew();
        materialNew.setBillId(billId);
        materialNew.setBillNo("NO_" + billId);
        materialNew.setMaterialName(materialName);
        materialNew.setClassId("TEST_CLASS");
        materialNew.setClassName("测试类别");
        materialNew.setClassIdPath("/ROOT/TEST_CLASS");
        materialNew.setClassNamePath("根目录/测试类别");
        materialNew.setIsEnable(1);
        materialNew.setUnit("个");
        materialNew.setSpec("测试规格");
        materialNew.setOrgId("TEST_ORG");
        materialNew.setOrgName("测试组织");
        materialNew.setVersionId("V1.0");

        return materialNew;
    }

    /**
     * 创建模拟的分页结果
     */
    private PcwpPageRes<MaterialNew> createMockPcwpPageRes() {
        PcwpPageRes<MaterialNew> pcwpPageRes = new PcwpPageRes<>();
        pcwpPageRes.setTotalCount(2);
        pcwpPageRes.setPageSize(10);
        pcwpPageRes.setTotalPage(1);
        pcwpPageRes.setCurrPage(1);

        List<MaterialNew> materialNewList = createTestMaterialNewList(2);
        pcwpPageRes.setList(materialNewList);

        return pcwpPageRes;
    }
}
