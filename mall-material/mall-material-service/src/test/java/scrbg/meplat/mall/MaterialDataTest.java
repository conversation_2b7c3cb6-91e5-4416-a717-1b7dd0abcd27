package scrbg.meplat.mall;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import scrbg.meplat.mall.entity.Product;
import scrbg.meplat.mall.entity.ProductCategory;
import scrbg.meplat.mall.mapper.ProductCategoryMapper;
import scrbg.meplat.mall.mapper.ProductMapper;
import scrbg.meplat.mall.pcwp.PcwpPageRes;
import scrbg.meplat.mall.pcwp.PcwpRes;
import scrbg.meplat.mall.pcwp.PcwpService;
import scrbg.meplat.mall.pcwp.third.model.Material;
import scrbg.meplat.mall.pcwp.third.model.MaterialNew;
import scrbg.meplat.mall.pcwp.third.model.MaterialPageDto;

import java.util.List;

/**
 * 物资数据测试类
 * 用于测试PcwpService的getAllCategoryLibrary接口
 *
 * <AUTHOR>
 * @create 2025-07-25
 */
@Log4j2
@SpringBootTest
public class MaterialDataTest {

    @Autowired
    private PcwpService pcwpService;

    @Autowired
    private ProductCategoryMapper categoryMapper;

    /**
     * 测试获取所有物资类别信息 - 简化版本
     * 只打印基本信息
     */
    @Test
    public void testGetAllCategoryLibrarySimple() {
        log.info("开始简化测试获取所有物资类别信息...");

        try {
            List<ProductCategory> categoryList = categoryMapper.selectList(
                new QueryWrapper<ProductCategory>()
                    .eq("state", 1)
                    .eq("version_id", "1942518269757186050")
                    .eq("class_level", 3)
                    .likeRight("class_path", "低值")
            );
            for(ProductCategory category:categoryList) {
                MaterialPageDto materialPageDto = new MaterialPageDto();
                materialPageDto.setIsActive(1);
                materialPageDto.setPageIndex(1);
                materialPageDto.setPageSize(Integer.MAX_VALUE);
                materialPageDto.setClassId(category.getClassId());
                materialPageDto.setVersionId("1942518269757186050");
                PcwpPageRes<MaterialNew> pcwpPageRes = pcwpService.queryPageMaterialDtl(materialPageDto);

                // 使用日志框架打印
                log.info("查询结果: {}", pcwpPageRes);

            }


            PcwpRes<List<Material>> response = null;

            if (response != null && response.getCode() == 200) {
                List<Material> materialList = response.getData();

                if (materialList != null && !materialList.isEmpty()) {
                    System.out.println("=== 物资类别信息列表 ===");
                    System.out.println("总数量: " + materialList.size());
                    System.out.println();

                    for (Material material : materialList) {
                        System.out.printf("ID: %s | 编号: %s | 名称: %s | 启用状态: %s%n",
                                material.getBillId(),
                                material.getBillNo(),
                                material.getClassName(),
                                material.getIsEnable() == 1 ? "启用" : "停用");
                    }

                    System.out.println("\n=== 测试完成 ===");
                } else {
                    System.out.println("未获取到物资类别数据");
                }
            } else {
                System.out.println("接口调用失败: " + (response != null ? response.getMessage() : "响应为空"));
            }

        } catch (Exception e) {
            System.out.println("测试过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
