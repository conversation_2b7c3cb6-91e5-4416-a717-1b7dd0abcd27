package scrbg.meplat.mall;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import scrbg.meplat.mall.entity.MaterialInfo;
import scrbg.meplat.mall.entity.Product;
import scrbg.meplat.mall.entity.ProductCategory;
import scrbg.meplat.mall.mapper.ProductCategoryMapper;
import scrbg.meplat.mall.mapper.ProductMapper;
import scrbg.meplat.mall.pcwp.PcwpPageRes;
import scrbg.meplat.mall.pcwp.PcwpRes;
import scrbg.meplat.mall.pcwp.PcwpService;
import scrbg.meplat.mall.pcwp.third.model.Material;
import scrbg.meplat.mall.pcwp.third.model.MaterialNew;
import scrbg.meplat.mall.pcwp.third.model.MaterialPageDto;
import scrbg.meplat.mall.service.MaterialInfoService;
import scrbg.meplat.mall.utils.ThreadLocalUtil;
import scrbg.meplat.mall.vo.user.UserLogin;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 物资数据测试类
 * 用于测试PcwpService的getAllCategoryLibrary接口
 *
 * <AUTHOR>
 * @create 2025-07-25
 */
@Log4j2
@SpringBootTest
public class MaterialDataTest {

    @Autowired
    private PcwpService pcwpService;

    @Autowired
    private ProductCategoryMapper categoryMapper;

    /**
     * 测试获取所有物资类别信息 - 简化版本
     * 只打印基本信息
     */
    @Test
    public void testGetAllCategoryLibrarySimple() {
        log.info("开始简化测试获取所有物资类别信息...");

        try {
            List<ProductCategory> categoryList = categoryMapper.selectList(
                new QueryWrapper<ProductCategory>()
                    .eq("state", 1)
                    .eq("version_id", "1942518269757186050")
                    .eq("class_level", 3)
                    .likeRight("class_path", "低值")
            );
            for(ProductCategory category:categoryList) {
                MaterialPageDto materialPageDto = new MaterialPageDto();
                materialPageDto.setIsActive(1);
                materialPageDto.setPageIndex(1);
                materialPageDto.setPageSize(Integer.MAX_VALUE);
                materialPageDto.setClassId(category.getClassId());
                materialPageDto.setVersionId("1942518269757186050");
                PcwpPageRes<MaterialNew> pcwpPageRes = pcwpService.queryPageMaterialDtl(materialPageDto);

                // 使用日志框架打印
                log.info("查询结果: {}", pcwpPageRes);

            }
        } catch (Exception e) {
            System.out.println("测试过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
