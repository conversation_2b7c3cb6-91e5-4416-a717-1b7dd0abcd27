spring:
  cloud:
    nacos:
      config:
        enabled: false # 关闭
      discovery:
        server-addr: 192.168.91.9:8848
        register-enabled: false
  datasource:
    username: root
    password: qlows@12js
    url: **************************************************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
#接口文档
knife4j:
  enable: true
  setting:
    enableSwaggerModels: true
    swaggerModelName: 实体类列表
minio:
  endpoint: http://192.168.91.11:9001 #Minio服务所在地址
  accessKey: minioadmin #访问的key
  secretKey: SL@sl1#lT9@19 #访问的秘钥
