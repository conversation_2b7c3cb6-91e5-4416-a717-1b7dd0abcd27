package com.y.oss.interceptor;

import com.y.oss.exception.BusinessException;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @create 2022-12-14 16:39
 */
@Component
public class CheckTokenInterceptor implements HandlerInterceptor {
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 放行options请求
        String method = request.getMethod();
        if ("OPTIONS".equalsIgnoreCase(method)) {
            return true;
        }
        String token = request.getHeader("token");
        if (token == null) {
            throw new BusinessException(400, "请携带上传令牌！");
        } else {
            if (token.equals("UXp0UjJET2csMTY3MTA0MjIzMw.H83JNfMc8fze5OF-Vn4oSF4SqF4Wgm6PnZTK01adYvrQpLTZSN4ekA")) {
                return true;
            } else {
                // 对外使用的token，物资导入方使用
                if(token.equals("UXp0UjJET2csMTY3lsjldfMjIzMw.H83JNf2228fze5OF-Vn4oSF4SqF4Wgm6PnZTK01alsjfvrQpLTZSN4ekA")){
                    return true;
                }else {
                    throw new BusinessException(400, "请传入正确的上传令牌");
                }
            }
        }
    }
    /**
     * 接口访问结束后，从ThreadLocal中删除用户信息
     *
     * @param response
     * @param handler
     * @param ex
     * @throws Exception
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception
            ex)  {
    }
}
