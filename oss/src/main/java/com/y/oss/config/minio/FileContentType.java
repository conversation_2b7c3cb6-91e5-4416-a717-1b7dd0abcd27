package com.y.oss.config.minio;

import cn.hutool.core.util.StrUtil;

public enum FileContentType {
    DEFAULT("default","application/octet-stream"),
    /**
     * 视频格式
     */
    PDF("pdf", "application/pdf"),
    DOC("doc", "application/msword"),
    DOCX("docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"),
    ZIP("zip", "application/zip"),
    XLS("xls", "application/vnd.ms-excel"),
    XLSX("xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),
    RAR("rar", "application/x-rar");
    private String prefix;

    private String type;

    public static String getContentType(String prefix){
        if(StrUtil.isEmpty(prefix)){
            return null;
        }
        prefix = prefix.substring(prefix.lastIndexOf(".") + 1);
        for (FileContentType value : FileContentType.values()) {
            if(prefix.equalsIgnoreCase(value.getPrefix())){
                return value.getType();
            }
        }
        return null;
    }

    FileContentType(String prefix, String type) {
        this.prefix = prefix;
        this.type = type;
    }

    public String getPrefix() {
        return prefix;
    }

    public String getType() {
        return type;
    }
}
