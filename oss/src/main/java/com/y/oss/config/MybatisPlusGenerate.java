package com.y.oss.config;


import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.InjectionConfig;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.builder.ConfigBuilder;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.FileType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * mybatisplus生成器
 */
public class MybatisPlusGenerate {

    //微服务工程名
    private static final String PROJECT_NAME = "oss";
    //生成到xxx-service module下面
    private static final String MODULE_NAME = "oss\\";
    //作者
    private static final String AUTHOR_NAME = "ye";

    //数据库名字
    //private static final String DATABASE_NAME = "pcwp_" + PROJECT_NAME;
    private static final String DATABASE_NAME = "mall";
    //数据库IP
    private static final String DATABASE_IP = "127.0.0.1"; //测试服务器
    //    private static final String DATABASE_IP = "localhost";
    //数据库端口
    private static final String DATABASE_PORT = "3306";

    //数据库账号
    private static final String DATABASE_USERNAME = "root";
    //数据库密码
    private static final String DATABASE_PWD = "root";


    public static void main(String[] args) {
        // 代码生成器
        AutoGenerator mpg = new AutoGenerator();

        // 全局配置
        GlobalConfig gc = new GlobalConfig();

        // 设置日期转义 生成Date
        gc.setDateType(DateType.ONLY_DATE);
        final String projectPath = System.getProperty("user.dir");
        // 输出路径
        gc.setOutputDir(projectPath + "/" + MODULE_NAME + "/src/main/java");
        //* gc.setOutputDir("D:\\cloud-demo\\user-service\\src\\main\\java");*//*
        gc.setAuthor(AUTHOR_NAME);
        gc.setOpen(false);
        // 是否覆盖原来的（！！！！！！！！谨慎使用，一般debug用！！！！！）
//        gc.setFileOverride(false);
        gc.setFileOverride(true);
        // 去掉接口上的I
        gc.setServiceName("%sService");
        mpg.setGlobalConfig(gc);

        // 数据源配置
        DataSourceConfig dsc = new DataSourceConfig();
        dsc.setUrl("jdbc:mysql://" + DATABASE_IP + ":" + DATABASE_PORT + "/" + DATABASE_NAME + "?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai");
        dsc.setDriverName("com.mysql.cj.jdbc.Driver");
        dsc.setUsername(DATABASE_USERNAME);
        dsc.setPassword(DATABASE_PWD);
        mpg.setDataSource(dsc);

        // 包配置,生成路径(会覆盖)
        PackageConfig pc = new PackageConfig();
        mpg.setPackageInfo(pc);
        // 包名
//        pc.setParent("scrbg.meplat." + PROJECT_NAME);
        pc.setParent("com.y." + PROJECT_NAME);
        pc.setEntity("entity");
        pc.setService("service");
        pc.setServiceImpl("service.impl");
        //设置controller不生成
        pc.setController("controller");
        // 自定义配置
        InjectionConfig cfg = new InjectionConfig() {
            @Override
            public void initMap() {
                // to do nothing
            }
        };

        //只有mapper和entity可以覆盖
        cfg.setFileCreate(new IFileCreate() {
            @Override
            public boolean isCreate(ConfigBuilder configBuilder, FileType fileType, String filePath) {
                // 判断自定义文件夹是否需要创建,这里调用默认的方法
                checkDir(filePath);
                //对于已存在的文件，只需重复生成 entity 和 mapper.xml
                File file = new File(filePath);
                boolean exist = file.exists();
                if (exist) {
                    return filePath.endsWith("Mapper.xml") || FileType.ENTITY == fileType;
                }
                //不存在的文件都需要创建
                return true;
            }
        });

        // 配置模板
        TemplateConfig templateConfig = new TemplateConfig();
        //templateConfig.setEntity("templates/entity.dao.java");
        // 设置xml不生成 后面调整路径后生成
        templateConfig.setXml(null);


        mpg.setTemplate(templateConfig);


        // 调整 xml 生成目录演示
        List<FileOutConfig> focList = new ArrayList<FileOutConfig>();
        focList.add(new FileOutConfig("/templates/mapper.xml.vm") {
            @Override
            public String outputFile(TableInfo tableInfo) {
                return projectPath + "/" + MODULE_NAME + "/src/main/resources/mapper/" + tableInfo.getEntityName() + "Mapper.xml";
            }
        });
        cfg.setFileOutConfigList(focList);

        mpg.setCfg(cfg);

        // 策略配置
        StrategyConfig strategy = new StrategyConfig();
        //去表头
        strategy.setTablePrefix(PROJECT_NAME + "_");
        strategy.setInclude();
        strategy.setNaming(NamingStrategy.underline_to_camel);
        strategy.setColumnNaming(NamingStrategy.underline_to_camel);
        strategy.setEntityLombokModel(true);
        //需要生成的表   不设置此参数表示默认生成所有表
        strategy.setInclude("file_record");
//        strategy.setInclude(new String[]{"shop","product_inventory","user","user_address","product_attribute_value","product","product_attribute","sku_sale_attribute_value","product_sku","order_item","orders","shopping_cart","product_comment","product_category","product_medium","","","","","","","","","","","",""});
//        strategy.setInclude("shop", "product_inventory", "user", "user_address", "product_attribute_value", "product", "product_attribute", "sku_sale_attribute_value", "product_sku", "order_item", "orders", "shopping_cart", "product_comment", "product_category", "product_medium");
//        strategy.setInclude("tender","tender_dtl","tender_enroll_achievement_history"
//        ,"tender_enroll_certificate","tender_enroll_package","tender_notice","tender_notice_content"
//        ,"tender_package","tender_package_subcontractor","tender_result","tender_result_candidate");
        mpg.setStrategy(strategy);
        mpg.execute();
    }
}
