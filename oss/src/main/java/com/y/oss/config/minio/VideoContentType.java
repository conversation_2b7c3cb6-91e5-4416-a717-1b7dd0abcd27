package com.y.oss.config.minio;

import cn.hutool.core.util.StrUtil;

public enum VideoContentType {
    DEFAULT("default","application/octet-stream"),
    /**
     * 视频格式
     */
    MAP4("mp4", "video/mp4"),
    MOV("mov", "video/quicktime"),
    AVI("avi", "video/x-msvideo"),
    FIV("fiv", "video/x-flv"),
    WEBM("webm", "video/webm");

    private String prefix;

    private String type;

    public static String getContentType(String prefix){
        if(StrUtil.isEmpty(prefix)){
            return null;
        }
        prefix = prefix.substring(prefix.lastIndexOf(".") + 1);
        for (VideoContentType value : VideoContentType.values()) {
            if(prefix.equalsIgnoreCase(value.getPrefix())){
                return value.getType();
            }
            if(prefix.equalsIgnoreCase("3gp")){
                return "video/3gpp";
            }
        }
        return null;
    }

    VideoContentType(String prefix, String type) {
        this.prefix = prefix;
        this.type = type;
    }

    public String getPrefix() {
        return prefix;
    }

    public String getType() {
        return type;
    }
}
