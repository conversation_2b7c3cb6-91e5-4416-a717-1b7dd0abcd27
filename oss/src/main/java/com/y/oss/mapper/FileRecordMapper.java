package com.y.oss.mapper;

import com.y.oss.entity.FileRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;

/**
 * <p>
 * 文件上传记录信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-27
 */
public interface FileRecordMapper extends BaseMapper<FileRecord> {

    @Delete("DELETE FROM file_record WHERE record_id=#{id}")
    void deleteByRecordId(String id);


}
