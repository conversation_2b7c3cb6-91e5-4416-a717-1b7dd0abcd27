package com.y.oss;

import com.y.oss.adapter.ApplicationConfig;
import com.y.oss.config.MallConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Import;

import javax.annotation.PostConstruct;

@SpringBootApplication
@MapperScan("com.y.oss.mapper")
@Import(value = {ApplicationConfig.class})
public class OssApplication {
    @Autowired
    public MallConfig mallConfig;
    @PostConstruct
    private void init(){
            System.out.println("系统环境：" + mallConfig.profiles);
            System.out.println("                     =================================================");

    }
    public static void main(String[] args) {
        SpringApplication.run(OssApplication.class, args);
    }

}
