package com.y.oss.service.impl;

import com.y.oss.entity.FileRecord;
import com.y.oss.mapper.FileRecordMapper;
import com.y.oss.service.FileRecordService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;

/**
 * @描述：文件上传记录信息 服务类
 * @作者: ye
 * @日期: 2023-03-27
 */
@Service
public class FileRecordServiceImpl extends ServiceImpl<FileRecordMapper, FileRecord> implements FileRecordService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<FileRecord> queryWrapper) {
        IPage<FileRecord> page = this.page(
        new Query<FileRecord>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(FileRecord fileRecord) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(fileRecord);
    }

    @Override
    public void update(FileRecord fileRecord) {
        super.updateById(fileRecord);
    }


    @Override
    public FileRecord getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        baseMapper.deleteByRecordId(id);
    }

}