package com.y.oss.util;

import com.scrbg.common.utils.IErrorCode;
import com.scrbg.common.utils.ResultCode;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

public class R<T> implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("返回状态码")
    private Integer code;
    @ApiModelProperty("返回消息")
    private String message;
    @ApiModelProperty("返回对象")
    private T data;

    public R() {
    }

    public R(Integer code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public static <T> R<T> success(T data) {
        return new R(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), data);
    }

    public static <T> R<T> success() {
        return new R(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), (Object)null);
    }

    public static <T> R<T> success(T data, String message) {
        return new R(ResultCode.SUCCESS.getCode(), message, data);
    }

    public static <T> R<T> failed(IErrorCode errorCode) {
        return new R(errorCode.getCode(), errorCode.getMessage(), (Object)null);
    }

    public static <T> R<T> failed(IErrorCode errorCode, String message) {
        return new R(errorCode.getCode(), message, (Object)null);
    }

    private static <T> R<T> failed(ResultCode validateFailed, T data) {
        return new R(validateFailed.getCode(), validateFailed.getMessage(), data);
    }

    public static <T> R<T> failed(Integer errorCode, String message) {
        return new R(errorCode, message, (Object)null);
    }

    public static <T> R<T> failed(String message) {
        return new R(ResultCode.FAILED.getCode(), message, (Object)null);
    }

    public static <T> R<T> failed() {
        return new R(ResultCode.FAILED.getCode(), ResultCode.FAILED.getMessage(), (Object)null);
    }

    public static <T> R<T> validateFailed() {
        return failed((IErrorCode)ResultCode.VALIDATE_FAILED);
    }

    public static <T> R<T> validateFailed(T data) {
        return failed(ResultCode.VALIDATE_FAILED, data);
    }

    public static <T> R<T> validateFailed(String message) {
        return new R(ResultCode.VALIDATE_FAILED.getCode(), message, (Object)null);
    }

    public static <T> R<T> unauthorized(T data) {
        return new R(ResultCode.UNAUTHORIZED.getCode(), ResultCode.UNAUTHORIZED.getMessage(), data);
    }

    public static <T> R<T> forbidden(T data) {
        return new R(ResultCode.FORBIDDEN.getCode(), ResultCode.FORBIDDEN.getMessage(), data);
    }

    public Integer getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }

    public T getData() {
        return this.data;
    }

    public void setCode(final Integer code) {
        this.code = code;
    }

    public void setMessage(final String message) {
        this.message = message;
    }

    public void setData(final T data) {
        this.data = data;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof R)) {
            return false;
        } else {
            R<?> other = (R)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                label47: {
                    Object this$code = this.getCode();
                    Object other$code = other.getCode();
                    if (this$code == null) {
                        if (other$code == null) {
                            break label47;
                        }
                    } else if (this$code.equals(other$code)) {
                        break label47;
                    }

                    return false;
                }

                Object this$message = this.getMessage();
                Object other$message = other.getMessage();
                if (this$message == null) {
                    if (other$message != null) {
                        return false;
                    }
                } else if (!this$message.equals(other$message)) {
                    return false;
                }

                Object this$data = this.getData();
                Object other$data = other.getData();
                if (this$data == null) {
                    if (other$data != null) {
                        return false;
                    }
                } else if (!this$data.equals(other$data)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(final Object other) {
        return other instanceof R;
    }

    public int hashCode() {
        int result = 1;
        Object $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        Object $message = this.getMessage();
        result = result * 59 + ($message == null ? 43 : $message.hashCode());
        Object $data = this.getData();
        result = result * 59 + ($data == null ? 43 : $data.hashCode());
        return result;
    }

    public String toString() {
        return "R(code=" + this.getCode() + ", message=" + this.getMessage() + ", data=" + this.getData() + ")";
    }
}
