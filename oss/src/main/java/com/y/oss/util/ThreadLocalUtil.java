//package com.y.oss.util;
//
///**
// * <AUTHOR>
// * @create 2022-12-14 17:17
// */
//public class ThreadLocalUtil {
//
//        /**
//         * 保存用户对象的ThreadLocal  在拦截器操作 添加、删除相关用户数据
//         */
//        private static final ThreadLocal<UserLogin> userThreadLocal = new ThreadLocal<UserLogin>();
//
//        /**
//         * 添加当前登录用户方法  在拦截器方法执行前调用设置获取用户
//         * @param user
//         */
//        public static void addCurrentUser(UserLogin user){
//            userThreadLocal.set(user);
//        }
//
//        /**
//         * 获取当前登录用户方法
//         */
//        public static UserLogin getCurrentUser(){
//            UserLogin userLogin = userThreadLocal.get();
//            return userLogin;
//        }
//
//
//        /**
//         * 删除当前登录用户方法  在拦截器方法执行后 移除当前用户对象
//         */
//        public static void remove(){
//            userThreadLocal.remove();
//        }
//
//
//}
