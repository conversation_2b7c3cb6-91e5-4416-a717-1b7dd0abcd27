//package com.y.oss.util;
//
//import lombok.Data;
//
//import java.util.List;
//import java.util.Map;
//
///**
// * <AUTHOR>
// * @create 2022-12-14 17:20
// */
//@Data
//public class UserLogin {
//
//    /**
//     * 用户id
//     */
//    private String userId;
//
//
//    /**
//     * 用户名称
//     */
//    private String userName;
//
//    /**
//     * 店铺id
//     */
//    private String shopId;
//
//    /**
//     * 店铺名称
//     */
//    private String shopName;
//
//    /**
//     * 企业id
//     */
//    private String enterpriseId;
//
//    /**
//     * 远程企业id
//     */
//    private String orgId;
//
//    /**
//     * 远程用户id
//     */
//    private String FarUserId;
//
//    /**
//     * 组织以及子组织id集合
//     */
//    private List<String> orgIds;
//
//    /**
//     * 外部用户的手机号
//     */
//    private String userMobile;
//
//    /**
//     * token
//     */
//    private String token;
//
//    /**
//     * 是否是内部用户（0否1是）
//     */
//    private Integer isInterior;
//
//    /**
//     * 企业名称
//     */
//    private String enterpriseName;
//
//    /**
//     * 当前组织完整信息（用户前端接口请求头使用）
//     */
//    private Map orgInfo;
//
//    private Integer enterpriseType;
//
//    private Integer isSupplier;
//
//    private Integer isCheck;
//    private String socialCreditCode;
//
//    private String userNumber;
//}
