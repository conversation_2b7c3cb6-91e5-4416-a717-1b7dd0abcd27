package scrbg.meplat.gateway.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022-12-21 14:36
 */
@Data
public class LoginVO {

    /**
     * 登陆返回
     */
    @ApiModelProperty(value = "本地用户id")
    private String userId;

    @ApiModelProperty(value = "手机号码")
    private String userMobile;

    @ApiModelProperty(value = "远程用户id")
    private String farUserId;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "用户编号")
    private String userNumber;

    @ApiModelProperty(value = "远程token")
    private String token;

    @ApiModelProperty(value = "当前组织统一社会信用代码")
    private String socialCreditCode;

    @ApiModelProperty(value = "是否有电商管理权限（0否1是）")
    private Integer isShpAuthority;

    @ApiModelProperty(value = "是否有二手设备回收运营中心权限（0否1是）")
    private Integer isDeviceRecycle;

    @ApiModelProperty(value = "是否有物流运营中心权限（0否1是）")
    private Integer isMaterialFlow;

    @ApiModelProperty(value = "是否是外部用户（0否1是）")
    private Integer isExternal;

    @ApiModelProperty(value = "是否是内部用户（0否1是）")
    private Integer isInterior;

    /**
     * 本地id、机构id
     */

    @ApiModelProperty(value = "当前本地组织id")
    private String LocalOrgId;
    /**
     * 调用远程查询
     */
    @ApiModelProperty(value = "组织列表")
    private List<OrganizationVO> organizationVOS;

    @ApiModelProperty(value = "当前组织名称")
    private String orgName;

    @ApiModelProperty(value = "当前组织id")
    private String orgId;

    @ApiModelProperty(value = "当前组织以及子组织ids（远程）")
    private List<String> orgIds;

    @ApiModelProperty(value = "是否是供应商（0否1是）")
    private Integer isSupplier;

    @ApiModelProperty(value = "当前组织完整信息（用户前端接口请求头使用）")
    private Map orgInfo;


    /**
     * 判断
     */
//    @ApiModelProperty(value = "是否普通用户（0否1是）")
//    private Integer isOrdinary;

    @ApiModelProperty(value = "是否运营平台管理者（0否1是）")
    private Integer isPlatformAdmin;

//    @ApiModelProperty(value = "是否店铺管理者（0否1是）")
//    private Integer isShopManage;

    @ApiModelProperty(value = "店铺id")
    private String shopId;

    @ApiModelProperty(value = "店铺类型 0：个体户  1：企业  2：个人")
    private Integer shopType;

    @ApiModelProperty(value = "企业类型：0：个体户  1：企业  2：个人")
    private Integer enterpriseType;

    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;

    @ApiModelProperty(value = "是否有招标权限（0否1是）")
    private Integer isTender;

    @ApiModelProperty(value = "是否有纪检权限（0否1是）")
    private Integer isCheck;

    @ApiModelProperty(value = "其他服务权限")
    private Map<String, Integer> isOtherAuth;
}
