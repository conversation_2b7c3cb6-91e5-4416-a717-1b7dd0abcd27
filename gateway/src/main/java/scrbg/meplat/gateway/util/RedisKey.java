package scrbg.meplat.gateway.util;

/**
 * <AUTHOR>
 * @create 2022-12-06 11:17
 */
public class RedisKey {

    /**
     * 分类缓存
     */
    public static final String CATEGORY = "category";

    /**
     * 保存登陆用户信息
     */
    public static final String USER_MAP = "user_";
    public static final String USER_MAP_KEY = "user:id_";

    /**
     * 发送短信验证码
     */
    public static final String LOGIN_CODE_KEY = "login:code:";
    public static final Long LOGIN_CODE_TTL   = 1L;

    /**
     * 注册发送验证码
     */
    public static final String REGISTER_CODE_KEY = "register:code:";
    public static final Long REGISTER_CODE_TTL   = 1L;

    /**
     * 修改密码
     */

    public static final String UPDATE_PASSWORD_CODE_KEY = "updatePassword:code:";
    public static final Long UPDATE_PASSWORD_CODE_TTL   = 1L;

    /**
     * 修改手机号
     */
    public static final String UPDATE_MOBILE_CODE_KEY = "updateMobile:code:";
    public static final Long UPDATE_MOBILE_CODE_TTL   = 1L;

}
