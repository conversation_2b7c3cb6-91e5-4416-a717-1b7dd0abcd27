package scrbg.meplat.mallauth.service;

import scrbg.meplat.mallauth.entity.SysRole;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mallauth.entity.SysRole;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
/**
 * @描述：角色表 服务类
 * @作者: ye
 * @日期: 2023-12-20
 */
public interface SysRoleService extends IService<SysRole> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<SysRole> queryWrapper);

        void create(SysRole sysRole);
        void update(SysRole sysRole);
        SysRole getById(String id);

        void delete(String id);

        void deleteBatch( List<String> ids);

    void saveMenuAndSysRole(SysRole sysRole);

        List<String> getMenuAndSysRoleDateListById(String roleId);

    /**
     * 批量更改排序值
     * @param ids
     */
    void changeSortValue(List<SysRole> ids);
}
