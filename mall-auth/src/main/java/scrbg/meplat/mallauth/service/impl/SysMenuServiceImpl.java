package scrbg.meplat.mallauth.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import scrbg.meplat.mallauth.entity.SysMenu;
import scrbg.meplat.mallauth.entity.SysMenuRole;
import scrbg.meplat.mallauth.entity.SysRole;
import scrbg.meplat.mallauth.entity.parent.MustBaseEntity;
import scrbg.meplat.mallauth.exception.BusinessException;
import scrbg.meplat.mallauth.mapper.SysMenuMapper;
import scrbg.meplat.mallauth.service.SysMenuRoleService;
import scrbg.meplat.mallauth.service.SysMenuService;
import scrbg.meplat.mallauth.config.redis.redisson.NotResubmit;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Service;

import scrbg.meplat.mallauth.vo.ChangStateVo;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @描述：菜单表 服务类
 * @作者: ye
 * @日期: 2023-12-20
 */
@Service
public class SysMenuServiceImpl extends ServiceImpl<SysMenuMapper, SysMenu> implements SysMenuService {


    @Autowired
    private SysMenuRoleService sysMenuRoleService;

    @Override

    public List<SysMenu> queryPage(JSONObject jsonObject, LambdaQueryWrapper<SysMenu> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        Integer state = (Integer) innerMap.get("state");
        String title = (String) innerMap.get("title");
        String code = (String) innerMap.get("code");
        String authLabel = (String) innerMap.get("authLabel");
        String type = (String) innerMap.get("type");
        Integer classCode = (Integer) innerMap.get("classCode");
        Integer level = (Integer) innerMap.get("level");

        queryWrapper.eq(SysMenu::getClassCode, classCode);
        if (org.apache.commons.lang.StringUtils.isNotBlank(keywords)) {
            queryWrapper.and((t) -> {
                t.like(SysMenu::getTitle, keywords)
                        .or()
                        .like(SysMenu::getCode, keywords).or()
                        .like(SysMenu::getAuthLabel, keywords)
                        .or()
                        .like(SysMenu::getPathUrl, keywords);

            });
        }

        if (!StringUtils.isEmpty(authLabel)) {
            queryWrapper.eq(SysMenu::getAuthLabel, authLabel);
        }
        if (!StringUtils.isEmpty(title)) {
            queryWrapper.eq(SysMenu::getTitle, title);
        }
        if (!StringUtils.isEmpty(code)) {
            queryWrapper.eq(SysMenu::getCode, code);
        }
        queryWrapper.orderByDesc(MustBaseEntity::getSort,MustBaseEntity::getGmtCreate);
        queryWrapper.eq(type != null, SysMenu::getType, type);
        queryWrapper.eq(level != null, SysMenu::getLevel, level);
        queryWrapper.eq(state != null, SysMenu::getState, state);
        List<SysMenu> list = list(queryWrapper);
        return getChildrens(list);
    }


    private List<SysMenu> getChildrens(List<SysMenu> entities) {
        List<SysMenu> level1Menus = new ArrayList<>();
        if (entities.size() > 0) {
            level1Menus = entities.stream().
                    filter(entity -> StringUtils.isEmpty(entity.getParentMenuId()) || "0".equals(entity.getParentMenuId()))
                    .map(entity -> {
                        entity.setChildren(getChildren(entity, entities));
                        return entity;
                    })
                    .sorted((entity1, entity2) ->
                            (entity2.getSort() == null ? 0 : entity2.getSort()) -
                                    (entity1.getSort() == null ? 0 : entity1.getSort()))
                    .collect(Collectors.toList());
        }
        return level1Menus;
    }


    private List<SysMenu> getChildren(SysMenu root, List<SysMenu> all) {
        List<SysMenu> children = all.stream().
                filter(entity -> root.getMenuId().equals(entity.getParentMenuId()))
                .map(entity -> {
                    entity.setChildren(getChildren(entity, all));
                    return entity;
                })
                .sorted((entity1, entity2) ->
                        (entity2.getSort() == null ? 0 : entity2.getSort()) -
                                (entity1.getSort() == null ? 0 : entity1.getSort()))
                .collect(Collectors.toList());
        return children;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void create(SysMenu sysMenu) {
        if (StringUtils.isEmpty(sysMenu.getTitle())) {
            throw new BusinessException(500, "菜单名称不能为空！");
        }
        if (StringUtils.isEmpty(sysMenu.getClassCode())) {
            throw new BusinessException(500, "所属平台不能为空！");
        }
        LambdaQueryChainWrapper<SysMenu> queryChainWrapper = lambdaQuery()
                .eq(SysMenu::getTitle, sysMenu.getTitle())
                .eq(SysMenu::getCode, sysMenu.getCode())
                .eq(SysMenu::getClassCode, sysMenu.getClassCode())
                .eq(SysMenu::getType, sysMenu.getType());
        if (sysMenu.getParentMenuId() == null) {
            queryChainWrapper.isNull(SysMenu::getParentMenuId);
        } else {
            queryChainWrapper.eq(SysMenu::getTitle, sysMenu.getTitle());
        }
        Integer count = queryChainWrapper.count();
        if (count == 0) {
            sysMenu.setMallType(0);
            super.save(sysMenu);
        } else {
            throw new BusinessException(500, "菜单资源重复！");
        }
        //调用父类方法即可
        //也可以baseMapper.insert

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void update(SysMenu sysMenu) {
        SysMenu one = lambdaQuery().eq(SysMenu::getClassCode, sysMenu.getClassCode())
                .eq(SysMenu::getTitle, sysMenu.getTitle())
                .eq(SysMenu::getLevel, sysMenu.getLevel())
                .eq(SysMenu::getType, sysMenu.getType())
                .eq(sysMenu.getParentMenuId() != null, SysMenu::getParentMenuId, sysMenu.getParentMenuId()).one();
        if (one != null) {
            if (one.getMenuId().equals(sysMenu.getMenuId())) {
                updateById(sysMenu);
            } else {
                throw new BusinessException(500, "菜单" + one.getTitle() + "已经存在，不需要重复添加");
            }
        } else {
            SysMenu byId = getById(sysMenu.getMenuId());
            if (byId.getLevel() == sysMenu.getLevel()) {
                updateById(sysMenu);
            } else {
                SysMenu menu = getChildrenById(byId);
                ArrayList<SysMenu> sysMenus = new ArrayList<>();
                int level = sysMenu.getLevel();
                List<SysMenu> children = menu.getChildren();
                if (!children.isEmpty()) {
                    for (SysMenu child : children) {
                        child.setLevel(level + 1);
                        sysMenus.add(child);
                    }
                }
                updateBatchById(sysMenus);
            }
        }
        super.updateById(sysMenu);
    }

    private SysMenu getChildrenById(SysMenu menu) {

        List<SysMenu> list = lambdaQuery().eq(SysMenu::getParentMenuId, menu.getMenuId()).list();
        if (!list.isEmpty()) {
            menu.setChildren(list);
            for (SysMenu sysMenu : list) {
                getChildrenById(sysMenu);
            }
            return menu;
        }
        return menu;
    }


    @Override
    public SysMenu getById(String id) {
        return super.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void delete(String id) {
        SysMenu byId = getById(id);
        ArrayList<String> removeList = new ArrayList<>();
        if (byId != null) {
            List<SysMenu> list = lambdaQuery().in(SysMenu::getParentMenuId, byId.getMenuId()).list();
            if (!list.isEmpty()) {
                for (SysMenu sysMenu : list) {
                    if (sysMenu.getState() == 1) {
                        throw new BusinessException(500, sysMenu.getTitle() + "还在显示，不能删除");
                    } else {
                        Integer count = sysMenuRoleService.lambdaQuery()
                                .eq(SysMenuRole::getMenuId, sysMenu.getMenuId())
                                .select(SysMenuRole::getRoleId).count();
                        if (count > 1) {
                            throw new BusinessException(500, sysMenu.getTitle() + "还在角色使用，不能删除");
                        } else {
                            removeList.add(sysMenu.getMenuId());
                            List<SysMenu> list2 = lambdaQuery().in(SysMenu::getParentMenuId, sysMenu.getMenuId()).list();
                            if (list2 != null && list2.size() > 0) {
                                for (SysMenu sysMenu2 : list2) {
                                    if (sysMenu2.getState() == 1) {
                                        throw new BusinessException(500, sysMenu2.getTitle() + "还在显示，不能删除");
                                    } else {
                                        Integer count2 = sysMenuRoleService.lambdaQuery()
                                                .eq(SysMenuRole::getMenuId, sysMenu2.getMenuId())
                                                .select(SysMenuRole::getRoleId).count();
                                        if (count2 > 1) {
                                            throw new BusinessException(500, sysMenu.getTitle() + "还在角色使用，不能删除");
                                        } else {
                                            removeList.add(sysMenu2.getMenuId());
                                        }
                                    }
                                }
                            }
                        }
                    }


                }
            }
        }
        removeList.add(id);
        super.removeByIds(removeList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void deleteBatch(List<String> ids) {
        super.removeByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void updateState(ChangStateVo changStateVo) {
        SysMenu byId = getById(changStateVo.getId());
        ArrayList<SysMenu> updateStateList = new ArrayList<>();
        if (changStateVo.getState() == 0) {
            byId.setState(0);
            List<SysMenu> sysMenus = updateChiledState(byId.getMenuId(), updateStateList);
            sysMenus.add(byId);
            updateBatchById(sysMenus);
        } else if (changStateVo.getState() == 1) {
            byId.setState(1);
            List<SysMenu> sysMenus = openState(byId, updateStateList);
            sysMenus.add(byId);
            updateBatchById(sysMenus);
        }

    }


    @Override
    public List<SysMenu> selectParentMenus(JSONObject jsonObject, LambdaQueryWrapper<SysMenu> sysMenuLambdaQueryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
//        innerMap.get()
        return null;
    }


    @Override
    public void updateShowDev(ChangStateVo changStateVo) {
        SysMenu byId = getById(changStateVo.getId());
        byId.setShowDev(changStateVo.getState());
        updateById(byId);
    }

    private List<SysMenu> openState(SysMenu byId, ArrayList<SysMenu> updateStateList) {
        if (byId.getParentMenuId() != null) {
            SysMenu parentMenu = getById(byId.getParentMenuId());
            if (parentMenu != null) {
                if (parentMenu.getState() == 0) {
                    parentMenu.setState(1);
                    updateStateList.add(parentMenu);
                    openState(parentMenu, updateStateList);
                }
            }
            return updateStateList;
        }
        return updateStateList;
    }

    private List<SysMenu> updateChiledState(String byId, ArrayList<SysMenu> updateStateList) {
        List<SysMenu> sysMenuList = lambdaQuery()
                .eq(SysMenu::getParentMenuId, byId)
                .list();
        if (!sysMenuList.isEmpty()) {
            for (SysMenu sysMenu : sysMenuList) {
                if (sysMenu.getState() == 1) {
                    sysMenu.setState(0);
                }
                updateStateList.add(sysMenu);
                updateChiledState(sysMenu.getMenuId(), updateStateList);
            }
        }
        return updateStateList;
    }


    @Override
    public void changeSortValue(List<SysMenu> ids) {
        List<SysMenu> SysMenus = new ArrayList<>();
        if (ids!=null&&ids.size()>0){
            for (SysMenu id : ids) {
                SysMenu byId = getById(id);
                byId.setSort(id.getSort());
                SysMenus.add(byId);
            }
            updateBatchById(SysMenus);
        }
    }
}
