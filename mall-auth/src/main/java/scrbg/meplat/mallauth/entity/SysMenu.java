package scrbg.meplat.mallauth.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mallauth.entity.parent.MustBaseEntity;
/**
 * @描述：菜单表
 * @作者: ye
 * @日期: 2023-12-20
 */
@ApiModel(value="菜单表")
@Data
@TableName("sys_menu")
public class SysMenu extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "菜单id")
    private String menuId;

    @ApiModelProperty(value = "菜单编号")

    private String code;


    @ApiModelProperty(value = "菜单名称")

    private String title;


    @ApiModelProperty(value = "菜单类型（1菜单2按钮3资源）")

    private String type;


    @ApiModelProperty(value = "权限标识（可选）")

    private String authLabel;


    @ApiModelProperty(value = "菜单图标")

    private String icon;


    @ApiModelProperty(value = "路由地址")

    private String pathUrl;


    @ApiModelProperty(value = "所属平台（1后台管理平台2供应商管理平台3履约管理 4 自营店供应商）")

    private Integer classCode;


    @ApiModelProperty(value = "父级菜单id（1级为null）")

    private String parentMenuId;


    @ApiModelProperty(value = "菜单层级（0子系统 1：一级，2：二级，3：二级）")

    private Integer level;




    @ApiModelProperty(value = "状态（是否显示（0否1是））")

    private Integer state;





    @ApiModelProperty(value = "是否只测试环境展示（0否1是）")
    private Integer showDev;




    @ApiModelProperty(value = "是否公开（0否1是）")

    private Integer isOpen;

    @ApiModelProperty(value = "子节点")
    @TableField(exist = false)
    private List<SysMenu> children;



}