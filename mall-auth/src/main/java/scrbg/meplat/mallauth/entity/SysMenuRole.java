package scrbg.meplat.mallauth.entity;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mallauth.entity.parent.MustBaseEntity;
/**
 * @描述：菜单角色表
 * @作者: ye
 * @日期: 2023-12-20
 */
@ApiModel(value="菜单角色表")
@Data
@TableName("sys_menu_role")
public class SysMenuRole  implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "菜单角色表id")
    private String sysMenuRoleId;

    @ApiModelProperty(value = "菜单id")

    private String menuId;


    @ApiModelProperty(value = "角色id")

    private String roleId;





}